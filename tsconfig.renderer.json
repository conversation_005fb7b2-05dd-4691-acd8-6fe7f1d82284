{"extends": "./tsconfig.json", "compilerOptions": {"target": "ES2022", "lib": ["ES2022", "DOM", "DOM.Iterable"], "allowJs": true, "skipLibCheck": true, "esModuleInterop": true, "allowSyntheticDefaultImports": true, "strict": true, "forceConsistentCasingInFileNames": true, "noFallthroughCasesInSwitch": true, "module": "ESNext", "moduleResolution": "bundler", "resolveJsonModule": true, "isolatedModules": true, "noEmit": true, "jsx": "react-jsx", "jsxImportSource": "react", "outDir": "dist/renderer", "rootDir": "src/renderer", "baseUrl": ".", "paths": {"@/*": ["src/*"], "@renderer/*": ["src/renderer/*"], "@shared/*": ["src/shared/*"], "@components/*": ["src/renderer/components/*"], "@stores/*": ["src/renderer/stores/*"], "@services/*": ["src/renderer/services/*"], "@styles/*": ["src/renderer/styles/*"]}}, "include": ["src/renderer/**/*", "src/shared/**/*"], "exclude": ["src/main", "src/preload", "node_modules", "**/*.test.ts", "**/*.test.tsx"]}