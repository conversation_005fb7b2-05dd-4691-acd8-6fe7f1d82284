import '@testing-library/jest-dom';

// Mock Electron APIs
global.window = Object.create(window);
Object.defineProperty(window, 'electronAPI', {
  value: {
    system: {
      getInfo: jest.fn().mockResolvedValue({
        platform: 'test',
        arch: 'x64',
        version: '1.0.0',
        electronVersion: '32.0.1',
        nodeVersion: '20.0.0',
      }),
    },
    db: {
      query: jest.fn(),
      transaction: jest.fn(),
    },
    security: {
      encrypt: jest.fn(),
      decrypt: jest.fn(),
    },
    mcp: {
      sync: jest.fn(),
    },
    app: {
      quit: jest.fn(),
      minimize: jest.fn(),
      maximize: jest.fn(),
      close: jest.fn(),
    },
  },
  writable: true,
});

// Mock framer-motion
jest.mock('framer-motion', () => ({
  motion: {
    div: 'div',
    button: 'button',
    span: 'span',
    h1: 'h1',
    h2: 'h2',
    h3: 'h3',
    p: 'p',
    ul: 'ul',
    li: 'li',
    form: 'form',
    input: 'input',
    textarea: 'textarea',
  },
  AnimatePresence: ({ children }: { children: React.ReactNode }) => children,
}));

// Mock Zustand stores (when they are implemented)
// jest.mock('./renderer/stores/todoStore', () => ({
//   useTodoStore: () => ({
//     todos: [],
//     loading: false,
//     error: null,
//     addTodo: jest.fn(),
//     updateTodo: jest.fn(),
//     deleteTodo: jest.fn(),
//     toggleTodo: jest.fn(),
//     fetchTodos: jest.fn(),
//   }),
// }));

// jest.mock('./renderer/stores/authStore', () => ({
//   useAuthStore: () => ({
//     user: null,
//     isAuthenticated: false,
//     loading: false,
//     error: null,
//     login: jest.fn(),
//     logout: jest.fn(),
//     register: jest.fn(),
//   }),
// }));
