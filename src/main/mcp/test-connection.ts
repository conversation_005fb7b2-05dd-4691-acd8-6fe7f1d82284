import { mcpService } from './service';
import { config } from '@main/utils/config';

interface MCPTestResult {
  name: string;
  status: 'PASS' | 'FAIL' | 'WARN';
  message: string;
  details?: any;
  timestamp: Date;
}

class MCPConnectionTester {
  private results: MCPTestResult[] = [];

  private addResult(name: string, status: 'PASS' | 'FAIL' | 'WARN', message: string, details?: any) {
    const result: MCPTestResult = {
      name,
      status,
      message,
      details,
      timestamp: new Date()
    };
    this.results.push(result);
    
    const icon = status === 'PASS' ? '✅' : status === 'FAIL' ? '❌' : '⚠️';
    console.log(`${icon} ${name}: ${message}`);
    if (details) {
      console.log(`   Details:`, details);
    }
  }

  async runAllTests(): Promise<void> {
    console.log('🔗 MCP Service Connection Status Check');
    console.log('=====================================\n');

    try {
      await this.testConfiguration();
      await this.testServiceInitialization();
      await this.testConnectionStatus();
      await this.testHealthCheck();
      await this.testMockOperations();
      await this.testReconnection();
      await this.testDisconnection();
      
      this.printSummary();
    } catch (error) {
      console.error('❌ MCP test suite failed:', error);
      this.addResult('Test Suite', 'FAIL', `Critical error: ${error instanceof Error ? error.message : 'Unknown error'}`);
    }
  }

  private async testConfiguration(): Promise<void> {
    console.log('1. Testing MCP Configuration...');
    
    try {
      // Test if MOTHERDUCK_TOKEN is available
      const motherduckToken = process.env.MOTHERDUCK_TOKEN;
      if (!motherduckToken) {
        this.addResult(
          'MotherDuck Token', 
          'WARN', 
          'MOTHERDUCK_TOKEN not set - using mock mode',
          { note: 'Cloud sync features will be disabled' }
        );
      } else {
        this.addResult(
          'MotherDuck Token', 
          'PASS', 
          'Token configured',
          { tokenLength: motherduckToken.length }
        );
      }

      // Test MCP configuration retrieval
      try {
        const mcpConfig = config.getMCPConfig();
        this.addResult(
          'MCP Configuration', 
          'PASS', 
          'Configuration loaded successfully',
          {
            databaseName: mcpConfig.databaseName,
            retryAttempts: mcpConfig.retryAttempts,
            retryDelay: mcpConfig.retryDelay,
            enableMetrics: mcpConfig.enableMetrics
          }
        );
      } catch (configError) {
        this.addResult(
          'MCP Configuration', 
          'WARN', 
          'Using fallback configuration',
          { error: configError instanceof Error ? configError.message : 'Unknown error' }
        );
      }

    } catch (error) {
      this.addResult(
        'Configuration Test', 
        'FAIL', 
        `Configuration test failed: ${error instanceof Error ? error.message : 'Unknown error'}`
      );
    }
  }

  private async testServiceInitialization(): Promise<void> {
    console.log('\n2. Testing MCP Service Initialization...');
    
    try {
      await mcpService.initialize();
      this.addResult(
        'Service Initialization', 
        'PASS', 
        'MCP service initialized successfully'
      );
    } catch (error) {
      this.addResult(
        'Service Initialization', 
        'WARN', 
        'Service initialization failed (expected in mock mode)',
        { error: error instanceof Error ? error.message : 'Unknown error' }
      );
    }
  }

  private async testConnectionStatus(): Promise<void> {
    console.log('\n3. Testing Connection Status...');
    
    try {
      const connectionStatus = mcpService.getConnectionStatus();
      const isConnected = mcpService.isConnected();
      
      this.addResult(
        'Connection Status Check', 
        isConnected ? 'PASS' : 'WARN', 
        `Connection status: ${isConnected ? 'Connected' : 'Disconnected'}`,
        {
          isConnected: connectionStatus.isConnected,
          connectionAttempts: connectionStatus.connectionAttempts,
          lastConnectionAt: connectionStatus.lastConnectionAt,
          lastError: connectionStatus.lastError
        }
      );
    } catch (error) {
      this.addResult(
        'Connection Status Check', 
        'FAIL', 
        `Failed to get connection status: ${error instanceof Error ? error.message : 'Unknown error'}`
      );
    }
  }

  private async testHealthCheck(): Promise<void> {
    console.log('\n4. Testing Health Check...');
    
    try {
      const isHealthy = await mcpService.healthCheck();
      this.addResult(
        'Health Check', 
        isHealthy ? 'PASS' : 'WARN', 
        `Health check: ${isHealthy ? 'Healthy' : 'Unhealthy'}`,
        { healthStatus: isHealthy }
      );
    } catch (error) {
      this.addResult(
        'Health Check', 
        'FAIL', 
        `Health check failed: ${error instanceof Error ? error.message : 'Unknown error'}`
      );
    }
  }

  private async testMockOperations(): Promise<void> {
    console.log('\n5. Testing Mock Operations...');
    
    const operations = [
      {
        name: 'Execute Query',
        operation: () => mcpService.executeQuery('SELECT 1 as test', [])
      },
      {
        name: 'Create Table',
        operation: () => mcpService.createTable({ name: 'test_table', columns: [] })
      },
      {
        name: 'Insert Data',
        operation: () => mcpService.insertData('test_table', [{ id: 1, name: 'test' }])
      },
      {
        name: 'Update Data',
        operation: () => mcpService.updateData('test_table', { name: 'updated' }, { id: 1 })
      },
      {
        name: 'Delete Data',
        operation: () => mcpService.deleteData('test_table', { id: 1 })
      }
    ];

    for (const { name, operation } of operations) {
      try {
        const result = await operation();
        this.addResult(
          `Mock ${name}`, 
          'PASS', 
          'Operation completed successfully',
          { result }
        );
      } catch (error) {
        this.addResult(
          `Mock ${name}`, 
          'WARN', 
          `Operation failed (expected in disconnected state)`,
          { error: error instanceof Error ? error.message : 'Unknown error' }
        );
      }
    }
  }

  private async testReconnection(): Promise<void> {
    console.log('\n6. Testing Reconnection...');
    
    try {
      await mcpService.reconnect();
      this.addResult(
        'Reconnection', 
        'PASS', 
        'Reconnection attempt completed'
      );
    } catch (error) {
      this.addResult(
        'Reconnection', 
        'WARN', 
        'Reconnection failed (expected in mock mode)',
        { error: error instanceof Error ? error.message : 'Unknown error' }
      );
    }
  }

  private async testDisconnection(): Promise<void> {
    console.log('\n7. Testing Disconnection...');
    
    try {
      await mcpService.disconnect();
      const isConnected = mcpService.isConnected();
      
      this.addResult(
        'Disconnection', 
        !isConnected ? 'PASS' : 'WARN', 
        `Disconnection ${!isConnected ? 'successful' : 'incomplete'}`,
        { isConnectedAfterDisconnect: isConnected }
      );
    } catch (error) {
      this.addResult(
        'Disconnection', 
        'FAIL', 
        `Disconnection failed: ${error instanceof Error ? error.message : 'Unknown error'}`
      );
    }
  }

  private printSummary(): void {
    console.log('\n📊 MCP Connection Status Summary');
    console.log('=================================');
    
    const passed = this.results.filter(r => r.status === 'PASS').length;
    const warned = this.results.filter(r => r.status === 'WARN').length;
    const failed = this.results.filter(r => r.status === 'FAIL').length;
    const total = this.results.length;
    
    console.log(`Total tests: ${total}`);
    console.log(`✅ Passed: ${passed}`);
    console.log(`⚠️  Warnings: ${warned}`);
    console.log(`❌ Failed: ${failed}`);
    
    if (failed > 0) {
      console.log('\n❌ Failed Tests:');
      this.results.filter(r => r.status === 'FAIL').forEach(result => {
        console.log(`   - ${result.name}: ${result.message}`);
      });
    }
    
    if (warned > 0) {
      console.log('\n⚠️  Warnings:');
      this.results.filter(r => r.status === 'WARN').forEach(result => {
        console.log(`   - ${result.name}: ${result.message}`);
      });
    }
    
    // Overall status assessment
    console.log('\n🔍 Overall Assessment:');
    if (failed === 0 && warned === 0) {
      console.log('✅ MCP service is fully functional');
    } else if (failed === 0) {
      console.log('⚠️  MCP service is functional with warnings (likely in mock mode)');
    } else {
      console.log('❌ MCP service has critical issues');
    }
    
    // Recommendations
    console.log('\n💡 Recommendations:');
    if (!process.env.MOTHERDUCK_TOKEN) {
      console.log('- Set MOTHERDUCK_TOKEN environment variable for cloud sync features');
    }
    if (warned > 0) {
      console.log('- Review warnings for potential configuration improvements');
    }
    if (failed > 0) {
      console.log('- Address failed tests before production deployment');
    }
  }
}

// Run the MCP connection test
async function runMCPConnectionTest() {
  const tester = new MCPConnectionTester();
  await tester.runAllTests();
  process.exit(0);
}

if (require.main === module) {
  runMCPConnectionTest().catch(console.error);
}

export { MCPConnectionTester };
