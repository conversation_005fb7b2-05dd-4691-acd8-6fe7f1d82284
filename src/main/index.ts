import { app, BrowserWindow, ipc<PERSON><PERSON>, Menu } from 'electron';
import { join } from 'path';
import { isDev } from './utils/environment';
import { DatabaseConnection } from './database/connection';
import { CryptographyService } from './auth/crypto.service';
import { MCPService } from './mcp/service';
import log from 'electron-log';

// Configure logging
log.transports.file.level = 'info';
log.transports.console.level = 'debug';

class ModernTodoApp {
  private mainWindow: BrowserWindow | null = null;
  private databaseService: DatabaseConnection | null = null;
  private securityService: CryptographyService | null = null;
  private mcpService: MCPService | null = null;

  constructor() {
    this.setupApp();
    this.setupServices();
    this.setupIpcHandlers();
  }

  private setupApp(): void {
    // Set app user model id for Windows
    app.setAppUserModelId('com.moderntodo.app');

    // Handle app events
    app.whenReady().then(() => {
      this.createMainWindow();
      this.setupMenu();
      
      app.on('activate', () => {
        if (BrowserWindow.getAllWindows().length === 0) {
          this.createMainWindow();
        }
      });
    });

    app.on('window-all-closed', () => {
      if (process.platform !== 'darwin') {
        app.quit();
      }
    });

    app.on('before-quit', async () => {
      await this.cleanup();
    });
  }

  private async setupServices(): Promise<void> {
    try {
      // Initialize core services
      this.securityService = new CryptographyService();
      this.databaseService = DatabaseConnection.getInstance();
      this.mcpService = MCPService.getInstance();

      // Initialize services in order
      await this.databaseService.initialize();
      log.info('Database service initialized');

      await this.mcpService.initialize();
      log.info('MCP service initialized');

    } catch (error) {
      log.error('Failed to initialize services:', error);
      app.quit();
    }
  }

  private createMainWindow(): void {
    this.mainWindow = new BrowserWindow({
      width: 1200,
      height: 800,
      minWidth: 800,
      minHeight: 600,
      show: false,
      webPreferences: {
        nodeIntegration: false,
        contextIsolation: true,
        preload: join(__dirname, '../preload/index.js'),
        webSecurity: true,
        allowRunningInsecureContent: false,
      },
      titleBarStyle: 'hiddenInset',
      vibrancy: 'under-window', // macOS glassmorphism effect
      backgroundMaterial: 'acrylic', // Windows 11 glassmorphism
      transparent: true,
      frame: false,
    });

    // Load the renderer
    if (isDev()) {
      this.mainWindow.loadURL('http://localhost:5173');
      this.mainWindow.webContents.openDevTools();
    } else {
      this.mainWindow.loadFile(join(__dirname, '../renderer/index.html'));
    }

    // Show window when ready
    this.mainWindow.once('ready-to-show', () => {
      this.mainWindow?.show();
      
      if (isDev()) {
        this.mainWindow?.webContents.openDevTools();
      }
    });

    // Handle window closed
    this.mainWindow.on('closed', () => {
      this.mainWindow = null;
    });
  }

  private setupMenu(): void {
    const template: Electron.MenuItemConstructorOptions[] = [
      {
        label: 'File',
        submenu: [
          {
            label: 'New Todo',
            accelerator: 'CmdOrCtrl+N',
            click: () => {
              this.mainWindow?.webContents.send('menu-new-todo');
            },
          },
          { type: 'separator' },
          {
            label: 'Import',
            accelerator: 'CmdOrCtrl+I',
            click: () => {
              this.mainWindow?.webContents.send('menu-import');
            },
          },
          {
            label: 'Export',
            accelerator: 'CmdOrCtrl+E',
            click: () => {
              this.mainWindow?.webContents.send('menu-export');
            },
          },
          { type: 'separator' },
          {
            label: 'Quit',
            accelerator: process.platform === 'darwin' ? 'Cmd+Q' : 'Ctrl+Q',
            click: () => {
              app.quit();
            },
          },
        ],
      },
      {
        label: 'Edit',
        submenu: [
          { role: 'undo' },
          { role: 'redo' },
          { type: 'separator' },
          { role: 'cut' },
          { role: 'copy' },
          { role: 'paste' },
          { role: 'selectAll' },
        ],
      },
      {
        label: 'View',
        submenu: [
          { role: 'reload' },
          { role: 'forceReload' },
          { role: 'toggleDevTools' },
          { type: 'separator' },
          { role: 'resetZoom' },
          { role: 'zoomIn' },
          { role: 'zoomOut' },
          { type: 'separator' },
          { role: 'togglefullscreen' },
        ],
      },
      {
        label: 'Window',
        submenu: [
          { role: 'minimize' },
          { role: 'close' },
        ],
      },
    ];

    const menu = Menu.buildFromTemplate(template);
    Menu.setApplicationMenu(menu);
  }

  private setupIpcHandlers(): void {
    // Database operations
    ipcMain.handle('db:query', async (event: any, sql: string, params?: any[]) => {
      try {
        return await this.databaseService?.executeQuery(sql, params);
      } catch (error) {
        log.error('Database query error:', error);
        throw error;
      }
    });

    ipcMain.handle('db:transaction', async (event: any, operations: any[]) => {
      try {
        return await this.databaseService?.executeTransaction(async (execute) => {
          const results = [];
          for (const op of operations) {
            results.push(await execute(op.query, op.params));
          }
          return results;
        });
      } catch (error) {
        log.error('Database transaction error:', error);
        throw error;
      }
    });

    // Security operations
    ipcMain.handle('security:encrypt', async (event: any, data: string, password: string) => {
      try {
        return await this.securityService?.encryptData(data, password);
      } catch (error) {
        log.error('Encryption error:', error);
        throw error;
      }
    });

    ipcMain.handle('security:decrypt', async (event: any, encryptedData: any, password: string) => {
      try {
        return await this.securityService?.decryptData(encryptedData, password);
      } catch (error) {
        log.error('Decryption error:', error);
        throw error;
      }
    });

    // MCP operations
    ipcMain.handle('mcp:sync', async (event: any, data: any) => {
      try {
        return await this.mcpService?.executeQuery(data.sql, data.params);
      } catch (error) {
        log.error('MCP sync error:', error);
        throw error;
      }
    });

    // System operations
    ipcMain.handle('system:getInfo', async () => {
      return {
        platform: process.platform,
        arch: process.arch,
        version: app.getVersion(),
        electronVersion: process.versions.electron,
        nodeVersion: process.versions.node,
      };
    });

    ipcMain.handle('app:quit', () => {
      app.quit();
    });

    ipcMain.handle('app:minimize', () => {
      this.mainWindow?.minimize();
    });

    ipcMain.handle('app:maximize', () => {
      if (this.mainWindow?.isMaximized()) {
        this.mainWindow.unmaximize();
      } else {
        this.mainWindow?.maximize();
      }
    });

    ipcMain.handle('app:close', () => {
      this.mainWindow?.close();
    });
  }

  private async cleanup(): Promise<void> {
    try {
      await this.databaseService?.close();
      await this.mcpService?.disconnect();
      log.info('Application cleanup completed');
    } catch (error) {
      log.error('Cleanup error:', error);
    }
  }
}

// Create and initialize the application
new ModernTodoApp();