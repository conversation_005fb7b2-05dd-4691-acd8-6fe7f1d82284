import { cryptoService } from './crypto.service';
import { userDAO } from '@main/dao/user.dao';
import { config } from '@main/utils/config';
import { AuthenticationError, User, UserSession, UserProfile } from '@shared/types';

export interface LoginCredentials {
  username: string;
  password: string;
  totpCode?: string;
  deviceInfo?: any;
}

export interface RegisterData {
  username: string;
  password: string;
  email?: string;
  fullName?: string;
}

export interface AuthSession {
  sessionId: string;
  userId: string;
  username: string;
  createdAt: Date;
  expiresAt: Date;
  deviceFingerprint: string;
  ipAddress: string;
  userAgent: string;
  isActive: boolean;
  permissions: string[];
}

export class AuthenticationService {
  private sessions: Map<string, AuthSession> = new Map();
  private loginAttempts: Map<string, number> = new Map();
  private accountLockouts: Map<string, Date> = new Map();
  private sessionCleanupTimer: NodeJS.Timeout | null = null;
  private securityConfig = config.getSecurityConfig();

  constructor() {
    this.startSessionCleanup();
  }

  public async register(registerData: RegisterData): Promise<{ user: User; profile: UserProfile }> {
    try {
      // Check if username already exists
      const existingUser = await userDAO.findByUsername(registerData.username);
      if (existingUser) {
        throw new AuthenticationError('Username already exists', 'USERNAME_EXISTS');
      }

      // Check if email already exists (if provided)
      if (registerData.email) {
        const existingEmailUser = await userDAO.findByEmail(registerData.email);
        if (existingEmailUser) {
          throw new AuthenticationError('Email already exists', 'EMAIL_EXISTS');
        }
      }

      // Hash password
      const passwordHash = await cryptoService.hashPassword(registerData.password);

      // Create user and profile
      const result = await userDAO.createWithProfile(
        {
          username: registerData.username,
          password_hash: passwordHash,
          is_active: true,
          email_verified: false,
        },
        {
          full_name: registerData.fullName,
          email: registerData.email,
          theme_preference: 'light',
          notification_enabled: true,
          timezone: 'UTC',
          language_preference: 'en',
        }
      );

      console.log(`User registered successfully: ${registerData.username}`);
      return result;

    } catch (error) {
      if (error instanceof AuthenticationError) {
        throw error;
      }
      console.error('Registration error:', error);
      throw new AuthenticationError('Registration failed', 'REGISTRATION_ERROR');
    }
  }

  public async login(credentials: LoginCredentials): Promise<AuthSession> {
    try {
      // Check account lockout
      await this.checkAccountLockout(credentials.username);

      // Validate credentials
      const user = await this.validateCredentials(credentials);

      // Two-factor authentication (if enabled)
      if (this.securityConfig.require2FA && credentials.totpCode) {
        await this.validateTwoFactor(user.id, credentials.totpCode);
      }

      // Create session
      const session = await this.createSession(user, credentials.deviceInfo || {});

      // Reset login attempts
      this.loginAttempts.delete(credentials.username);

      // Update last login
      await userDAO.updateLastLogin(user.id);

      // Log successful authentication
      console.log(`User logged in successfully: ${credentials.username}`);

      return session;

    } catch (error) {
      // Track failed attempts
      await this.trackFailedLogin(credentials.username);

      if (error instanceof AuthenticationError) {
        throw error;
      }

      console.error('Login error:', error);
      throw new AuthenticationError('Login failed', 'LOGIN_ERROR');
    }
  }

  public async logout(sessionId: string): Promise<void> {
    const session = this.sessions.get(sessionId);

    if (session) {
      session.isActive = false;
      this.sessions.delete(sessionId);
      console.log(`User logged out: ${session.username}`);
    }
  }

  public async validateSession(sessionId: string): Promise<AuthSession | null> {
    const session = this.sessions.get(sessionId);

    if (!session) {
      return null;
    }

    // Check if session is expired
    if (session.expiresAt < new Date()) {
      await this.logout(sessionId);
      return null;
    }

    // Update session expiry (sliding expiration)
    session.expiresAt = new Date(Date.now() + this.securityConfig.sessionTimeout);

    return session;
  }

  public async refreshSession(sessionId: string): Promise<AuthSession | null> {
    const session = await this.validateSession(sessionId);
    if (!session) {
      return null;
    }

    // Extend session
    session.expiresAt = new Date(Date.now() + this.securityConfig.sessionTimeout);
    return session;
  }

  public async changePassword(
    userId: string,
    currentPassword: string,
    newPassword: string
  ): Promise<void> {
    try {
      // Get user
      const user = await userDAO.findById(userId);
      if (!user) {
        throw new AuthenticationError('User not found', 'USER_NOT_FOUND');
      }

      // Verify current password
      const isCurrentPasswordValid = await cryptoService.verifyPassword(
        currentPassword,
        user.password_hash
      );

      if (!isCurrentPasswordValid) {
        throw new AuthenticationError('Current password is incorrect', 'INVALID_PASSWORD');
      }

      // Hash new password
      const newPasswordHash = await cryptoService.hashPassword(newPassword);

      // Update password
      await userDAO.updatePassword(userId, newPasswordHash);

      // Invalidate all user sessions (force re-login)
      await this.revokeAllUserSessions(userId);

      console.log(`Password changed for user: ${user.username}`);

    } catch (error) {
      if (error instanceof AuthenticationError) {
        throw error;
      }
      console.error('Password change error:', error);
      throw new AuthenticationError('Password change failed', 'PASSWORD_CHANGE_ERROR');
    }
  }

  public async resetPassword(username: string, newPassword: string): Promise<void> {
    try {
      const user = await userDAO.findByUsername(username);
      if (!user) {
        throw new AuthenticationError('User not found', 'USER_NOT_FOUND');
      }

      const newPasswordHash = await cryptoService.hashPassword(newPassword);
      await userDAO.updatePassword(user.id, newPasswordHash);

      // Invalidate all user sessions
      await this.revokeAllUserSessions(user.id);

      console.log(`Password reset for user: ${username}`);

    } catch (error) {
      if (error instanceof AuthenticationError) {
        throw error;
      }
      console.error('Password reset error:', error);
      throw new AuthenticationError('Password reset failed', 'PASSWORD_RESET_ERROR');
    }
  }

  private async validateCredentials(credentials: LoginCredentials): Promise<User> {
    const user = await userDAO.findByUsername(credentials.username);

    if (!user) {
      throw new AuthenticationError('Invalid credentials', 'INVALID_CREDENTIALS');
    }

    const isValid = await cryptoService.verifyPassword(
      credentials.password,
      user.password_hash
    );

    if (!isValid) {
      throw new AuthenticationError('Invalid credentials', 'INVALID_CREDENTIALS');
    }

    if (!user.is_active) {
      throw new AuthenticationError('Account is disabled', 'ACCOUNT_DISABLED');
    }

    return user;
  }

  private async validateTwoFactor(userId: string, totpCode: string): Promise<void> {
    // In a real implementation, you would retrieve the user's TOTP secret
    // and validate the code. For now, this is a placeholder.
    // const userTotpSecret = await getUserTotpSecret(userId);
    // const isValid = cryptoService.verifyTOTP(totpCode, userTotpSecret);
    
    // For demo purposes, accept '123456' as valid TOTP
    if (totpCode !== '123456') {
      throw new AuthenticationError('Invalid two-factor authentication code', 'INVALID_2FA');
    }
  }

  private async createSession(user: User, deviceInfo: any): Promise<AuthSession> {
    const sessionId = cryptoService.generateSessionToken();
    const deviceFingerprint = cryptoService.generateDeviceFingerprint(deviceInfo);

    const session: AuthSession = {
      sessionId,
      userId: user.id,
      username: user.username,
      createdAt: new Date(),
      expiresAt: new Date(Date.now() + this.securityConfig.sessionTimeout),
      deviceFingerprint,
      ipAddress: deviceInfo.ipAddress || 'localhost',
      userAgent: deviceInfo.userAgent || 'unknown',
      isActive: true,
      permissions: ['read', 'write'], // Default permissions
    };

    // Store session
    this.sessions.set(sessionId, session);

    return session;
  }

  private async checkAccountLockout(username: string): Promise<void> {
    const lockoutUntil = this.accountLockouts.get(username);

    if (lockoutUntil && lockoutUntil > new Date()) {
      const remainingTime = Math.ceil((lockoutUntil.getTime() - Date.now()) / 1000 / 60);
      throw new AuthenticationError(
        `Account locked. Try again in ${remainingTime} minutes.`,
        'ACCOUNT_LOCKED'
      );
    }
  }

  private async trackFailedLogin(username: string): Promise<void> {
    const attempts = (this.loginAttempts.get(username) || 0) + 1;
    this.loginAttempts.set(username, attempts);

    if (attempts >= this.securityConfig.maxLoginAttempts) {
      const lockoutUntil = new Date(Date.now() + this.securityConfig.lockoutDuration);
      this.accountLockouts.set(username, lockoutUntil);

      console.warn(
        `Account locked for ${username} after ${attempts} failed attempts`
      );
    }
  }

  private async revokeAllUserSessions(userId: string): Promise<void> {
    for (const [sessionId, session] of this.sessions.entries()) {
      if (session.userId === userId) {
        await this.logout(sessionId);
      }
    }
  }

  private startSessionCleanup(): void {
    this.sessionCleanupTimer = setInterval(() => {
      const now = new Date();

      for (const [sessionId, session] of this.sessions.entries()) {
        if (session.expiresAt < now) {
          this.sessions.delete(sessionId);
        }
      }

      // Clean up old lockouts
      for (const [username, lockoutUntil] of this.accountLockouts.entries()) {
        if (lockoutUntil < now) {
          this.accountLockouts.delete(username);
        }
      }

      // Clean up old login attempts (after 1 hour)
      const oneHourAgo = Date.now() - 3600000;
      for (const [username] of this.loginAttempts.entries()) {
        // In a real implementation, you'd track timestamps for login attempts
        // For now, we'll clear all attempts periodically
        this.loginAttempts.delete(username);
      }
    }, 60000); // Clean up every minute
  }

  public getSessionCount(): number {
    return this.sessions.size;
  }

  public getActiveUsers(): string[] {
    const users = new Set<string>();
    for (const session of this.sessions.values()) {
      if (session.isActive) {
        users.add(session.username);
      }
    }
    return Array.from(users);
  }

  public async getUserSessions(userId: string): Promise<AuthSession[]> {
    const userSessions: AuthSession[] = [];
    for (const session of this.sessions.values()) {
      if (session.userId === userId && session.isActive) {
        userSessions.push(session);
      }
    }
    return userSessions;
  }

  public async shutdown(): Promise<void> {
    if (this.sessionCleanupTimer) {
      clearInterval(this.sessionCleanupTimer);
      this.sessionCleanupTimer = null;
    }

    // Clear all sessions
    this.sessions.clear();
    this.loginAttempts.clear();
    this.accountLockouts.clear();

    console.log('Authentication service shut down');
  }
}

export const authService = new AuthenticationService();