import { BaseDAO } from './base.dao';
import { Todo, TodoStatus, TodoPriority, DatabaseError, FilterOptions, PaginationOptions } from '@shared/types';

export interface TodoFilterOptions extends FilterOptions {
  status?: TodoStatus;
  priority?: TodoPriority;
  category_id?: string;
  due_date_from?: Date;
  due_date_to?: Date;
  tags?: string[];
  search?: string;
  is_completed?: boolean;
  is_overdue?: boolean;
}

export class TodoDAO extends BaseDAO<Todo> {
  constructor() {
    super('todos');
  }

  /**
   * Create a new todo with proper validation and defaults
   */
  public async createTodo(todoData: Partial<Todo>, userId: string): Promise<Todo> {
    // Validate required fields
    if (!todoData.title || todoData.title.trim() === '') {
      throw new DatabaseError('Title is required', 'VALIDATION_ERROR');
    }

    if (!userId) {
      throw new DatabaseError('User ID is required', 'VALIDATION_ERROR');
    }

    // Get next position for the category
    const position = await this.getNextPosition(todoData.category_id || null, userId);

    // Prepare data with defaults
    const data: Partial<Todo> = {
      ...todoData,
      user_id: userId,
      title: todoData.title.trim(),
      status: todoData.status || 'pending',
      priority: todoData.priority || 'medium',
      tags: todoData.tags || [],
      position,
      metadata: todoData.metadata || {},
      is_deleted: false,
    };

    // Use custom query to handle arrays and JSON properly
    const fields = Object.keys(data).filter(key => (data as any)[key] !== undefined);
    const values: any[] = [];
    let paramIndex = 1;

    const placeholders = fields.map(key => {
      const value = (data as any)[key];

      // Handle JSON fields
      if (key === 'metadata' && typeof value === 'object') {
        values.push(JSON.stringify(value));
        return `$${paramIndex++}`;
      }

      // Handle array fields - use array literal syntax for DuckDB
      if (key === 'tags' && Array.isArray(value)) {
        if (value.length === 0) {
          return `ARRAY[]::VARCHAR[]`;
        } else {
          // Create array literal with quoted strings
          const arrayLiteral = `ARRAY[${value.map(tag => `'${tag.replace(/'/g, "''")}'`).join(', ')}]`;
          return arrayLiteral;
        }
      }

      // Regular fields
      values.push(value);
      return `$${paramIndex++}`;
    });

    const query = `
      INSERT INTO todos (${fields.join(', ')})
      VALUES (${placeholders.join(', ')})
      RETURNING *
    `;

    const result = await this.executeQuery<Todo>(query, values);

    if (result.rows.length === 0) {
      throw new DatabaseError(`Failed to create todo`, 'CREATE_ERROR');
    }

    return result.rows[0];
  }

  /**
   * Find todo by ID with user authorization
   */
  public async findByIdForUser(id: string, userId: string): Promise<Todo | null> {
    const result = await this.executeQuery<Todo>(
      `SELECT t.*, c.name as category_name, c.color as category_color
       FROM todos t
       LEFT JOIN categories c ON t.category_id = c.id
       WHERE t.id = $1 AND t.user_id = $2 AND t.is_deleted = FALSE`,
      [id, userId]
    );
    return result.rows[0] || null;
  }

  /**
   * Update todo with user authorization and special handling
   */
  public async updateTodo(id: string, data: Partial<Todo>, userId: string): Promise<Todo> {
    // Validate user authorization first
    const existingTodo = await this.findByIdForUser(id, userId);
    if (!existingTodo) {
      throw new DatabaseError('Todo not found or access denied', 'NOT_FOUND');
    }

    // Prepare update data
    const updateData = { ...data };
    delete updateData.id;
    delete updateData.user_id; // Prevent user_id changes
    delete updateData.created_at; // Prevent created_at changes

    // Handle status change logic
    if (updateData.status === 'completed' && existingTodo.status !== 'completed') {
      updateData.completed_at = new Date();
    } else if (updateData.status !== 'completed' && existingTodo.status === 'completed') {
      updateData.completed_at = undefined;
    }

    // Validate title if provided
    if (updateData.title !== undefined && (!updateData.title || updateData.title.trim() === '')) {
      throw new DatabaseError('Title cannot be empty', 'VALIDATION_ERROR');
    }

    if (updateData.title) {
      updateData.title = updateData.title.trim();
    }

    const fields = Object.keys(updateData).filter(key => (updateData as any)[key] !== undefined);

    if (fields.length === 0) {
      throw new DatabaseError('No fields to update', 'UPDATE_ERROR');
    }

    const setClause = fields.map((field, index) => `${field} = $${index + 1}`).join(', ');
    const values = fields.map(key => (updateData as any)[key]);
    values.push(id, userId);

    const query = `
      UPDATE todos
      SET ${setClause}, updated_at = CURRENT_TIMESTAMP
      WHERE id = $${values.length - 1} AND user_id = $${values.length} AND is_deleted = FALSE
      RETURNING *
    `;

    const result = await this.executeQuery<Todo>(query, values);

    if (result.rows.length === 0) {
      throw new DatabaseError('Todo not found or access denied', 'NOT_FOUND');
    }

    return result.rows[0];
  }

  /**
   * Delete todo with user authorization
   */
  public async deleteTodo(id: string, userId: string): Promise<boolean> {
    const result = await this.executeQuery(
      `DELETE FROM todos WHERE id = $1 AND user_id = $2`,
      [id, userId]
    );
    return result.rowCount > 0;
  }

  /**
   * Soft delete todo with user authorization
   */
  public async softDeleteTodo(id: string, userId: string): Promise<Todo> {
    const result = await this.executeQuery<Todo>(
      `UPDATE todos
       SET is_deleted = TRUE, updated_at = CURRENT_TIMESTAMP
       WHERE id = $1 AND user_id = $2 AND is_deleted = FALSE
       RETURNING *`,
      [id, userId]
    );

    if (result.rows.length === 0) {
      throw new DatabaseError('Todo not found or access denied', 'NOT_FOUND');
    }

    return result.rows[0];
  }

  public async findByUserId(
    userId: string, 
    pagination?: PaginationOptions,
    filters?: TodoFilterOptions
  ): Promise<{ data: Todo[]; total: number }> {
    let whereClause = 'WHERE t.user_id = $1 AND t.is_deleted = FALSE';
    let params: any[] = [userId];
    let paramIndex = 2;

    // Build additional filters
    if (filters) {
      if (filters.status) {
        whereClause += ` AND t.status = $${paramIndex}`;
        params.push(filters.status);
        paramIndex++;
      }

      if (filters.priority) {
        whereClause += ` AND t.priority = $${paramIndex}`;
        params.push(filters.priority);
        paramIndex++;
      }

      if (filters.category_id) {
        whereClause += ` AND t.category_id = $${paramIndex}`;
        params.push(filters.category_id);
        paramIndex++;
      }

      if (filters.due_date_from) {
        whereClause += ` AND t.due_date >= $${paramIndex}`;
        params.push(filters.due_date_from);
        paramIndex++;
      }

      if (filters.due_date_to) {
        whereClause += ` AND t.due_date <= $${paramIndex}`;
        params.push(filters.due_date_to);
        paramIndex++;
      }

      if (filters.tags && filters.tags.length > 0) {
        whereClause += ` AND t.tags && $${paramIndex}`;
        params.push(filters.tags);
        paramIndex++;
      }

      if (filters.search) {
        whereClause += ` AND (t.title ILIKE $${paramIndex} OR t.description ILIKE $${paramIndex})`;
        params.push(`%${filters.search}%`);
        paramIndex++;
      }

      if (filters.is_completed !== undefined) {
        if (filters.is_completed) {
          whereClause += ` AND t.status = 'completed'`;
        } else {
          whereClause += ` AND t.status != 'completed'`;
        }
      }

      if (filters.is_overdue) {
        whereClause += ` AND t.due_date < CURRENT_TIMESTAMP AND t.status NOT IN ('completed', 'cancelled')`;
      }
    }

    // Get total count
    const countQuery = `SELECT COUNT(*) as total FROM todos t ${whereClause}`;
    const countResult = await this.executeQuery<{ total: number }>(countQuery, params);
    const total = countResult.rows[0]?.total || 0;

    // Build main query
    let query = `
      SELECT t.*, c.name as category_name, c.color as category_color
      FROM todos t
      LEFT JOIN categories c ON t.category_id = c.id
      ${whereClause}
    `;

    // Add ordering
    if (pagination?.sortBy) {
      const sortOrder = pagination.sortOrder || 'ASC';
      query += ` ORDER BY ${pagination.sortBy} ${sortOrder}`;
    } else {
      // Default ordering: by position, then by created_at
      query += ` ORDER BY t.position ASC, t.created_at DESC`;
    }

    // Add pagination
    if (pagination?.limit) {
      query += ` LIMIT $${paramIndex}`;
      params.push(pagination.limit);
      paramIndex++;

      if (pagination.page && pagination.page > 1) {
        const offset = (pagination.page - 1) * pagination.limit;
        query += ` OFFSET $${paramIndex}`;
        params.push(offset);
      }
    }

    const result = await this.executeQuery<Todo>(query, params);
    
    return {
      data: result.rows,
      total
    };
  }

  public async updateStatus(todoId: string, status: TodoStatus, userId: string): Promise<Todo> {
    const updates: any = { status, updated_at: new Date() };
    
    // Set completed_at when status is completed
    if (status === 'completed') {
      updates.completed_at = new Date();
    } else {
      updates.completed_at = null;
    }

    const result = await this.executeQuery<Todo>(
      `UPDATE todos 
       SET status = $1, completed_at = $2, updated_at = CURRENT_TIMESTAMP
       WHERE id = $3 AND user_id = $4 AND is_deleted = FALSE
       RETURNING *`,
      [status, updates.completed_at, todoId, userId]
    );

    if (result.rows.length === 0) {
      throw new DatabaseError('Todo not found or access denied', 'NOT_FOUND');
    }

    return result.rows[0];
  }

  public async updatePriority(todoId: string, priority: TodoPriority, userId: string): Promise<Todo> {
    const result = await this.executeQuery<Todo>(
      `UPDATE todos 
       SET priority = $1, updated_at = CURRENT_TIMESTAMP
       WHERE id = $2 AND user_id = $3 AND is_deleted = FALSE
       RETURNING *`,
      [priority, todoId, userId]
    );

    if (result.rows.length === 0) {
      throw new DatabaseError('Todo not found or access denied', 'NOT_FOUND');
    }

    return result.rows[0];
  }

  public async updatePosition(todoId: string, newPosition: number, userId: string): Promise<Todo> {
    return await this.executeTransaction(async (execute) => {
      // Get the current todo
      const currentResult = await execute(
        'SELECT position, category_id FROM todos WHERE id = $1 AND user_id = $2 AND is_deleted = FALSE',
        [todoId, userId]
      );

      if (currentResult.rows.length === 0) {
        throw new DatabaseError('Todo not found or access denied', 'NOT_FOUND');
      }

      const currentTodo = currentResult.rows[0];
      const oldPosition = currentTodo.position;
      const categoryId = currentTodo.category_id;

      // Update positions of other todos in the same category
      if (newPosition > oldPosition) {
        // Moving down: shift todos up
        await execute(
          `UPDATE todos 
           SET position = position - 1, updated_at = CURRENT_TIMESTAMP
           WHERE user_id = $1 AND category_id = $2 AND position > $3 AND position <= $4 AND is_deleted = FALSE`,
          [userId, categoryId, oldPosition, newPosition]
        );
      } else if (newPosition < oldPosition) {
        // Moving up: shift todos down
        await execute(
          `UPDATE todos 
           SET position = position + 1, updated_at = CURRENT_TIMESTAMP
           WHERE user_id = $1 AND category_id = $2 AND position >= $3 AND position < $4 AND is_deleted = FALSE`,
          [userId, categoryId, newPosition, oldPosition]
        );
      }

      // Update the target todo's position
      const result = await execute(
        `UPDATE todos 
         SET position = $1, updated_at = CURRENT_TIMESTAMP
         WHERE id = $2 AND user_id = $3 AND is_deleted = FALSE
         RETURNING *`,
        [newPosition, todoId, userId]
      );

      return result.rows[0];
    });
  }

  public async addTags(todoId: string, tags: string[], userId: string): Promise<Todo> {
    const result = await this.executeQuery<Todo>(
      `UPDATE todos 
       SET tags = array_cat(tags, $1), updated_at = CURRENT_TIMESTAMP
       WHERE id = $2 AND user_id = $3 AND is_deleted = FALSE
       RETURNING *`,
      [tags, todoId, userId]
    );

    if (result.rows.length === 0) {
      throw new DatabaseError('Todo not found or access denied', 'NOT_FOUND');
    }

    return result.rows[0];
  }

  public async removeTags(todoId: string, tags: string[], userId: string): Promise<Todo> {
    const result = await this.executeQuery<Todo>(
      `UPDATE todos 
       SET tags = array_remove_all(tags, $1), updated_at = CURRENT_TIMESTAMP
       WHERE id = $2 AND user_id = $3 AND is_deleted = FALSE
       RETURNING *`,
      [tags, todoId, userId]
    );

    if (result.rows.length === 0) {
      throw new DatabaseError('Todo not found or access denied', 'NOT_FOUND');
    }

    return result.rows[0];
  }

  public async getOverdueTodos(userId: string): Promise<Todo[]> {
    const result = await this.executeQuery<Todo>(
      `SELECT t.*, c.name as category_name, c.color as category_color
       FROM todos t
       LEFT JOIN categories c ON t.category_id = c.id
       WHERE t.user_id = $1 
         AND t.due_date < CURRENT_TIMESTAMP 
         AND t.status NOT IN ('completed', 'cancelled')
         AND t.is_deleted = FALSE
       ORDER BY t.due_date ASC`,
      [userId]
    );

    return result.rows;
  }

  public async getTodosWithReminders(beforeTime?: Date): Promise<Todo[]> {
    const time = beforeTime || new Date();
    
    const result = await this.executeQuery<Todo>(
      `SELECT t.*, c.name as category_name, c.color as category_color
       FROM todos t
       LEFT JOIN categories c ON t.category_id = c.id
       WHERE t.reminder_at <= $1 
         AND t.status NOT IN ('completed', 'cancelled')
         AND t.is_deleted = FALSE
       ORDER BY t.reminder_at ASC`,
      [time]
    );

    return result.rows;
  }

  public async getTodosByCategory(categoryId: string, userId: string): Promise<Todo[]> {
    const result = await this.executeQuery<Todo>(
      `SELECT * FROM todos 
       WHERE category_id = $1 AND user_id = $2 AND is_deleted = FALSE
       ORDER BY position ASC, created_at DESC`,
      [categoryId, userId]
    );

    return result.rows;
  }

  public async searchTodos(userId: string, searchTerm: string, limit: number = 20): Promise<Todo[]> {
    const result = await this.executeQuery<Todo>(
      `SELECT t.*, c.name as category_name, c.color as category_color
       FROM todos t
       LEFT JOIN categories c ON t.category_id = c.id
       WHERE t.user_id = $1 
         AND t.is_deleted = FALSE
         AND (
           t.title ILIKE $2 
           OR t.description ILIKE $2 
           OR $3 = ANY(t.tags)
         )
       ORDER BY 
         CASE WHEN t.title ILIKE $2 THEN 1 ELSE 2 END,
         t.updated_at DESC
       LIMIT $4`,
      [userId, `%${searchTerm}%`, searchTerm, limit]
    );

    return result.rows;
  }

  public async getUserTodoStats(userId: string): Promise<{
    total: number;
    completed: number;
    pending: number;
    inProgress: number;
    overdue: number;
    completionRate: number;
  }> {
    const result = await this.executeQuery<{
      total: number;
      completed: number;
      pending: number;
      in_progress: number;
      overdue: number;
    }>(
      `SELECT 
        COUNT(*) as total,
        COUNT(CASE WHEN status = 'completed' THEN 1 END) as completed,
        COUNT(CASE WHEN status = 'pending' THEN 1 END) as pending,
        COUNT(CASE WHEN status = 'in_progress' THEN 1 END) as in_progress,
        COUNT(CASE WHEN due_date < CURRENT_TIMESTAMP AND status NOT IN ('completed', 'cancelled') THEN 1 END) as overdue
       FROM todos 
       WHERE user_id = $1 AND is_deleted = FALSE`,
      [userId]
    );

    const stats = result.rows[0] || {
      total: 0,
      completed: 0,
      pending: 0,
      in_progress: 0,
      overdue: 0
    };

    const completionRate = stats.total > 0 ? (stats.completed / stats.total) * 100 : 0;

    return {
      total: stats.total,
      completed: stats.completed,
      pending: stats.pending,
      inProgress: stats.in_progress,
      overdue: stats.overdue,
      completionRate: Math.round(completionRate * 100) / 100,
    };
  }

  public async getNextPosition(categoryId: string | null, userId: string): Promise<number> {
    const result = await this.executeQuery<{ max_position: number }>(
      `SELECT COALESCE(MAX(position), 0) + 1 as max_position
       FROM todos 
       WHERE user_id = $1 AND category_id = $2 AND is_deleted = FALSE`,
      [userId, categoryId]
    );

    return result.rows[0]?.max_position || 1;
  }

  public async moveToCategory(todoId: string, newCategoryId: string | null, userId: string): Promise<Todo> {
    return await this.executeTransaction(async (execute) => {
      // Get next position in the new category
      const nextPosition = await this.getNextPosition(newCategoryId, userId);

      // Update the todo
      const result = await execute(
        `UPDATE todos 
         SET category_id = $1, position = $2, updated_at = CURRENT_TIMESTAMP
         WHERE id = $3 AND user_id = $4 AND is_deleted = FALSE
         RETURNING *`,
        [newCategoryId, nextPosition, todoId, userId]
      );

      if (result.rows.length === 0) {
        throw new DatabaseError('Todo not found or access denied', 'NOT_FOUND');
      }

      return result.rows[0];
    });
  }
}

export const todoDAO = new TodoDAO();