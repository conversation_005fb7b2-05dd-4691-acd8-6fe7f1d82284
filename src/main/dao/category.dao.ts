import { BaseDAO } from './base.dao';
import { Category, DatabaseError } from '@shared/types';

export class CategoryDA<PERSON> extends BaseDAO<Category> {
  constructor() {
    super('categories');
  }

  public async findByUserId(userId: string): Promise<Category[]> {
    const result = await this.executeQuery<Category>(
      'SELECT * FROM categories WHERE user_id = $1 ORDER BY sort_order ASC, created_at ASC',
      [userId]
    );
    return result.rows;
  }

  public async findByName(userId: string, name: string): Promise<Category | null> {
    const result = await this.executeQuery<Category>(
      'SELECT * FROM categories WHERE user_id = $1 AND name = $2',
      [userId, name]
    );
    return result.rows[0] || null;
  }

  public async getDefaultCategory(userId: string): Promise<Category | null> {
    const result = await this.executeQuery<Category>(
      'SELECT * FROM categories WHERE user_id = $1 AND is_default = TRUE',
      [userId]
    );
    return result.rows[0] || null;
  }

  public async createCategory(
    userId: string,
    name: string,
    color: string = '#007bff',
    icon?: string,
    isDefault: boolean = false
  ): Promise<Category> {
    // Get the next sort order
    const nextSortOrder = await this.getNextSortOrder(userId);

    // If setting as default, remove default from other categories
    if (isDefault) {
      await this.removeDefaultStatus(userId);
    }

    const result = await this.executeQuery<Category>(
      `INSERT INTO categories (user_id, name, color, icon, sort_order, is_default)
       VALUES ($1, $2, $3, $4, $5, $6)
       RETURNING *`,
      [userId, name, color, icon, nextSortOrder, isDefault]
    );

    if (result.rows.length === 0) {
      throw new DatabaseError('Failed to create category', 'CREATE_ERROR');
    }

    return result.rows[0];
  }

  public async updateCategory(
    categoryId: string,
    userId: string,
    updates: Partial<Category>
  ): Promise<Category> {
    // If setting as default, remove default from other categories
    if (updates.is_default) {
      await this.removeDefaultStatus(userId);
    }

    const result = await this.executeQuery<Category>(
      `UPDATE categories 
       SET name = COALESCE($1, name),
           color = COALESCE($2, color),
           icon = COALESCE($3, icon),
           is_default = COALESCE($4, is_default),
           updated_at = CURRENT_TIMESTAMP
       WHERE id = $5 AND user_id = $6
       RETURNING *`,
      [updates.name, updates.color, updates.icon, updates.is_default, categoryId, userId]
    );

    if (result.rows.length === 0) {
      throw new DatabaseError('Category not found or access denied', 'NOT_FOUND');
    }

    return result.rows[0];
  }

  public async deleteCategory(categoryId: string, userId: string): Promise<boolean> {
    return await this.executeTransaction(async (execute) => {
      // Check if this is the default category
      const categoryResult = await execute(
        'SELECT is_default FROM categories WHERE id = $1 AND user_id = $2',
        [categoryId, userId]
      );

      if (categoryResult.rows.length === 0) {
        throw new DatabaseError('Category not found or access denied', 'NOT_FOUND');
      }

      const isDefault = categoryResult.rows[0].is_default;

      if (isDefault) {
        throw new DatabaseError('Cannot delete the default category', 'VALIDATION_ERROR');
      }

      // Move todos to default category
      const defaultCategory = await this.getDefaultCategory(userId);
      if (defaultCategory) {
        await execute(
          'UPDATE todos SET category_id = $1 WHERE category_id = $2 AND user_id = $3',
          [defaultCategory.id, categoryId, userId]
        );
      } else {
        // If no default category, set todos to null category
        await execute(
          'UPDATE todos SET category_id = NULL WHERE category_id = $1 AND user_id = $2',
          [categoryId, userId]
        );
      }

      // Delete the category
      const deleteResult = await execute(
        'DELETE FROM categories WHERE id = $1 AND user_id = $2',
        [categoryId, userId]
      );

      return deleteResult.rowCount > 0;
    });
  }

  public async reorderCategories(userId: string, categoryOrders: Array<{ id: string; sort_order: number }>): Promise<Category[]> {
    return await this.executeTransaction(async (execute) => {
      const updatedCategories: Category[] = [];

      for (const { id, sort_order } of categoryOrders) {
        const result = await execute(
          `UPDATE categories 
           SET sort_order = $1, updated_at = CURRENT_TIMESTAMP
           WHERE id = $2 AND user_id = $3
           RETURNING *`,
          [sort_order, id, userId]
        );

        if (result.rows.length > 0) {
          updatedCategories.push(result.rows[0]);
        }
      }

      return updatedCategories;
    });
  }

  public async getCategoryWithTodoCount(userId: string): Promise<Array<Category & { todo_count: number }>> {
    const result = await this.executeQuery<Category & { todo_count: number }>(
      `SELECT c.*, 
              COUNT(t.id) as todo_count
       FROM categories c
       LEFT JOIN todos t ON c.id = t.category_id AND t.is_deleted = FALSE
       WHERE c.user_id = $1
       GROUP BY c.id, c.user_id, c.name, c.color, c.icon, c.sort_order, c.is_default, c.created_at, c.updated_at
       ORDER BY c.sort_order ASC, c.created_at ASC`,
      [userId]
    );

    return result.rows;
  }

  public async setDefaultCategory(categoryId: string, userId: string): Promise<Category> {
    return await this.executeTransaction(async (execute) => {
      // Remove default status from all categories
      await execute(
        'UPDATE categories SET is_default = FALSE WHERE user_id = $1',
        [userId]
      );

      // Set the specified category as default
      const result = await execute(
        `UPDATE categories 
         SET is_default = TRUE, updated_at = CURRENT_TIMESTAMP
         WHERE id = $1 AND user_id = $2
         RETURNING *`,
        [categoryId, userId]
      );

      if (result.rows.length === 0) {
        throw new DatabaseError('Category not found or access denied', 'NOT_FOUND');
      }

      return result.rows[0];
    });
  }

  private async getNextSortOrder(userId: string): Promise<number> {
    const result = await this.executeQuery<{ max_order: number }>(
      'SELECT COALESCE(MAX(sort_order), 0) + 1 as max_order FROM categories WHERE user_id = $1',
      [userId]
    );
    return result.rows[0]?.max_order || 1;
  }

  private async removeDefaultStatus(userId: string): Promise<void> {
    await this.executeQuery(
      'UPDATE categories SET is_default = FALSE WHERE user_id = $1 AND is_default = TRUE',
      [userId]
    );
  }

  public async getCategoryStats(categoryId: string, userId: string): Promise<{
    totalTodos: number;
    completedTodos: number;
    pendingTodos: number;
    overdueTodos: number;
  }> {
    const result = await this.executeQuery<{
      total_todos: number;
      completed_todos: number;
      pending_todos: number;
      overdue_todos: number;
    }>(
      `SELECT 
        COUNT(t.id) as total_todos,
        COUNT(CASE WHEN t.status = 'completed' THEN 1 END) as completed_todos,
        COUNT(CASE WHEN t.status = 'pending' THEN 1 END) as pending_todos,
        COUNT(CASE WHEN t.due_date < CURRENT_TIMESTAMP AND t.status NOT IN ('completed', 'cancelled') THEN 1 END) as overdue_todos
       FROM todos t
       WHERE t.category_id = $1 AND t.user_id = $2 AND t.is_deleted = FALSE`,
      [categoryId, userId]
    );

    const stats = result.rows[0] || {
      total_todos: 0,
      completed_todos: 0,
      pending_todos: 0,
      overdue_todos: 0
    };

    return {
      totalTodos: stats.total_todos,
      completedTodos: stats.completed_todos,
      pendingTodos: stats.pending_todos,
      overdueTodos: stats.overdue_todos,
    };
  }

  public async searchCategories(userId: string, searchTerm: string): Promise<Category[]> {
    const result = await this.executeQuery<Category>(
      `SELECT * FROM categories 
       WHERE user_id = $1 AND name ILIKE $2
       ORDER BY sort_order ASC, created_at ASC`,
      [userId, `%${searchTerm}%`]
    );

    return result.rows;
  }

  public async duplicateCategory(categoryId: string, userId: string, newName: string): Promise<Category> {
    const originalCategory = await this.findById(categoryId);
    
    if (!originalCategory || originalCategory.user_id !== userId) {
      throw new DatabaseError('Category not found or access denied', 'NOT_FOUND');
    }

    return await this.createCategory(
      userId,
      newName,
      originalCategory.color,
      originalCategory.icon,
      false // Never duplicate as default
    );
  }
}

export const categoryDAO = new CategoryDAO();