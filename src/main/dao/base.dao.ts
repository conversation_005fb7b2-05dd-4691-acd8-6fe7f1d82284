import { dbConnection } from '@main/database/connection';
import { DatabaseError, QueryResult, PaginationOptions, FilterOptions } from '@shared/types';

export abstract class BaseDAO<T> {
  protected tableName: string;

  constructor(tableName: string) {
    this.tableName = tableName;
  }

  protected async executeQuery<R = any>(
    query: string, 
    params: any[] = []
  ): Promise<QueryResult<R>> {
    try {
      return await dbConnection.executeQuery<R>(query, params);
    } catch (error) {
      const errorMessage = error instanceof Error ? error.message : 'Unknown error';
      throw new DatabaseError(
        `Query failed for table ${this.tableName}: ${errorMessage}`,
        'QUERY_ERROR',
        error
      );
    }
  }

  protected async executeTransaction<R>(
    callback: (execute: (query: string, params?: any[]) => Promise<QueryResult>) => Promise<R>
  ): Promise<R> {
    try {
      return await dbConnection.executeTransaction(callback);
    } catch (error) {
      const errorMessage = error instanceof Error ? error.message : 'Unknown error';
      throw new DatabaseError(
        `Transaction failed for table ${this.tableName}: ${errorMessage}`,
        'TRANSACTION_ERROR',
        error
      );
    }
  }

  public async findById(id: string): Promise<T | null> {
    const result = await this.executeQuery<T>(
      `SELECT * FROM ${this.tableName} WHERE id = $1`,
      [id]
    );
    return result.rows[0] || null;
  }

  public async findAll(
    pagination?: PaginationOptions,
    filters?: FilterOptions
  ): Promise<{ data: T[]; total: number }> {
    let whereClause = '';
    let params: any[] = [];
    let paramIndex = 1;

    // Build WHERE clause from filters
    if (filters) {
      const conditions: string[] = [];
      
      Object.entries(filters).forEach(([key, value]) => {
        if (value !== undefined && value !== null) {
          if (Array.isArray(value)) {
            // Handle array filters (e.g., tags)
            conditions.push(`${key} && $${paramIndex}`);
            params.push(value);
          } else if (key.includes('_from') || key.includes('_to')) {
            // Handle date range filters
            const baseKey = key.replace('_from', '').replace('_to', '');
            if (key.includes('_from')) {
              conditions.push(`${baseKey} >= $${paramIndex}`);
            } else {
              conditions.push(`${baseKey} <= $${paramIndex}`);
            }
            params.push(value);
          } else {
            conditions.push(`${key} = $${paramIndex}`);
            params.push(value);
          }
          paramIndex++;
        }
      });

      if (conditions.length > 0) {
        whereClause = `WHERE ${conditions.join(' AND ')}`;
      }
    }

    // Get total count
    const countQuery = `SELECT COUNT(*) as total FROM ${this.tableName} ${whereClause}`;
    const countResult = await this.executeQuery<{ total: number }>(countQuery, params);
    const total = countResult.rows[0]?.total || 0;

    // Build main query with pagination
    let query = `SELECT * FROM ${this.tableName} ${whereClause}`;
    
    if (pagination?.sortBy) {
      const sortOrder = pagination.sortOrder || 'ASC';
      query += ` ORDER BY ${pagination.sortBy} ${sortOrder}`;
    }

    if (pagination?.limit) {
      query += ` LIMIT $${paramIndex}`;
      params.push(pagination.limit);
      paramIndex++;

      if (pagination.page && pagination.page > 1) {
        const offset = (pagination.page - 1) * pagination.limit;
        query += ` OFFSET $${paramIndex}`;
        params.push(offset);
      }
    }

    const result = await this.executeQuery<T>(query, params);
    
    return {
      data: result.rows,
      total
    };
  }

  public async create(data: Partial<T>): Promise<T> {
    const fields = Object.keys(data).filter(key => (data as any)[key] !== undefined);
    const values = fields.map(key => (data as any)[key]);
    const placeholders = fields.map((_, index) => `$${index + 1}`).join(', ');

    const query = `
      INSERT INTO ${this.tableName} (${fields.join(', ')})
      VALUES (${placeholders})
      RETURNING *
    `;

    const result = await this.executeQuery<T>(query, values);
    
    if (result.rows.length === 0) {
      throw new DatabaseError(`Failed to create record in ${this.tableName}`, 'CREATE_ERROR');
    }

    return result.rows[0];
  }

  public async update(id: string, data: Partial<T>): Promise<T> {
    const fields = Object.keys(data).filter(key => (data as any)[key] !== undefined && key !== 'id');
    
    if (fields.length === 0) {
      throw new DatabaseError('No fields to update', 'UPDATE_ERROR');
    }

    const setClause = fields.map((field, index) => `${field} = $${index + 1}`).join(', ');
    const values = fields.map(key => (data as any)[key]);
    values.push(id);

    const query = `
      UPDATE ${this.tableName}
      SET ${setClause}, updated_at = CURRENT_TIMESTAMP
      WHERE id = $${values.length}
      RETURNING *
    `;

    const result = await this.executeQuery<T>(query, values);
    
    if (result.rows.length === 0) {
      throw new DatabaseError(`Record not found in ${this.tableName}`, 'NOT_FOUND');
    }

    return result.rows[0];
  }

  public async delete(id: string): Promise<boolean> {
    const result = await this.executeQuery(
      `DELETE FROM ${this.tableName} WHERE id = $1`,
      [id]
    );
    return result.rowCount > 0;
  }

  public async softDelete(id: string): Promise<T> {
    const result = await this.executeQuery<T>(
      `UPDATE ${this.tableName} 
       SET is_deleted = TRUE, updated_at = CURRENT_TIMESTAMP 
       WHERE id = $1 
       RETURNING *`,
      [id]
    );
    
    if (result.rows.length === 0) {
      throw new DatabaseError(`Record not found in ${this.tableName}`, 'NOT_FOUND');
    }

    return result.rows[0];
  }

  public async exists(id: string): Promise<boolean> {
    const result = await this.executeQuery<{ exists: boolean }>(
      `SELECT EXISTS(SELECT 1 FROM ${this.tableName} WHERE id = $1) as exists`,
      [id]
    );
    return result.rows[0]?.exists || false;
  }

  public async count(filters?: FilterOptions): Promise<number> {
    let whereClause = '';
    let params: any[] = [];

    if (filters) {
      const conditions: string[] = [];
      let paramIndex = 1;

      Object.entries(filters).forEach(([key, value]) => {
        if (value !== undefined && value !== null) {
          conditions.push(`${key} = $${paramIndex}`);
          params.push(value);
          paramIndex++;
        }
      });

      if (conditions.length > 0) {
        whereClause = `WHERE ${conditions.join(' AND ')}`;
      }
    }

    const result = await this.executeQuery<{ count: number }>(
      `SELECT COUNT(*) as count FROM ${this.tableName} ${whereClause}`,
      params
    );
    
    return result.rows[0]?.count || 0;
  }

  protected buildWhereClause(filters: Record<string, any>): { clause: string; params: any[] } {
    const conditions: string[] = [];
    const params: any[] = [];
    let paramIndex = 1;

    Object.entries(filters).forEach(([key, value]) => {
      if (value !== undefined && value !== null) {
        if (Array.isArray(value)) {
          conditions.push(`${key} = ANY($${paramIndex})`);
        } else {
          conditions.push(`${key} = $${paramIndex}`);
        }
        params.push(value);
        paramIndex++;
      }
    });

    const clause = conditions.length > 0 ? `WHERE ${conditions.join(' AND ')}` : '';
    return { clause, params };
  }

  protected generateUUID(): string {
    return 'xxxxxxxx-xxxx-4xxx-yxxx-xxxxxxxxxxxx'.replace(/[xy]/g, function(c) {
      const r = Math.random() * 16 | 0;
      const v = c == 'x' ? r : (r & 0x3 | 0x8);
      return v.toString(16);
    });
  }
}