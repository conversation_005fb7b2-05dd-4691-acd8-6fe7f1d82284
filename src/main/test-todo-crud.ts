import { dbConnection } from '@main/database/connection';
import { todoDAO } from '@main/dao/todo.dao';
import { userDAO } from '@main/dao/user.dao';
import { categoryDAO } from '@main/dao/category.dao';
import { Todo, User, Category } from '@shared/types';

async function testTodoCRUD(): Promise<void> {
  console.log('🧪 Testing TodoDAO CRUD Operations');
  console.log('===================================\n');

  try {
    // Initialize database connection
    await dbConnection.initialize();
    console.log('✅ Database connection initialized\n');

    // Create a test user first
    console.log('👤 Creating test user...');
    const testUser: User = await userDAO.create({
      username: `testuser_${Date.now()}`,
      password_hash: 'test_hash',
      is_active: true,
      email_verified: false
    });
    console.log(`✅ Test user created: ${testUser.username} (${testUser.id})\n`);

    // Create a test category
    console.log('📁 Creating test category...');
    const testCategory: Category = await categoryDAO.create({
      user_id: testUser.id,
      name: 'Test Category',
      color: '#FF5733',
      sort_order: 1,
      is_default: false
    });
    console.log(`✅ Test category created: ${testCategory.name} (${testCategory.id})\n`);

    // Test 1: Create Todo
    console.log('➕ Test 1: Create Todo');
    const newTodo = await todoDAO.createTodo({
      category_id: testCategory.id,
      title: 'Test Todo Item',
      description: 'This is a test todo for CRUD operations',
      priority: 'high',
      status: 'pending',
      tags: ['test', 'crud'],
      due_date: new Date(Date.now() + 86400000), // Tomorrow
      metadata: { source: 'crud-test' }
    }, testUser.id);
    
    console.log('✅ Todo created successfully');
    console.log(`   📝 Title: ${newTodo.title}`);
    console.log(`   🏷️  Tags: ${newTodo.tags.join(', ')}`);
    console.log(`   📊 Status: ${newTodo.status}`);
    console.log(`   🎯 Priority: ${newTodo.priority}`);
    console.log(`   📍 Position: ${newTodo.position}\n`);

    // Test 2: Read Todo by ID
    console.log('🔍 Test 2: Read Todo by ID');
    const foundTodo = await todoDAO.findByIdForUser(newTodo.id, testUser.id);
    console.log('✅ Todo found by ID');
    console.log(`   📝 Found: ${foundTodo?.title} [${foundTodo?.status}]\n`);

    // Test 3: Update Todo
    console.log('🔄 Test 3: Update Todo');
    const updatedTodo = await todoDAO.updateTodo(newTodo.id, {
      title: 'Updated Test Todo Item',
      description: 'This todo has been updated via CRUD test',
      priority: 'low',
      status: 'in_progress'
    }, testUser.id);
    
    console.log('✅ Todo updated successfully');
    console.log(`   📝 New title: ${updatedTodo.title}`);
    console.log(`   🎯 New priority: ${updatedTodo.priority}`);
    console.log(`   📊 New status: ${updatedTodo.status}\n`);

    // Test 4: Complete Todo (status change with completed_at)
    console.log('✅ Test 4: Complete Todo');
    const completedTodo = await todoDAO.updateTodo(newTodo.id, {
      status: 'completed'
    }, testUser.id);
    
    console.log('✅ Todo marked as completed');
    console.log(`   ⏰ Completed at: ${completedTodo.completed_at}\n`);

    // Test 5: Create another todo for list operations
    console.log('➕ Test 5: Create second Todo');
    const secondTodo = await todoDAO.createTodo({
      category_id: testCategory.id,
      title: 'Second Test Todo',
      description: 'Another test todo',
      priority: 'medium',
      status: 'pending',
      tags: ['test', 'second']
    }, testUser.id);
    console.log(`✅ Second todo created: ${secondTodo.title}\n`);

    // Test 6: List todos by user
    console.log('📋 Test 6: List Todos by User');
    const userTodos = await todoDAO.findByUserId(testUser.id, { page: 1, limit: 10 });
    console.log('✅ User todos retrieved');
    console.log(`   📊 Total: ${userTodos.total} todos`);
    userTodos.data.forEach(todo => {
      console.log(`   • ${todo.title} [${todo.status}] - Position: ${todo.position}`);
    });
    console.log();

    // Test 7: Soft Delete Todo
    console.log('🗑️ Test 7: Soft Delete Todo');
    const softDeletedTodo = await todoDAO.softDeleteTodo(secondTodo.id, testUser.id);
    console.log('✅ Todo soft deleted');
    console.log(`   🗑️ Deleted: ${softDeletedTodo.title} (is_deleted: ${softDeletedTodo.is_deleted})\n`);

    // Test 8: Verify soft deleted todo is not found
    console.log('🔍 Test 8: Verify Soft Deleted Todo Not Found');
    const deletedTodoSearch = await todoDAO.findByIdForUser(secondTodo.id, testUser.id);
    console.log('✅ Soft deleted todo not found (as expected)');
    console.log(`   📝 Result: ${deletedTodoSearch ? 'Found (unexpected)' : 'Not found (correct)'}\n`);

    // Test 9: Hard Delete Todo
    console.log('🗑️ Test 9: Hard Delete Todo');
    const deleteResult = await todoDAO.deleteTodo(newTodo.id, testUser.id);
    console.log('✅ Todo hard deleted');
    console.log(`   🗑️ Delete result: ${deleteResult}\n`);

    // Test 10: Verify hard deleted todo is not found
    console.log('🔍 Test 10: Verify Hard Deleted Todo Not Found');
    const hardDeletedTodoSearch = await todoDAO.findByIdForUser(newTodo.id, testUser.id);
    console.log('✅ Hard deleted todo not found (as expected)');
    console.log(`   📝 Result: ${hardDeletedTodoSearch ? 'Found (unexpected)' : 'Not found (correct)'}\n`);

    // Test 11: Authorization test - try to access todo with wrong user
    console.log('🔒 Test 11: Authorization Test');
    try {
      await todoDAO.findByIdForUser('non-existent-id', 'wrong-user-id');
      console.log('❌ Authorization test failed - should not have found todo');
    } catch (error) {
      console.log('✅ Authorization working correctly');
      console.log(`   🔒 Access denied as expected\n`);
    }

    console.log('🎉 All TodoDAO CRUD tests passed successfully!\n');

    // Cleanup
    console.log('🧹 Cleaning up test data...');
    // Delete any remaining todos first (including soft-deleted ones)
    const allUserTodos = await dbConnection.executeQuery(
      'DELETE FROM todos WHERE user_id = $1',
      [testUser.id]
    );
    await categoryDAO.delete(testCategory.id);
    await userDAO.delete(testUser.id);
    console.log('✅ Test data cleaned up\n');

  } catch (error) {
    console.error('❌ TodoDAO CRUD test failed:', error);
    throw error;
  } finally {
    await dbConnection.close();
    console.log('🔒 Database connection closed');
  }
}

// Run the test
testTodoCRUD()
  .then(() => {
    console.log('✅ TodoDAO CRUD test completed successfully');
    process.exit(0);
  })
  .catch((error) => {
    console.error('❌ TodoDAO CRUD test failed:', error);
    process.exit(1);
  });
