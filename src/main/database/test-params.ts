import { dbConnection } from './connection';

async function testParameterBinding(): Promise<void> {
  try {
    console.log('🔍 Testing parameter binding...\n');

    // Initialize connection
    await dbConnection.initialize();

    // Test 1: Simple query without parameters
    console.log('1. Testing query without parameters...');
    const result1 = await dbConnection.executeQuery('SELECT 1 as test_value');
    console.log('✅ Result:', result1.rows[0]);

    // Test 2: Query with single parameter using ?
    console.log('\n2. Testing query with single parameter (?)...');
    const result2 = await dbConnection.executeQuery('SELECT ? as param_value', ['hello']);
    console.log('✅ Result:', result2.rows[0]);

    // Test 3: Query with multiple parameters using ?
    console.log('\n3. Testing query with multiple parameters (?)...');
    const result3 = await dbConnection.executeQuery('SELECT ? as param1, ? as param2, ? as param3', ['value1', 'value2', 'value3']);
    console.log('✅ Result:', result3.rows[0]);

    // Test 4: Create temporary table and insert with parameters
    console.log('\n4. Testing INSERT with parameters...');
    await dbConnection.executeQuery('CREATE TEMPORARY TABLE test_params (id INTEGER, name VARCHAR, active BOOLEAN)');
    
    const insertResult = await dbConnection.executeQuery(
      'INSERT INTO test_params (id, name, active) VALUES (?, ?, ?)',
      [1, 'Test Name', true]
    );
    console.log('✅ Insert result:', insertResult);

    // Test 5: Select from the table with parameters
    console.log('\n5. Testing SELECT with parameters...');
    const selectResult = await dbConnection.executeQuery(
      'SELECT * FROM test_params WHERE id = ? AND active = ?',
      [1, true]
    );
    console.log('✅ Select result:', selectResult.rows[0]);

    // Test 6: Test with different data types
    console.log('\n6. Testing different data types...');
    await dbConnection.executeQuery('CREATE TEMPORARY TABLE test_types (str VARCHAR, num INTEGER, bool BOOLEAN, dt TIMESTAMP)');
    
    const now = new Date();
    const insertTypesResult = await dbConnection.executeQuery(
      'INSERT INTO test_types (str, num, bool, dt) VALUES (?, ?, ?, ?)',
      ['test string', 42, false, now.toISOString()]
    );
    console.log('✅ Insert types result:', insertTypesResult);

    const selectTypesResult = await dbConnection.executeQuery('SELECT * FROM test_types');
    console.log('✅ Select types result:', selectTypesResult.rows[0]);

    console.log('\n🎉 All parameter binding tests passed!');

  } catch (error) {
    console.error('\n❌ Parameter binding test failed:', error);
    throw error;
  } finally {
    try {
      await dbConnection.close();
      console.log('\n🔒 Database connection closed');
    } catch (error) {
      console.warn('Warning: Failed to close database connection:', error);
    }
  }
}

// Run the test if this file is executed directly
if (require.main === module) {
  testParameterBinding()
    .then(() => {
      console.log('\n✅ Parameter binding test completed successfully');
      process.exit(0);
    })
    .catch((error) => {
      console.error('\n❌ Parameter binding test failed:', error);
      process.exit(1);
    });
}

export { testParameterBinding };
