import { dbConnection } from './connection';
import { databaseSchema } from './schema';
import { config } from '@main/utils/config';

async function testDatabaseConnection(): Promise<void> {
  try {
    console.log('🔍 Testing database connection and operations...\n');

    // Test 1: Initialize connection
    console.log('1. Testing database initialization...');
    await dbConnection.initialize();
    console.log('✅ Database connection initialized successfully');

    // Test 2: Health check
    console.log('\n2. Testing health check...');
    const isHealthy = await dbConnection.healthCheck();
    console.log(`✅ Database health check: ${isHealthy ? 'HEALTHY' : 'UNHEALTHY'}`);

    // Test 3: Connection status
    console.log('\n3. Testing connection status...');
    const status = dbConnection.getConnectionStatus();
    console.log('✅ Connection status:', {
      isConnected: status.isConnected,
      connectionAttempts: status.connectionAttempts,
      lastConnectionAt: status.lastConnectionAt?.toISOString(),
    });

    // Test 4: Basic query execution
    console.log('\n4. Testing basic query execution...');
    const testQuery = await dbConnection.executeQuery('SELECT 1 as test_value, \'Hello DuckDB\' as message');
    console.log('✅ Basic query result:', testQuery.rows[0]);

    // Test 5: Test table existence
    console.log('\n5. Testing table existence...');
    const tables = await dbConnection.executeQuery(`
      SELECT table_name 
      FROM information_schema.tables 
      WHERE table_schema = 'main' 
      ORDER BY table_name
    `);
    console.log('✅ Tables found:', tables.rows.map(row => row.table_name));

    // Test 6: Test schema validation
    console.log('\n6. Testing schema validation...');
    const isValidSchema = await databaseSchema.validateSchema();
    console.log(`✅ Schema validation: ${isValidSchema ? 'VALID' : 'INVALID'}`);

    // Test 7: Test transaction
    console.log('\n7. Testing transaction support...');
    await dbConnection.executeTransaction(async (execute) => {
      await execute('CREATE TEMPORARY TABLE test_transaction (id INTEGER, name VARCHAR)');
      await execute('INSERT INTO test_transaction VALUES (1, \'test\')');
      const result = await execute('SELECT * FROM test_transaction');
      console.log('✅ Transaction test result:', result.rows[0]);
      return result;
    });

    // Test 8: Test database statistics
    console.log('\n8. Testing database statistics...');
    const stats = await dbConnection.getStats();
    console.log('✅ Database statistics:', {
      isConnected: stats.isConnected,
      tableCount: stats.tableCount,
      connectionAttempts: stats.connectionAttempts,
    });

    // Test 9: Test configuration
    console.log('\n9. Testing configuration...');
    const dbConfig = config.getDatabaseConfig();
    console.log('✅ Database configuration:', {
      path: dbConfig.path,
      connectionTimeout: dbConfig.connectionTimeout,
      maxConnections: dbConfig.maxConnections,
    });

    console.log('\n🎉 All database tests passed successfully!');

  } catch (error) {
    console.error('\n❌ Database test failed:', error);
    throw error;
  } finally {
    // Clean up
    try {
      await dbConnection.close();
      console.log('\n🔒 Database connection closed');
    } catch (error) {
      console.warn('Warning: Failed to close database connection:', error);
    }
  }
}

// Run the test if this file is executed directly
if (require.main === module) {
  testDatabaseConnection()
    .then(() => {
      console.log('\n✅ Database connection test completed successfully');
      process.exit(0);
    })
    .catch((error) => {
      console.error('\n❌ Database connection test failed:', error);
      process.exit(1);
    });
}

export { testDatabaseConnection };
