import { dbConnection } from './connection';
import { databaseSchema } from './schema';

async function testCrudOperations(): Promise<void> {
  try {
    console.log('🔍 Testing CRUD operations...\n');

    // Initialize connection
    await dbConnection.initialize();

    // Test 1: Create a test user
    console.log('1. Testing user creation...');
    const createUserResult = await dbConnection.executeQuery(`
      INSERT INTO users (username, password_hash, is_active, email_verified)
      VALUES (?, ?, ?, ?)
      RETURNING id, username, created_at
    `, ['testuser', 'hashed_password_123', true, true]);
    
    const userId = createUserResult.rows[0].id;
    console.log('✅ User created:', createUserResult.rows[0]);

    // Test 2: Create user profile
    console.log('\n2. Testing user profile creation...');
    const createProfileResult = await dbConnection.executeQuery(`
      INSERT INTO user_profiles (user_id, full_name, email, theme_preference)
      VALUES (?, ?, ?, ?)
      RETURNING user_id, full_name, email
    `, [userId, 'Test User', '<EMAIL>', 'frutiger-aero']);
    
    console.log('✅ User profile created:', createProfileResult.rows[0]);

    // Test 3: Create default categories for the user
    console.log('\n3. Testing category creation...');
    await databaseSchema.createDefaultCategories(userId);
    
    const categoriesResult = await dbConnection.executeQuery(`
      SELECT id, name, color, is_default
      FROM categories
      WHERE user_id = ?
      ORDER BY sort_order
    `, [userId]);
    
    console.log('✅ Categories created:', categoriesResult.rows);

    // Test 4: Create a test todo
    console.log('\n4. Testing todo creation...');
    const categoryId = categoriesResult.rows[0].id; // Use first category
    const createTodoResult = await dbConnection.executeQuery(`
      INSERT INTO todos (user_id, category_id, title, description, status, priority)
      VALUES (?, ?, ?, ?, ?, ?)
      RETURNING id, title, status, priority, created_at
    `, [userId, categoryId, 'Test Todo Item', 'This is a test todo description', 'pending', 'medium']);
    
    const todoId = createTodoResult.rows[0].id;
    console.log('✅ Todo created:', createTodoResult.rows[0]);

    // Test 5: Read operations
    console.log('\n5. Testing read operations...');
    const todosResult = await dbConnection.executeQuery(`
      SELECT t.id, t.title, t.description, t.status, t.priority, c.name as category_name
      FROM todos t
      LEFT JOIN categories c ON t.category_id = c.id
      WHERE t.user_id = ? AND t.is_deleted = FALSE
    `, [userId]);
    
    console.log('✅ Todos retrieved:', todosResult.rows);

    // Test 6: Update operations
    console.log('\n6. Testing update operations...');
    const updateTodoResult = await dbConnection.executeQuery(`
      UPDATE todos
      SET status = ?, priority = ?, updated_at = CURRENT_TIMESTAMP
      WHERE id = ? AND user_id = ?
      RETURNING id, title, status, priority, updated_at
    `, ['in_progress', 'high', todoId, userId]);
    
    console.log('✅ Todo updated:', updateTodoResult.rows[0]);

    // Test 7: Transaction test with multiple operations
    console.log('\n7. Testing transaction with multiple operations...');
    await dbConnection.executeTransaction(async (execute) => {
      // Create another todo
      const todo2Result = await execute(`
        INSERT INTO todos (user_id, category_id, title, status, priority)
        VALUES (?, ?, ?, ?, ?)
        RETURNING id, title
      `, [userId, categoryId, 'Second Todo', 'pending', 'low']);

      // Update user's last login
      await execute(`
        UPDATE users
        SET last_login_at = CURRENT_TIMESTAMP
        WHERE id = ?
      `, [userId]);
      
      console.log('✅ Transaction completed - created todo:', todo2Result.rows[0]);
      return todo2Result;
    });

    // Test 8: Count operations
    console.log('\n8. Testing count operations...');
    const countsResult = await dbConnection.executeQuery(`
      SELECT
        (SELECT COUNT(*) FROM users WHERE id = ?) as user_count,
        (SELECT COUNT(*) FROM categories WHERE user_id = ?) as category_count,
        (SELECT COUNT(*) FROM todos WHERE user_id = ? AND is_deleted = FALSE) as todo_count
    `, [userId, userId, userId]);
    
    console.log('✅ Counts:', countsResult.rows[0]);

    // Test 9: Soft delete (mark as deleted)
    console.log('\n9. Testing soft delete...');
    const deleteResult = await dbConnection.executeQuery(`
      UPDATE todos
      SET is_deleted = TRUE, updated_at = CURRENT_TIMESTAMP
      WHERE id = ? AND user_id = ?
      RETURNING id, title, is_deleted
    `, [todoId, userId]);
    
    console.log('✅ Todo soft deleted:', deleteResult.rows[0]);

    // Test 10: Verify soft delete
    console.log('\n10. Testing soft delete verification...');
    const activeCountResult = await dbConnection.executeQuery(`
      SELECT COUNT(*) as active_count
      FROM todos
      WHERE user_id = ? AND is_deleted = FALSE
    `, [userId]);
    
    console.log('✅ Active todos after soft delete:', activeCountResult.rows[0]);

    console.log('\n🎉 All CRUD operations completed successfully!');

  } catch (error) {
    console.error('\n❌ CRUD test failed:', error);
    throw error;
  } finally {
    // Clean up
    try {
      await dbConnection.close();
      console.log('\n🔒 Database connection closed');
    } catch (error) {
      console.warn('Warning: Failed to close database connection:', error);
    }
  }
}

// Run the test if this file is executed directly
if (require.main === module) {
  testCrudOperations()
    .then(() => {
      console.log('\n✅ CRUD operations test completed successfully');
      process.exit(0);
    })
    .catch((error) => {
      console.error('\n❌ CRUD operations test failed:', error);
      process.exit(1);
    });
}

export { testCrudOperations };
