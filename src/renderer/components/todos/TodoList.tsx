import React from 'react';
import { motion } from 'framer-motion';
import { TodoItem } from './TodoItem';

// Mock todo data
const mockTodos = [
  {
    id: '1',
    title: 'Complete Frutiger Aero UI implementation',
    completed: false,
    priority: 'high',
    dueDate: new Date(Date.now() + 86400000), // Tomorrow
    category: 'Work',
  },
  {
    id: '2',
    title: 'Review design system documentation',
    completed: true,
    priority: 'medium',
    dueDate: new Date(),
    category: 'Documentation',
  },
  {
    id: '3',
    title: 'Implement glassmorphism effects',
    completed: false,
    priority: 'medium',
    dueDate: new Date(Date.now() + 172800000), // In 2 days
    category: 'Development',
  },
  {
    id: '4',
    title: 'Test responsive design on mobile',
    completed: false,
    priority: 'low',
    dueDate: new Date(Date.now() + 259200000), // In 3 days
    category: 'Testing',
  },
];

export const TodoList: React.FC = () => {
  return (
    <div className="w-full h-full overflow-y-auto">
      <div className="space-y-3">
        {mockTodos.map((todo, index) => (
          <motion.div
            key={todo.id}
            initial={{ opacity: 0, y: 20 }}
            animate={{ opacity: 1, y: 0 }}
            transition={{ delay: index * 0.1 }}
          >
            <TodoItem todo={todo} />
          </motion.div>
        ))}
      </div>
      
      {mockTodos.length === 0 && (
        <div className="h-full flex items-center justify-center">
          <div className="text-center">
            <div className="text-5xl mb-4">🍃</div>
            <h3 className="fa-heading-3 mb-2">No tasks yet</h3>
            <p className="fa-body text-fa-gray-500">
              Add your first task to get started!
            </p>
          </div>
        </div>
      )}
    </div>
  );
};