import React from 'react';
import { render, screen } from '@testing-library/react';
import { TodoItem } from '../TodoItem';

describe('TodoItem', () => {
  const mockTodo: any = {
    id: '1',
    title: 'Test Todo',
    completed: false,
    priority: 'medium',
    dueDate: new Date(),
    category: 'Work',
  };

  it('renders todo item correctly', () => {
    render(<TodoItem todo={mockTodo} />);
    
    expect(screen.getByText('Test Todo')).toBeInTheDocument();
    expect(screen.getByText('Work')).toBeInTheDocument();
  });

  it('shows completed state', () => {
    const completedTodo: any = { ...mockTodo, completed: true };
    render(<TodoItem todo={completedTodo} />);
    
    const title = screen.getByText('Test Todo');
    expect(title).toHaveClass('line-through');
  });
});