import React, { useState } from 'react';
import { motion } from 'framer-motion';
import { 
  Check, 
  Star, 
  Flag, 
  Calendar, 
  MoreHorizontal,
  Edit3,
  Trash2,
  Clock
} from 'lucide-react';

interface Todo {
  id: string;
  title: string;
  completed: boolean;
  priority: 'low' | 'medium' | 'high';
  dueDate?: Date;
  category?: string;
}

interface TodoItemProps {
  todo: Todo;
}

export const TodoItem: React.FC<TodoItemProps> = ({ todo }) => {
  const [isHovered, setIsHovered] = useState(false);
  const [isEditing, setIsEditing] = useState(false);
  const [editValue, setEditValue] = useState(todo.title);

  const handleToggleComplete = () => {
    // TODO: Update todo completion status
    console.log('Toggle complete:', todo.id);
  };

  const handleDelete = () => {
    // TODO: Delete todo
    console.log('Delete todo:', todo.id);
  };

  const handleEdit = () => {
    setIsEditing(true);
  };

  const handleSaveEdit = () => {
    // TODO: Save edited todo
    console.log('Save edit:', todo.id, editValue);
    setIsEditing(false);
  };

  const getPriorityColor = () => {
    switch (todo.priority) {
      case 'high': return 'text-fa-error';
      case 'medium': return 'text-fa-warning';
      case 'low': return 'text-fa-success';
      default: return 'text-fa-gray-400';
    }
  };

  const formatDate = (date?: Date) => {
    if (!date) return '';
    return date.toLocaleDateString('en-US', { 
      month: 'short', 
      day: 'numeric' 
    });
  };

  return (
    <div 
      className={`fa-todo-card relative transition-all duration-300 ${
        todo.completed ? 'opacity-70' : ''
      }`}
      onMouseEnter={() => setIsHovered(true)}
      onMouseLeave={() => setIsHovered(false)}
    >
      <div className="flex items-start">
        {/* Checkbox */}
        <motion.button
          whileHover={{ scale: 1.1 }}
          whileTap={{ scale: 0.9 }}
          onClick={handleToggleComplete}
          className={`flex-shrink-0 w-6 h-6 rounded-full border-2 flex items-center justify-center mr-4 mt-1 ${
            todo.completed
              ? 'bg-fa-success border-fa-success text-white'
              : 'border-fa-gray-300 hover:border-fa-blue-400'
          }`}
        >
          {todo.completed && <Check className="w-4 h-4" />}
        </motion.button>
        
        {/* Todo Content */}
        <div className="flex-1 min-w-0">
          {isEditing ? (
            <input
              type="text"
              value={editValue}
              onChange={(e) => setEditValue(e.target.value)}
              onBlur={handleSaveEdit}
              onKeyDown={(e) => e.key === 'Enter' && handleSaveEdit()}
              className="w-full bg-transparent text-fa-gray-800 focus:outline-none border-b border-fa-blue-300 pb-1"
              autoFocus
            />
          ) : (
            <>
              <h3 className={`text-lg font-medium ${
                todo.completed 
                  ? 'line-through text-fa-gray-500' 
                  : 'text-fa-gray-800'
              }`}>
                {todo.title}
              </h3>
              
              <div className="flex items-center space-x-4 mt-2">
                {todo.category && (
                  <span className="fa-caption bg-fa-white-glass px-2 py-1 rounded-full text-fa-gray-600">
                    {todo.category}
                  </span>
                )}
                
                {todo.dueDate && (
                  <div className="flex items-center text-fa-gray-500">
                    <Calendar className="w-4 h-4 mr-1" />
                    <span className="fa-caption">{formatDate(todo.dueDate)}</span>
                  </div>
                )}
                
                <div className="flex items-center">
                  <Flag className={`w-4 h-4 ${getPriorityColor()}`} />
                </div>
              </div>
            </>
          )}
        </div>
        
        {/* Action Buttons */}
        <motion.div 
          className="flex items-center space-x-1 ml-2"
          initial={{ opacity: 0, width: 0 }}
          animate={{ 
            opacity: isHovered ? 1 : 0, 
            width: isHovered ? 'auto' : 0 
          }}
        >
          <motion.button
            whileHover={{ scale: 1.1 }}
            whileTap={{ scale: 0.9 }}
            onClick={handleEdit}
            className="p-1 text-fa-gray-400 hover:text-fa-blue-500"
          >
            <Edit3 className="w-4 h-4" />
          </motion.button>
          
          <motion.button
            whileHover={{ scale: 1.1 }}
            whileTap={{ scale: 0.9 }}
            onClick={handleDelete}
            className="p-1 text-fa-gray-400 hover:text-fa-error"
          >
            <Trash2 className="w-4 h-4" />
          </motion.button>
        </motion.div>
      </div>
      
      {/* Priority indicator */}
      {todo.priority === 'high' && (
        <div className="absolute top-0 left-0 w-full h-1 bg-fa-error rounded-t-2xl"></div>
      )}
    </div>
  );
};