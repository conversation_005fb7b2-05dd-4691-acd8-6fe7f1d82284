import React, { useState } from 'react';
import { motion } from 'framer-motion';
import { Plus, Calendar, Tag } from 'lucide-react';

export const TodoInput: React.FC = () => {
  const [inputValue, setInputValue] = useState('');
  const [isFocused, setIsFocused] = useState(false);

  const handleSubmit = (e: React.FormEvent) => {
    e.preventDefault();
    if (inputValue.trim()) {
      // TODO: Add todo to store
      console.log('Adding todo:', inputValue);
      setInputValue('');
    }
  };

  return (
    <form onSubmit={handleSubmit} className="w-full">
      <div className={`fa-glass-panel transition-all duration-300 ${
        isFocused ? 'ring-2 ring-fa-blue-400 shadow-lg' : 'shadow-md'
      }`}>
        <div className="flex items-center">
          <motion.button
            whileHover={{ scale: 1.05 }}
            whileTap={{ scale: 0.95 }}
            type="submit"
            className="p-4 text-fa-blue-500 hover:text-fa-blue-600"
          >
            <Plus className="w-5 h-5" />
          </motion.button>
          
          <input
            type="text"
            value={inputValue}
            onChange={(e) => setInputValue(e.target.value)}
            onFocus={() => setIsFocused(true)}
            onBlur={() => setIsFocused(false)}
            placeholder="What needs to be done?"
            className="flex-1 bg-transparent py-4 px-2 text-fa-gray-800 placeholder-fa-gray-400 focus:outline-none"
          />
          
          <div className="flex items-center space-x-2 pr-4">
            <motion.button
              whileHover={{ scale: 1.05 }}
              whileTap={{ scale: 0.95 }}
              type="button"
              className="p-2 text-fa-gray-400 hover:text-fa-gray-600"
            >
              <Calendar className="w-4 h-4" />
            </motion.button>
            
            <motion.button
              whileHover={{ scale: 1.05 }}
              whileTap={{ scale: 0.95 }}
              type="button"
              className="p-2 text-fa-gray-400 hover:text-fa-gray-600"
            >
              <Tag className="w-4 h-4" />
            </motion.button>
          </div>
        </div>
      </div>
    </form>
  );
};