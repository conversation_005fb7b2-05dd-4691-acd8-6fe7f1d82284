import React, { useState } from 'react';
import { useAuth } from '@/renderer/contexts/AuthContext';
import { motion } from 'framer-motion';
import { User, Mail, Calendar, Lock, Save, X } from 'lucide-react';

interface UserProfileProps {
  onClose: () => void;
}

export const UserProfile: React.FC<UserProfileProps> = ({ onClose }) => {
  const { user, profile, changePassword } = useAuth();
  const [currentPassword, setCurrentPassword] = useState('');
  const [newPassword, setNewPassword] = useState('');
  const [confirmPassword, setConfirmPassword] = useState('');
  const [isChangingPassword, setIsChangingPassword] = useState(false);
  const [message, setMessage] = useState<{ type: 'success' | 'error'; text: string } | null>(null);
  const [loading, setLoading] = useState(false);

  const handlePasswordChange = async (e: React.FormEvent) => {
    e.preventDefault();
    
    if (newPassword !== confirmPassword) {
      setMessage({ type: 'error', text: 'New passwords do not match' });
      return;
    }
    
    if (newPassword.length < 12) {
      setMessage({ type: 'error', text: 'Password must be at least 12 characters long' });
      return;
    }
    
    setLoading(true);
    setMessage(null);
    
    try {
      await changePassword(currentPassword, newPassword);
      setMessage({ type: 'success', text: 'Password changed successfully' });
      setCurrentPassword('');
      setNewPassword('');
      setConfirmPassword('');
      setIsChangingPassword(false);
    } catch (err) {
      setMessage({ type: 'error', text: err instanceof Error ? err.message : 'Failed to change password' });
    } finally {
      setLoading(false);
    }
  };

  return (
    <motion.div
      initial={{ opacity: 0 }}
      animate={{ opacity: 1 }}
      exit={{ opacity: 0 }}
      className="fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center p-4 z-50"
      onClick={(e) => e.target === e.currentTarget && onClose()}
    >
      <motion.div
        initial={{ scale: 0.9, opacity: 0 }}
        animate={{ scale: 1, opacity: 1 }}
        exit={{ scale: 0.9, opacity: 0 }}
        className="fa-glass-panel-frosted rounded-2xl p-6 w-full max-w-md"
      >
        <div className="flex justify-between items-center mb-6">
          <h2 className="fa-heading-2 text-gray-800">User Profile</h2>
          <button
            onClick={onClose}
            className="text-gray-500 hover:text-gray-700"
          >
            <X className="w-6 h-6" />
          </button>
        </div>

        {message && (
          <motion.div
            initial={{ opacity: 0, y: -10 }}
            animate={{ opacity: 1, y: 0 }}
            className={`mb-4 p-3 rounded-lg text-sm ${
              message.type === 'success' 
                ? 'bg-green-50 text-green-700 border border-green-200' 
                : 'bg-red-50 text-red-700 border border-red-200'
            }`}
          >
            {message.text}
          </motion.div>
        )}

        <div className="space-y-6">
          <div className="flex flex-col items-center">
            <div className="w-20 h-20 bg-gradient-to-br from-blue-400 to-aqua-400 rounded-full flex items-center justify-center mb-4">
              <User className="w-10 h-10 text-white" />
            </div>
            <h3 className="fa-heading-3 text-gray-800">{user?.username}</h3>
            {profile?.full_name && (
              <p className="fa-body text-gray-600">{profile.full_name}</p>
            )}
          </div>

          <div className="space-y-4">
            <div className="flex items-center p-3 bg-white bg-opacity-20 rounded-lg">
              <User className="w-5 h-5 text-gray-500 mr-3" />
              <div>
                <p className="fa-caption text-gray-500">Username</p>
                <p className="fa-body text-gray-800">{user?.username}</p>
              </div>
            </div>

            {profile?.email && (
              <div className="flex items-center p-3 bg-white bg-opacity-20 rounded-lg">
                <Mail className="w-5 h-5 text-gray-500 mr-3" />
                <div>
                  <p className="fa-caption text-gray-500">Email</p>
                  <p className="fa-body text-gray-800">{profile.email}</p>
                </div>
              </div>
            )}

            <div className="flex items-center p-3 bg-white bg-opacity-20 rounded-lg">
              <Calendar className="w-5 h-5 text-gray-500 mr-3" />
              <div>
                <p className="fa-caption text-gray-500">Member since</p>
                <p className="fa-body text-gray-800">
                  {user?.created_at ? new Date(user.created_at).toLocaleDateString() : 'Unknown'}
                </p>
              </div>
            </div>
          </div>

          <div className="pt-4">
            <button
              onClick={() => setIsChangingPassword(!isChangingPassword)}
              className="w-full fa-button-glass py-3 px-4 rounded-lg font-medium text-gray-700 flex items-center justify-center"
            >
              <Lock className="w-5 h-5 mr-2" />
              {isChangingPassword ? 'Cancel' : 'Change Password'}
            </button>

            {isChangingPassword && (
              <motion.form
                initial={{ opacity: 0, height: 0 }}
                animate={{ opacity: 1, height: 'auto' }}
                exit={{ opacity: 0, height: 0 }}
                onSubmit={handlePasswordChange}
                className="mt-4 space-y-4"
              >
                <div>
                  <label htmlFor="currentPassword" className="block text-sm font-medium text-gray-700 mb-1">
                    Current Password
                  </label>
                  <input
                    id="currentPassword"
                    type="password"
                    value={currentPassword}
                    onChange={(e) => setCurrentPassword(e.target.value)}
                    className="fa-input w-full"
                    required
                  />
                </div>

                <div>
                  <label htmlFor="newPassword" className="block text-sm font-medium text-gray-700 mb-1">
                    New Password
                  </label>
                  <input
                    id="newPassword"
                    type="password"
                    value={newPassword}
                    onChange={(e) => setNewPassword(e.target.value)}
                    className="fa-input w-full"
                    required
                  />
                </div>

                <div>
                  <label htmlFor="confirmPassword" className="block text-sm font-medium text-gray-700 mb-1">
                    Confirm New Password
                  </label>
                  <input
                    id="confirmPassword"
                    type="password"
                    value={confirmPassword}
                    onChange={(e) => setConfirmPassword(e.target.value)}
                    className="fa-input w-full"
                    required
                  />
                </div>

                <motion.button
                  whileHover={{ scale: 1.02 }}
                  whileTap={{ scale: 0.98 }}
                  type="submit"
                  disabled={loading}
                  className="w-full fa-button-primary py-3 px-4 rounded-lg font-medium text-white flex items-center justify-center disabled:opacity-50 disabled:cursor-not-allowed"
                >
                  {loading ? (
                    <>
                      <div className="animate-spin rounded-full h-4 w-4 border-b-2 border-white mr-2"></div>
                      Changing...
                    </>
                  ) : (
                    <>
                      <Save className="w-4 h-4 mr-2" />
                      Save Changes
                    </>
                  )}
                </motion.button>
              </motion.form>
            )}
          </div>
        </div>
      </motion.div>
    </motion.div>
  );
};