import React from 'react';
import { render, screen, fireEvent, waitFor } from '@testing-library/react';
import { AuthProvider, useAuth } from '@/renderer/contexts/AuthContext';
import { authService } from '@/renderer/services/auth.service';

// Mock the authService
jest.mock('@/renderer/services/auth.service', () => ({
  authService: {
    initialize: jest.fn(),
    login: jest.fn(),
    register: jest.fn(),
    logout: jest.fn(),
    validateSession: jest.fn(),
    refreshSession: jest.fn(),
    changePassword: jest.fn(),
    getSession: jest.fn(),
    getUser: jest.fn(),
    getProfile: jest.fn(),
    requiresMultiFactorAuth: jest.fn(),
    verifyMFA: jest.fn(),
  },
}));

// Test component to access auth context
const TestComponent: React.FC = () => {
  const {
    isAuthenticated,
    login,
    register,
    logout,
    loading,
    error,
  } = useAuth();

  return (
    <div>
      <div data-testid="auth-status">
        {isAuthenticated ? 'Authenticated' : 'Not Authenticated'}
      </div>
      <div data-testid="loading">{loading ? 'Loading' : 'Not Loading'}</div>
      <div data-testid="error">{error || 'No Error'}</div>
      <button data-testid="login-button" onClick={() => login('testuser', 'password123')}>
        Login
      </button>
      <button data-testid="register-button" onClick={() => register('testuser', 'password123')}>
        Register
      </button>
      <button data-testid="logout-button" onClick={() => logout()}>
        Logout
      </button>
    </div>
  );
};

describe('Authentication Flow', () => {
  beforeEach(() => {
    jest.clearAllMocks();
  });

  it('should render auth provider and show initial state', () => {
    (authService.validateSession as jest.Mock).mockResolvedValue(true);
    (authService.getSession as jest.Mock).mockReturnValue(null);
    (authService.getUser as jest.Mock).mockReturnValue(null);
    (authService.getProfile as jest.Mock).mockReturnValue(null);
    (authService.requiresMultiFactorAuth as jest.Mock).mockReturnValue(false);

    render(
      <AuthProvider>
        <TestComponent />
      </AuthProvider>
    );

    expect(screen.getByTestId('auth-status')).toHaveTextContent('Not Authenticated');
    expect(screen.getByTestId('loading')).toHaveTextContent('Loading');
  });

  it('should handle successful login', async () => {
    (authService.validateSession as jest.Mock).mockResolvedValue(true);
    (authService.getSession as jest.Mock).mockReturnValue(null);
    (authService.getUser as jest.Mock).mockReturnValue(null);
    (authService.getProfile as jest.Mock).mockReturnValue(null);
    (authService.requiresMultiFactorAuth as jest.Mock).mockReturnValue(false);

    const mockSession = {
      sessionId: 'test-session-id',
      userId: 'test-user-id',
      username: 'testuser',
      createdAt: new Date(),
      expiresAt: new Date(Date.now() + 3600000),
      deviceFingerprint: 'test-fingerprint',
      ipAddress: '127.0.0.1',
      userAgent: 'test-agent',
      isActive: true,
      permissions: ['read', 'write']
    };

    const mockUser = {
      id: 'test-user-id',
      username: 'testuser',
      password_hash: 'hashed-password',
      created_at: new Date(),
      updated_at: new Date(),
      is_active: true,
      email_verified: false
    };

    const mockProfile = {
      user_id: 'test-user-id',
      full_name: 'Test User',
      email: '<EMAIL>',
      theme_preference: 'light',
      notification_enabled: true,
      timezone: 'UTC',
      language_preference: 'en'
    };

    (authService.login as jest.Mock).mockResolvedValue(mockSession);
    (authService.getSession as jest.Mock).mockReturnValue(mockSession);
    (authService.getUser as jest.Mock).mockReturnValue(mockUser);
    (authService.getProfile as jest.Mock).mockReturnValue(mockProfile);

    render(
      <AuthProvider>
        <TestComponent />
      </AuthProvider>
    );

    // Wait for initial loading to complete
    await waitFor(() => {
      expect(screen.getByTestId('loading')).toHaveTextContent('Not Loading');
    });

    // Click login button
    fireEvent.click(screen.getByTestId('login-button'));

    // Wait for login to complete
    await waitFor(() => {
      expect(screen.getByTestId('auth-status')).toHaveTextContent('Authenticated');
    });

    expect(authService.login).toHaveBeenCalledWith({
      username: 'testuser',
      password: 'password123'
    });
  });

  it('should handle login error', async () => {
    (authService.validateSession as jest.Mock).mockResolvedValue(true);
    (authService.getSession as jest.Mock).mockReturnValue(null);
    (authService.getUser as jest.Mock).mockReturnValue(null);
    (authService.getProfile as jest.Mock).mockReturnValue(null);
    (authService.requiresMultiFactorAuth as jest.Mock).mockReturnValue(false);

    (authService.login as jest.Mock).mockRejectedValue(new Error('Invalid credentials'));

    render(
      <AuthProvider>
        <TestComponent />
      </AuthProvider>
    );

    // Wait for initial loading to complete
    await waitFor(() => {
      expect(screen.getByTestId('loading')).toHaveTextContent('Not Loading');
    });

    // Click login button
    fireEvent.click(screen.getByTestId('login-button'));

    // Wait for error to appear
    await waitFor(() => {
      expect(screen.getByTestId('error')).toHaveTextContent('Invalid credentials');
    });

    expect(screen.getByTestId('auth-status')).toHaveTextContent('Not Authenticated');
  });

  it('should handle successful logout', async () => {
    (authService.validateSession as jest.Mock).mockResolvedValue(true);
    (authService.requiresMultiFactorAuth as jest.Mock).mockReturnValue(false);

    const mockSession = {
      sessionId: 'test-session-id',
      userId: 'test-user-id',
      username: 'testuser',
      createdAt: new Date(),
      expiresAt: new Date(Date.now() + 3600000),
      deviceFingerprint: 'test-fingerprint',
      ipAddress: '127.0.0.1',
      userAgent: 'test-agent',
      isActive: true,
      permissions: ['read', 'write']
    };

    const mockUser = {
      id: 'test-user-id',
      username: 'testuser',
      password_hash: 'hashed-password',
      created_at: new Date(),
      updated_at: new Date(),
      is_active: true,
      email_verified: false
    };

    const mockProfile = {
      user_id: 'test-user-id',
      full_name: 'Test User',
      email: '<EMAIL>',
      theme_preference: 'light',
      notification_enabled: true,
      timezone: 'UTC',
      language_preference: 'en'
    };

    (authService.login as jest.Mock).mockResolvedValue(mockSession);
    (authService.getSession as jest.Mock)
      .mockReturnValueOnce(null) // Initial state
      .mockReturnValueOnce(mockSession); // After login
    (authService.getUser as jest.Mock)
      .mockReturnValueOnce(null) // Initial state
      .mockReturnValueOnce(mockUser); // After login
    (authService.getProfile as jest.Mock)
      .mockReturnValueOnce(null) // Initial state
      .mockReturnValueOnce(mockProfile); // After login

    (authService.logout as jest.Mock).mockResolvedValue(undefined);

    render(
      <AuthProvider>
        <TestComponent />
      </AuthProvider>
    );

    // Wait for initial loading to complete
    await waitFor(() => {
      expect(screen.getByTestId('loading')).toHaveTextContent('Not Loading');
    });

    // Click login button
    fireEvent.click(screen.getByTestId('login-button'));

    // Wait for login to complete
    await waitFor(() => {
      expect(screen.getByTestId('auth-status')).toHaveTextContent('Authenticated');
    });

    // Click logout button
    fireEvent.click(screen.getByTestId('logout-button'));

    // Wait for logout to complete
    await waitFor(() => {
      expect(screen.getByTestId('auth-status')).toHaveTextContent('Not Authenticated');
    });

    expect(authService.logout).toHaveBeenCalled();
  });
});