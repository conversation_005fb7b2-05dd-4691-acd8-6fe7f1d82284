import React, { useState } from 'react';
import { motion } from 'framer-motion';
import { Shield, Smartphone, Mail, X } from 'lucide-react';

interface MultiFactorAuthProps {
  onClose: () => void;
  onVerify: (code: string) => Promise<void>;
}

export const MultiFactorAuth: React.FC<MultiFactorAuthProps> = ({ onClose, onVerify }) => {
  const [code, setCode] = useState('');
  const [isLoading, setIsLoading] = useState(false);
  const [error, setError] = useState<string | null>(null);
  const [method, setMethod] = useState<'totp' | 'email' | 'sms'>('totp');

  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault();
    setIsLoading(true);
    setError(null);
    
    try {
      await onVerify(code);
      onClose();
    } catch (err) {
      setError(err instanceof Error ? err.message : 'Verification failed');
    } finally {
      setIsLoading(false);
    }
  };

  const handleResendCode = async () => {
    setIsLoading(true);
    setError(null);
    
    try {
      // In a real implementation, this would trigger sending a new code
      // For now, we'll just simulate the process
      await new Promise(resolve => setTimeout(resolve, 1000));
      setError('Code sent successfully');
    } catch (err) {
      setError('Failed to send code');
    } finally {
      setIsLoading(false);
    }
  };

  return (
    <motion.div
      initial={{ opacity: 0 }}
      animate={{ opacity: 1 }}
      exit={{ opacity: 0 }}
      className="fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center p-4 z-50"
      onClick={(e) => e.target === e.currentTarget && onClose()}
    >
      <motion.div
        initial={{ scale: 0.9, opacity: 0 }}
        animate={{ scale: 1, opacity: 1 }}
        exit={{ scale: 0.9, opacity: 0 }}
        className="fa-glass-panel-frosted rounded-2xl p-6 w-full max-w-md"
      >
        <div className="flex justify-between items-center mb-6">
          <h2 className="fa-heading-2 text-gray-800">Two-Factor Authentication</h2>
          <button
            onClick={onClose}
            className="text-gray-500 hover:text-gray-700"
          >
            <X className="w-6 h-6" />
          </button>
        </div>

        {error && (
          <motion.div
            initial={{ opacity: 0, y: -10 }}
            animate={{ opacity: 1, y: 0 }}
            className="mb-4 p-3 bg-red-50 border border-red-200 rounded-lg text-red-700 text-sm"
          >
            {error}
          </motion.div>
        )}

        <div className="mb-6">
          <p className="fa-body text-gray-600 mb-4">
            Enter the verification code from your authenticator app
          </p>
          
          <div className="flex space-x-2 mb-6">
            <button
              onClick={() => setMethod('totp')}
              className={`flex-1 py-2 px-3 rounded-lg text-sm font-medium flex items-center justify-center ${
                method === 'totp' 
                  ? 'bg-blue-100 text-blue-700 border border-blue-200' 
                  : 'bg-white bg-opacity-20 text-gray-700'
              }`}
            >
              <Smartphone className="w-4 h-4 mr-2" />
              Authenticator App
            </button>
            
            <button
              onClick={() => setMethod('email')}
              className={`flex-1 py-2 px-3 rounded-lg text-sm font-medium flex items-center justify-center ${
                method === 'email' 
                  ? 'bg-blue-100 text-blue-700 border border-blue-200' 
                  : 'bg-white bg-opacity-20 text-gray-700'
              }`}
            >
              <Mail className="w-4 h-4 mr-2" />
              Email
            </button>
          </div>
        </div>

        <form onSubmit={handleSubmit} className="space-y-6">
          <div>
            <label htmlFor="code" className="block text-sm font-medium text-gray-700 mb-2">
              Verification Code
            </label>
            <input
              id="code"
              type="text"
              value={code}
              onChange={(e) => setCode(e.target.value)}
              className="fa-input w-full text-center text-lg tracking-widest"
              placeholder="000000"
              maxLength={6}
              required
            />
          </div>

          <div className="flex space-x-3">
            <motion.button
              whileHover={{ scale: 1.02 }}
              whileTap={{ scale: 0.98 }}
              type="button"
              onClick={handleResendCode}
              disabled={isLoading}
              className="flex-1 fa-button-glass py-3 px-4 rounded-lg font-medium text-gray-700 disabled:opacity-50 disabled:cursor-not-allowed"
            >
              Resend Code
            </motion.button>
            
            <motion.button
              whileHover={{ scale: 1.02 }}
              whileTap={{ scale: 0.98 }}
              type="submit"
              disabled={isLoading || code.length !== 6}
              className="flex-1 fa-button-primary py-3 px-4 rounded-lg font-medium text-white disabled:opacity-50 disabled:cursor-not-allowed"
            >
              {isLoading ? (
                <div className="flex items-center justify-center">
                  <div className="animate-spin rounded-full h-5 w-5 border-b-2 border-white mr-2"></div>
                  Verifying...
                </div>
              ) : (
                'Verify'
              )}
            </motion.button>
          </div>
        </form>

        <div className="mt-6 pt-6 border-t border-gray-200">
          <div className="flex items-center text-sm text-gray-500">
            <Shield className="w-4 h-4 mr-2" />
            <span>Two-factor authentication adds an extra layer of security to your account</span>
          </div>
        </div>
      </motion.div>
    </motion.div>
  );
};