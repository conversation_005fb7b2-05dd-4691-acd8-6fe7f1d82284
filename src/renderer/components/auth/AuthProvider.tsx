import React, { useState } from 'react';
import { LoginForm } from './LoginForm';
import { RegisterForm } from './RegisterForm';
import { PasswordReset } from './PasswordReset';
import { motion, AnimatePresence } from 'framer-motion';

type AuthView = 'login' | 'register' | 'reset';

export const AuthProvider: React.FC = () => {
  const [currentView, setCurrentView] = useState<AuthView>('login');

  const handleSwitchToRegister = () => {
    setCurrentView('register');
  };

  const handleSwitchToLogin = () => {
    setCurrentView('login');
  };

  const handleSwitchToReset = () => {
    setCurrentView('reset');
  };

  const handleBackToLogin = () => {
    setCurrentView('login');
  };

  return (
    <div className="min-h-screen flex items-center justify-center bg-gradient-to-br from-blue-50 via-aqua-50 to-blue-100 p-4">
      <AnimatePresence mode="wait">
        <motion.div
          key={currentView}
          initial={{ opacity: 0, x: currentView === 'login' ? -20 : currentView === 'register' ? 0 : 20 }}
          animate={{ opacity: 1, x: 0 }}
          exit={{ opacity: 0, x: currentView === 'login' ? 20 : currentView === 'register' ? 0 : -20 }}
          transition={{ duration: 0.3 }}
          className="w-full"
        >
          {currentView === 'login' && (
            <LoginForm
              onSwitchToRegister={handleSwitchToReset}
            />
          )}
          {currentView === 'register' && (
            <RegisterForm onSwitchToLogin={handleSwitchToLogin} />
          )}
          {currentView === 'reset' && (
            <PasswordReset onBackToLogin={handleBackToLogin} />
          )}
        </motion.div>
      </AnimatePresence>
    </div>
  );
};