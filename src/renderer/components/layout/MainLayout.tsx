import React, { useState } from 'react';
import { motion } from 'framer-motion';
import { useTheme } from '../../providers/ThemeProvider';
import { TitleBar } from './TitleBar';
import { Sidebar } from './Sidebar';
import { TodoList } from '../todos/TodoList';
import { TodoInput } from '../todos/TodoInput';

interface MainLayoutProps {
  systemInfo?: any;
}

export const MainLayout: React.FC<MainLayoutProps> = ({ systemInfo }) => {
  const { theme, toggleTheme } = useTheme();
  const [sidebarOpen, setSidebarOpen] = useState(true);

  return (
    <div className="w-full h-full flex flex-col bg-transparent">
      {/* Title Bar */}
      <TitleBar 
        systemInfo={systemInfo} 
        onToggleTheme={toggleTheme}
        theme={theme}
      />
      
      <div className="flex flex-1 overflow-hidden">
        {/* Sidebar */}
        <motion.div
          className={`h-full ${sidebarOpen ? 'w-64' : 'w-0'}`}
          animate={{ width: sidebarOpen ? 256 : 0 }}
          transition={{ duration: 0.3, ease: [0.4, 0, 0.2, 1] }}
        >
          <Sidebar />
        </motion.div>
        
        {/* Main Content */}
        <div className="flex-1 flex flex-col overflow-hidden p-6">
          <div className="fa-glass-panel-frosted flex-1 flex flex-col rounded-2xl p-6">
            {/* Header */}
            <div className="mb-6">
              <h1 className="fa-heading-1 mb-2">My Tasks</h1>
              <p className="fa-body text-fa-gray-600">Stay organized and productive</p>
            </div>
            
            {/* Todo Input */}
            <div className="mb-6">
              <TodoInput />
            </div>
            
            {/* Todo List */}
            <div className="flex-1 overflow-hidden">
              <TodoList />
            </div>
          </div>
        </div>
      </div>
    </div>
  );
};