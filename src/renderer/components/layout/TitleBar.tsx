import React, { useState } from 'react';
import { motion } from 'framer-motion';
import { useTheme } from '../../providers/ThemeProvider';
import { useAuth } from '../../contexts/AuthContext';
import { Sun, Moon, Menu, X, User } from 'lucide-react';
import { UserProfile } from '../auth/UserProfile';

interface TitleBarProps {
  systemInfo?: any;
  onToggleTheme: () => void;
  theme: 'light' | 'dark';
}

export const TitleBar: React.FC<TitleBarProps> = ({ systemInfo, onToggleTheme, theme }) => {
  const { user, logout } = useAuth();
  const [showProfile, setShowProfile] = useState(false);

  return (
    <>
      <div className="w-full h-12 flex items-center justify-between px-4 bg-fa-white-glass backdrop-blur-xl border-b border-fa-white-frosted">
        <div className="flex items-center space-x-3">
          <motion.button
            whileHover={{ scale: 1.05 }}
            whileTap={{ scale: 0.95 }}
            className="p-2 rounded-lg fa-hover-lift"
            onClick={onToggleTheme}
          >
            {theme === 'light' ? (
              <Sun className="w-5 h-5 text-fa-blue-600" />
            ) : (
              <Moon className="w-5 h-5 text-fa-aqua-400" />
            )}
          </motion.button>
          
          <h1 className="fa-heading-3 font-bold text-fa-gray-800">Modern Todo</h1>
        </div>
        
        <div className="flex items-center space-x-3">
          {user && (
            <div className="flex items-center space-x-2">
              <motion.button
                whileHover={{ scale: 1.05 }}
                whileTap={{ scale: 0.95 }}
                className="p-2 rounded-lg fa-hover-lift flex items-center text-fa-gray-700"
                onClick={() => setShowProfile(true)}
              >
                <User className="w-5 h-5" />
                <span className="ml-2 text-sm font-medium hidden md:inline">
                  {user.username}
                </span>
              </motion.button>
              
              <motion.button
                whileHover={{ scale: 1.05 }}
                whileTap={{ scale: 0.95 }}
                className="p-2 rounded-lg fa-hover-lift text-fa-gray-700"
                onClick={logout}
              >
                <span className="text-sm font-medium">Logout</span>
              </motion.button>
            </div>
          )}
          
          <div className="text-xs text-fa-gray-500">
            {systemInfo?.platform} • {systemInfo?.version}
          </div>
        </div>
      </div>
      
      {showProfile && <UserProfile onClose={() => setShowProfile(false)} />}
    </>
  );
};