import React from 'react';
import { motion } from 'framer-motion';
import { 
  Home, 
  List, 
  Calendar, 
  Tag, 
  Settings, 
  User, 
  Search,
  Plus,
  Folder,
  Heart,
  Zap
} from 'lucide-react';

export const Sidebar: React.FC = () => {
  const menuItems = [
    { icon: Home, label: 'Dashboard', active: true },
    { icon: List, label: 'All Tasks' },
    { icon: Calendar, label: 'Today' },
    { icon: Folder, label: 'Projects' },
    { icon: Tag, label: 'Tags' },
    { icon: Heart, label: 'Favorites' },
    { icon: Zap, label: 'Quick Add' },
  ];

  const categories = [
    { name: 'Personal', count: 12 },
    { name: 'Work', count: 8 },
    { name: 'Shopping', count: 3 },
    { name: 'Health', count: 5 },
  ];

  return (
    <div className="h-full fa-glass-panel rounded-r-2xl flex flex-col">
      {/* Sidebar Header */}
      <div className="p-4 border-b border-fa-white-frosted">
        <h2 className="fa-heading-3 font-bold text-fa-gray-800 mb-2">Navigation</h2>
      </div>
      
      {/* Main Menu */}
      <div className="flex-1 overflow-y-auto p-2">
        <nav className="space-y-1">
          {menuItems.map((item, index) => {
            const Icon = item.icon;
            return (
              <motion.button
                key={index}
                whileHover={{ x: 4 }}
                whileTap={{ scale: 0.98 }}
                className={`w-full flex items-center space-x-3 px-4 py-3 rounded-xl text-left transition-all ${
                  item.active 
                    ? 'bg-fa-white-frosted text-fa-blue-600 shadow-md' 
                    : 'text-fa-gray-600 hover:bg-fa-white-glass hover:text-fa-gray-800'
                }`}
              >
                <Icon className="w-5 h-5" />
                <span className="font-medium">{item.label}</span>
              </motion.button>
            );
          })}
        </nav>
        
        {/* Categories Section */}
        <div className="mt-8">
          <div className="flex items-center justify-between px-4 mb-3">
            <h3 className="fa-body font-semibold text-fa-gray-700">Categories</h3>
            <motion.button
              whileHover={{ scale: 1.05 }}
              whileTap={{ scale: 0.95 }}
              className="p-1 rounded-lg text-fa-blue-500 hover:bg-fa-white-glass"
            >
              <Plus className="w-4 h-4" />
            </motion.button>
          </div>
          
          <div className="space-y-1">
            {categories.map((category, index) => (
              <motion.button
                key={index}
                whileHover={{ x: 4 }}
                whileTap={{ scale: 0.98 }}
                className="w-full flex items-center justify-between px-4 py-2 rounded-lg text-left text-fa-gray-600 hover:bg-fa-white-glass hover:text-fa-gray-800"
              >
                <span>{category.name}</span>
                <span className="text-xs bg-fa-white-glass px-2 py-1 rounded-full">
                  {category.count}
                </span>
              </motion.button>
            ))}
          </div>
        </div>
      </div>
      
      {/* Sidebar Footer */}
      <div className="p-4 border-t border-fa-white-frosted">
        <motion.button
          whileHover={{ scale: 1.02 }}
          whileTap={{ scale: 0.98 }}
          className="w-full flex items-center space-x-3 px-4 py-3 rounded-xl text-fa-gray-600 hover:bg-fa-white-glass hover:text-fa-gray-800"
        >
          <Settings className="w-5 h-5" />
          <span className="font-medium">Settings</span>
        </motion.button>
      </div>
    </div>
  );
};