import React from 'react';
import { motion } from 'framer-motion';

export const LoadingScreen: React.FC = () => {
  return (
    <div className="w-full h-full flex items-center justify-center bg-gradient-to-br from-fa-blue-50 to-fa-aqua-50">
      <div className="text-center">
        <motion.div
          className="w-16 h-16 mx-auto mb-6 rounded-full border-4 border-fa-blue-500 border-t-transparent"
          animate={{ rotate: 360 }}
          transition={{
            duration: 1,
            repeat: Infinity,
            ease: "linear"
          }}
        />
        
        <motion.h1 
          className="fa-heading-1 mb-2"
          initial={{ opacity: 0, y: 20 }}
          animate={{ opacity: 1, y: 0 }}
          transition={{ delay: 0.2 }}
        >
          Modern Todo
        </motion.h1>
        
        <motion.p 
          className="fa-caption text-fa-gray-600"
          initial={{ opacity: 0 }}
          animate={{ opacity: 1 }}
          transition={{ delay: 0.4 }}
        >
          Loading your productivity experience...
        </motion.p>
      </div>
    </div>
  );
};