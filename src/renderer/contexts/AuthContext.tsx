import React, { createContext, useContext, useState, useEffect, ReactNode } from 'react';
import { authService } from '../services/auth.service';
import { User, UserProfile } from '@/shared/types';
import { AuthSession } from '@/main/auth';
import { MultiFactorAuth } from '../components/auth/MultiFactorAuth';

interface AuthContextType {
  session: AuthSession | null;
  user: User | null;
  profile: UserProfile | null;
  isAuthenticated: boolean;
  requiresMFA: boolean;
  login: (username: string, password: string) => Promise<void>;
  register: (username: string, password: string, email?: string, fullName?: string) => Promise<void>;
  verifyMFA: (code: string) => Promise<void>;
  logout: () => Promise<void>;
  refreshSession: () => Promise<void>;
  changePassword: (currentPassword: string, newPassword: string) => Promise<void>;
  loading: boolean;
  error: string | null;
}

const AuthContext = createContext<AuthContextType | undefined>(undefined);

export const AuthProvider: React.FC<{ children: ReactNode }> = ({ children }) => {
  const [session, setSession] = useState<AuthSession | null>(null);
  const [user, setUser] = useState<User | null>(null);
  const [profile, setProfile] = useState<UserProfile | null>(null);
  const [isAuthenticated, setIsAuthenticated] = useState<boolean>(false);
  const [requiresMFA, setRequiresMFA] = useState<boolean>(false);
  const [showMFA, setShowMFA] = useState<boolean>(false);
  const [loading, setLoading] = useState<boolean>(true);
  const [error, setError] = useState<string | null>(null);

  // Initialize auth state on app startup
  useEffect(() => {
    const initializeAuth = async () => {
      try {
        setLoading(true);
        await authService.initialize();
        
        const currentSession = authService.getSession();
        const currentUser = authService.getUser();
        const currentProfile = authService.getProfile();
        
        setSession(currentSession);
        setUser(currentUser);
        setProfile(currentProfile);
        setIsAuthenticated(!!currentSession && !!currentUser);
        setRequiresMFA(authService.requiresMultiFactorAuth());
      } catch (err) {
        console.error('Auth initialization error:', err);
        setError('Failed to initialize authentication');
      } finally {
        setLoading(false);
      }
    };

    initializeAuth();
  }, []);

  const login = async (username: string, password: string) => {
    try {
      setLoading(true);
      setError(null);
      
      const credentials = { username, password };
      const session = await authService.login(credentials);
      
      const user = authService.getUser();
      const profile = authService.getProfile();
      
      setSession(session);
      setUser(user);
      setProfile(profile);
      setIsAuthenticated(true);
      setRequiresMFA(false);
    } catch (err) {
      if (err instanceof Error && err.message === 'MFA_REQUIRED') {
        setRequiresMFA(true);
        setShowMFA(true);
        setError(null);
      } else {
        const errorMessage = err instanceof Error ? err.message : 'Login failed';
        setError(errorMessage);
        throw err;
      }
    } finally {
      setLoading(false);
    }
  };

  const verifyMFA = async (code: string) => {
    try {
      setLoading(true);
      setError(null);
      
      const session = await authService.verifyMFA(code);
      
      const user = authService.getUser();
      const profile = authService.getProfile();
      
      setSession(session);
      setUser(user);
      setProfile(profile);
      setIsAuthenticated(true);
      setRequiresMFA(false);
      setShowMFA(false);
    } catch (err) {
      const errorMessage = err instanceof Error ? err.message : 'MFA verification failed';
      setError(errorMessage);
      throw err;
    } finally {
      setLoading(false);
    }
  };

  const register = async (username: string, password: string, email?: string, fullName?: string) => {
    try {
      setLoading(true);
      setError(null);
      
      const registerData = { username, password, email, fullName };
      await authService.register(registerData);
      
      // After registration, automatically log in the user
      await login(username, password);
    } catch (err) {
      const errorMessage = err instanceof Error ? err.message : 'Registration failed';
      setError(errorMessage);
      throw err;
    } finally {
      setLoading(false);
    }
  };

  const logout = async () => {
    try {
      setLoading(true);
      setError(null);
      
      await authService.logout();
      
      setSession(null);
      setUser(null);
      setProfile(null);
      setIsAuthenticated(false);
      setRequiresMFA(false);
      setShowMFA(false);
    } catch (err) {
      const errorMessage = err instanceof Error ? err.message : 'Logout failed';
      setError(errorMessage);
    } finally {
      setLoading(false);
    }
  };

  const refreshSession = async () => {
    try {
      setLoading(true);
      setError(null);
      
      const refreshedSession = await authService.refreshSession();
      
      if (refreshedSession) {
        setSession(refreshedSession);
        setIsAuthenticated(true);
      } else {
        // Session could not be refreshed, log out
        await logout();
      }
    } catch (err) {
      const errorMessage = err instanceof Error ? err.message : 'Session refresh failed';
      setError(errorMessage);
    } finally {
      setLoading(false);
    }
  };

  const changePassword = async (currentPassword: string, newPassword: string) => {
    try {
      setLoading(true);
      setError(null);
      
      await authService.changePassword(currentPassword, newPassword);
    } catch (err) {
      const errorMessage = err instanceof Error ? err.message : 'Password change failed';
      setError(errorMessage);
      throw err;
    } finally {
      setLoading(false);
    }
  };

  const value = {
    session,
    user,
    profile,
    isAuthenticated,
    requiresMFA,
    login,
    register,
    verifyMFA,
    logout,
    refreshSession,
    changePassword,
    loading,
    error
  };

  return (
    <AuthContext.Provider value={value}>
      {children}
      {showMFA && (
        <MultiFactorAuth 
          onClose={() => setShowMFA(false)} 
          onVerify={verifyMFA} 
        />
      )}
    </AuthContext.Provider>
  );
};

export const useAuth = (): AuthContextType => {
  const context = useContext(AuthContext);
  if (context === undefined) {
    throw new Error('useAuth must be used within an AuthProvider');
  }
  return context;
};