import { useEffect, useCallback } from 'react';
import { useAuth } from '@/renderer/contexts/AuthContext';

export const useSessionManager = () => {
  const { session, refreshSession, logout } = useAuth();

  const checkSessionExpiry = useCallback(async () => {
    if (session) {
      const now = new Date().getTime();
      const expiry = new Date(session.expiresAt).getTime();
      
      // If session expires in less than 5 minutes, try to refresh it
      if (expiry - now < 5 * 60 * 1000) {
        try {
          await refreshSession();
        } catch (error) {
          console.error('Failed to refresh session:', error);
          // If refresh fails, log out the user
          await logout();
        }
      }
    }
  }, [session, refreshSession, logout]);

  useEffect(() => {
    // Check session expiry on mount
    checkSessionExpiry();

    // Set up interval to check session expiry every minute
    const interval = setInterval(checkSessionExpiry, 60 * 1000);

    // Clean up interval on unmount
    return () => clearInterval(interval);
  }, [checkSessionExpiry]);

  return {
    session,
    checkSessionExpiry
  };
};