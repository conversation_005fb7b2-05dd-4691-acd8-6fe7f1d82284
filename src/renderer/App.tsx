import React, { useEffect, useState } from 'react';
import { motion, AnimatePresence } from 'framer-motion';
import { ApplicationProvider } from './providers/ApplicationProvider';
import { ThemeProvider } from './providers/ThemeProvider';
import { MainLayout } from './components/layout/MainLayout';
import { LoadingScreen } from './components/ui/LoadingScreen';
import { ErrorBoundary } from './components/ui/ErrorBoundary';
import { AuthProvider } from './contexts/AuthContext';
import { ProtectedRoute } from './components/auth/ProtectedRoute';

export const App: React.FC = () => {
  const [isLoading, setIsLoading] = useState(true);
  const [systemInfo, setSystemInfo] = useState<any>(null);

  useEffect(() => {
    const initializeApp = async () => {
      try {
        // Get system information
        if (window.electronAPI) {
          const info = await window.electronAPI.system.getInfo();
          setSystemInfo(info);
        }

        // Simulate loading time for smooth transition
        await new Promise(resolve => setTimeout(resolve, 1500));

        setIsLoading(false);
      } catch (error) {
        console.error('Failed to initialize app:', error);
        setIsLoading(false);
      }
    };

    initializeApp();
  }, []);

  if (isLoading) {
    return <LoadingScreen />;
  }

  return (
    <ErrorBoundary>
      <AuthProvider>
        <ApplicationProvider>
          <ThemeProvider>
            <ProtectedRoute>
              <AnimatePresence mode="wait">
                <motion.div
                  key="main-app"
                  initial={{ opacity: 0, scale: 0.95 }}
                  animate={{ opacity: 1, scale: 1 }}
                  transition={{
                    duration: 0.6,
                    ease: [0.4, 0, 0.2, 1],
                  }}
                  className="w-full h-full"
                >
                  <MainLayout systemInfo={systemInfo} />
                </motion.div>
              </AnimatePresence>
            </ProtectedRoute>
          </ThemeProvider>
        </ApplicationProvider>
      </AuthProvider>
    </ErrorBoundary>
  );
};