import React, { createContext, useContext, useEffect, useState } from 'react';

type Theme = 'light' | 'dark';

interface ThemeContextType {
  theme: Theme;
  toggleTheme: () => void;
  setTheme: (theme: Theme) => void;
}

const ThemeContext = createContext<ThemeContextType | undefined>(undefined);

export const useTheme = () => {
  const context = useContext(ThemeContext);
  if (!context) {
    throw new Error('useTheme must be used within ThemeProvider');
  }
  return context;
};

interface ThemeProviderProps {
  children: React.ReactNode;
}

export const ThemeProvider: React.FC<ThemeProviderProps> = ({ children }) => {
  const [theme, setThemeState] = useState<Theme>('light');

  useEffect(() => {
    // Load theme from localStorage or system preference
    const savedTheme = localStorage.getItem('fa-theme') as Theme;
    if (savedTheme) {
      setThemeState(savedTheme);
    } else {
      // Detect system theme preference
      const systemTheme = window.matchMedia('(prefers-color-scheme: dark)').matches ? 'dark' : 'light';
      setThemeState(systemTheme);
    }
  }, []);

  useEffect(() => {
    // Apply theme to document
    document.documentElement.setAttribute('data-theme', theme);
    localStorage.setItem('fa-theme', theme);
    
    // Update body background for theme
    if (theme === 'dark') {
      document.body.style.background = `linear-gradient(135deg, 
        rgba(30, 58, 138, 0.1) 0%, 
        rgba(37, 99, 235, 0.05) 50%, 
        rgba(59, 130, 246, 0.1) 100%)`;
    } else {
      document.body.style.background = `linear-gradient(135deg, 
        rgba(74, 144, 226, 0.1) 0%, 
        rgba(126, 211, 33, 0.05) 50%, 
        rgba(135, 206, 235, 0.1) 100%)`;
    }
  }, [theme]);

  const toggleTheme = () => {
    setThemeState(prev => prev === 'light' ? 'dark' : 'light');
  };

  const setTheme = (newTheme: Theme) => {
    setThemeState(newTheme);
  };

  const value: ThemeContextType = {
    theme,
    toggleTheme,
    setTheme,
  };

  return (
    <ThemeContext.Provider value={value}>
      {children}
    </ThemeContext.Provider>
  );
};