import React, { createContext, useContext, useEffect, useState } from 'react';
import { useSessionManager } from '@/renderer/hooks/useSessionManager';

interface ApplicationContextType {
  isInitialized: boolean;
  electronAPI: typeof window.electronAPI | null;
  systemInfo: any;
}

const ApplicationContext = createContext<ApplicationContextType | undefined>(undefined);

export const useApplication = () => {
  const context = useContext(ApplicationContext);
  if (!context) {
    throw new Error('useApplication must be used within ApplicationProvider');
  }
  return context;
};

interface ApplicationProviderProps {
  children: React.ReactNode;
}

export const ApplicationProvider: React.FC<ApplicationProviderProps> = ({ children }) => {
  const [isInitialized, setIsInitialized] = useState(false);
  const [systemInfo, setSystemInfo] = useState<any>(null);

  // Initialize session manager
  useSessionManager();

  useEffect(() => {
    const initialize = async () => {
      try {
        if (window.electronAPI) {
          const info = await window.electronAPI.system.getInfo();
          setSystemInfo(info);
        }
        setIsInitialized(true);
      } catch (error) {
        console.error('Failed to initialize application:', error);
        setIsInitialized(true); // Still mark as initialized to continue
      }
    };

    initialize();
  }, []);

  const value: ApplicationContextType = {
    isInitialized,
    electronAPI: window.electronAPI || null,
    systemInfo,
  };

  return (
    <ApplicationContext.Provider value={value}>
      {children}
    </ApplicationContext.Provider>
  );
};