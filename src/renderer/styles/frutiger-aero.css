/* Frutiger Aero Design System CSS Variables */
:root {
  /* Primary Blues */
  --fa-blue-50: #e6f3ff;
  --fa-blue-100: #cce7ff;
  --fa-blue-200: #99d0ff;
  --fa-blue-300: #66b8ff;
  --fa-blue-400: #33a1ff;
  --fa-blue-500: #007bff;
  --fa-blue-600: #0056cc;
  --fa-blue-700: #003d99;
  --fa-blue-800: #002966;
  --fa-blue-900: #001533;

  /* Secondary Greens */
  --fa-green-50: #e6fff2;
  --fa-green-100: #ccffe6;
  --fa-green-200: #99ffcc;
  --fa-green-300: #66ffb3;
  --fa-green-400: #33ff99;
  --fa-green-500: #00ff80;
  --fa-green-600: #00cc66;
  --fa-green-700: #00994d;
  --fa-green-800: #006633;
  --fa-green-900: #00331a;

  /* Accent Aqua */
  --fa-aqua-50: #e6fffe;
  --fa-aqua-100: #ccfffd;
  --fa-aqua-200: #99fffb;
  --fa-aqua-300: #66fff9;
  --fa-aqua-400: #33fff7;
  --fa-aqua-500: #00fff5;
  --fa-aqua-600: #00ccc4;
  --fa-aqua-700: #009993;
  --fa-aqua-800: #006662;
  --fa-aqua-900: #003331;

  /* Glass Whites */
  --fa-white: rgba(255, 255, 255, 0.95);
  --fa-white-glass: rgba(255, 255, 255, 0.15);
  --fa-white-frosted: rgba(255, 255, 255, 0.25);
  --fa-white-opaque: rgba(255, 255, 255, 0.85);

  /* Soft Grays */
  --fa-gray-50: rgba(248, 250, 252, 0.95);
  --fa-gray-100: rgba(241, 245, 249, 0.90);
  --fa-gray-200: rgba(226, 232, 240, 0.85);
  --fa-gray-300: rgba(203, 213, 225, 0.80);
  --fa-gray-400: rgba(148, 163, 184, 0.75);
  --fa-gray-500: rgba(100, 116, 139, 0.70);
  --fa-gray-600: rgba(71, 85, 105, 0.65);
  --fa-gray-700: rgba(51, 65, 85, 0.60);
  --fa-gray-800: rgba(30, 41, 59, 0.55);
  --fa-gray-900: rgba(15, 23, 42, 0.50);

  /* Deep Blues for Dark Mode */
  --fa-dark-50: rgba(30, 58, 138, 0.95);
  --fa-dark-100: rgba(30, 64, 175, 0.90);
  --fa-dark-200: rgba(37, 99, 235, 0.85);
  --fa-dark-300: rgba(59, 130, 246, 0.80);
  --fa-dark-400: rgba(96, 165, 250, 0.75);

  /* Status Colors */
  --fa-success: #10b981;
  --fa-success-bg: rgba(16, 185, 129, 0.1);
  --fa-success-border: rgba(16, 185, 129, 0.2);
  --fa-warning: #f59e0b;
  --fa-warning-bg: rgba(245, 158, 11, 0.1);
  --fa-warning-border: rgba(245, 158, 11, 0.2);
  --fa-error: #ef4444;
  --fa-error-bg: rgba(239, 68, 68, 0.1);
  --fa-error-border: rgba(239, 68, 68, 0.2);
  --fa-info: var(--fa-blue-500);
  --fa-info-bg: rgba(0, 123, 255, 0.1);
  --fa-info-border: rgba(0, 123, 255, 0.2);

  /* Typography */
  --fa-font-primary: 'Inter', 'Frutiger', 'Segoe UI', system-ui, -apple-system, sans-serif;
  --fa-font-mono: 'JetBrains Mono', 'Fira Code', 'Monaco', monospace;
  --fa-font-display: 'Inter Display', 'Frutiger', system-ui, sans-serif;

  /* Type Scale */
  --fa-text-xs: 0.75rem;
  --fa-text-sm: 0.875rem;
  --fa-text-base: 1rem;
  --fa-text-lg: 1.125rem;
  --fa-text-xl: 1.25rem;
  --fa-text-2xl: 1.5rem;
  --fa-text-3xl: 1.875rem;
  --fa-text-4xl: 2.25rem;
  --fa-text-5xl: 3rem;

  /* Line Heights */
  --fa-leading-tight: 1.25;
  --fa-leading-snug: 1.375;
  --fa-leading-normal: 1.5;
  --fa-leading-relaxed: 1.625;
  --fa-leading-loose: 2;

  /* Font Weights */
  --fa-font-thin: 100;
  --fa-font-light: 300;
  --fa-font-normal: 400;
  --fa-font-medium: 500;
  --fa-font-semibold: 600;
  --fa-font-bold: 700;
  --fa-font-extrabold: 800;

  /* Spacing Scale */
  --fa-space-0: 0;
  --fa-space-1: 0.25rem;
  --fa-space-2: 0.5rem;
  --fa-space-3: 0.75rem;
  --fa-space-4: 1rem;
  --fa-space-5: 1.25rem;
  --fa-space-6: 1.5rem;
  --fa-space-8: 2rem;
  --fa-space-10: 2.5rem;
  --fa-space-12: 3rem;
  --fa-space-16: 4rem;
  --fa-space-20: 5rem;
  --fa-space-24: 6rem;
  --fa-space-32: 8rem;

  /* Border Radius */
  --fa-radius-sm: 0.375rem;
  --fa-radius-md: 0.5rem;
  --fa-radius-lg: 0.75rem;
  --fa-radius-xl: 1rem;
  --fa-radius-2xl: 1.5rem;
  --fa-radius-full: 9999px;

  /* Breakpoints */
  --fa-breakpoint-sm: 640px;
  --fa-breakpoint-md: 768px;
  --fa-breakpoint-lg: 1024px;
  --fa-breakpoint-xl: 1280px;
  --fa-breakpoint-2xl: 1536px;
}

/* Dark Mode Overrides */
[data-theme="dark"] {
  --fa-white-glass: rgba(30, 58, 138, 0.15);
  --fa-white-frosted: rgba(30, 64, 175, 0.25);
  --fa-white-opaque: rgba(30, 58, 138, 0.85);
  
  color-scheme: dark;
}

/* Typography Classes */
.fa-heading-1 {
  font-family: var(--fa-font-display);
  font-size: var(--fa-text-4xl);
  font-weight: var(--fa-font-bold);
  line-height: var(--fa-leading-tight);
  letter-spacing: -0.025em;
  background: linear-gradient(135deg, var(--fa-blue-600), var(--fa-aqua-500));
  -webkit-background-clip: text;
  -webkit-text-fill-color: transparent;
  background-clip: text;
}

.fa-heading-2 {
  font-family: var(--fa-font-display);
  font-size: var(--fa-text-3xl);
  font-weight: var(--fa-font-semibold);
  line-height: var(--fa-leading-tight);
  color: var(--fa-gray-800);
}

.fa-heading-3 {
  font-family: var(--fa-font-primary);
  font-size: var(--fa-text-2xl);
  font-weight: var(--fa-font-semibold);
  line-height: var(--fa-leading-snug);
  color: var(--fa-gray-700);
}

.fa-body {
  font-family: var(--fa-font-primary);
  font-size: var(--fa-text-base);
  font-weight: var(--fa-font-normal);
  line-height: var(--fa-leading-relaxed);
  color: var(--fa-gray-600);
}

.fa-caption {
  font-family: var(--fa-font-primary);
  font-size: var(--fa-text-sm);
  font-weight: var(--fa-font-medium);
  line-height: var(--fa-leading-normal);
  color: var(--fa-gray-500);
}

/* Glass Panel Components */
.fa-glass-panel {
  background: var(--fa-white-glass);
  backdrop-filter: blur(20px);
  -webkit-backdrop-filter: blur(20px);
  border: 1px solid rgba(255, 255, 255, 0.2);
  border-radius: var(--fa-radius-xl);
  box-shadow: 
    0 8px 32px rgba(0, 0, 0, 0.1),
    inset 0 1px 0 rgba(255, 255, 255, 0.2);
}

.fa-glass-panel-frosted {
  background: var(--fa-white-frosted);
  backdrop-filter: blur(40px);
  -webkit-backdrop-filter: blur(40px);
  border: 1px solid rgba(255, 255, 255, 0.3);
  border-radius: var(--fa-radius-2xl);
  box-shadow: 
    0 16px 64px rgba(0, 0, 0, 0.15),
    inset 0 1px 0 rgba(255, 255, 255, 0.3),
    inset 0 -1px 0 rgba(0, 0, 0, 0.05);
}

/* Glass Button */
.fa-button-glass {
  background: var(--fa-white-glass);
  backdrop-filter: blur(16px);
  border: 1px solid rgba(255, 255, 255, 0.3);
  border-radius: var(--fa-radius-lg);
  padding: var(--fa-space-3) var(--fa-space-6);
  font-family: var(--fa-font-primary);
  font-weight: var(--fa-font-medium);
  color: var(--fa-gray-700);
  cursor: pointer;
  transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
  box-shadow: 
    0 4px 16px rgba(0, 0, 0, 0.1),
    inset 0 1px 0 rgba(255, 255, 255, 0.2);
}

.fa-button-glass:hover {
  background: var(--fa-white-frosted);
  transform: translateY(-2px);
  box-shadow: 
    0 8px 32px rgba(0, 0, 0, 0.15),
    inset 0 1px 0 rgba(255, 255, 255, 0.3);
}

.fa-button-glass:active {
  transform: translateY(0);
  box-shadow: 
    0 2px 8px rgba(0, 0, 0, 0.1),
    inset 0 1px 0 rgba(255, 255, 255, 0.2);
}

.fa-button-primary {
  background: linear-gradient(
    135deg,
    var(--fa-blue-500) 0%,
    var(--fa-aqua-500) 100%
  );
  border: 1px solid rgba(255, 255, 255, 0.3);
  color: white;
  box-shadow: 
    0 4px 16px rgba(0, 123, 255, 0.3),
    inset 0 1px 0 rgba(255, 255, 255, 0.2);
}

.fa-button-primary:hover {
  background: linear-gradient(
    135deg,
    var(--fa-blue-600) 0%,
    var(--fa-aqua-600) 100%
  );
  box-shadow: 
    0 8px 32px rgba(0, 123, 255, 0.4),
    inset 0 1px 0 rgba(255, 255, 255, 0.3);
}

/* Todo Card */
.fa-todo-card {
  background: var(--fa-white-glass);
  backdrop-filter: blur(20px);
  border: 1px solid rgba(255, 255, 255, 0.2);
  border-radius: var(--fa-radius-xl);
  padding: var(--fa-space-6);
  margin-bottom: var(--fa-space-4);
  transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
  position: relative;
  overflow: hidden;
}

.fa-todo-card::before {
  content: '';
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  height: 3px;
  background: linear-gradient(
    90deg,
    var(--fa-blue-500) 0%,
    var(--fa-green-500) 50%,
    var(--fa-aqua-500) 100%
  );
  opacity: 0;
  transition: opacity 0.3s ease;
}

.fa-todo-card:hover {
  transform: translateY(-4px);
  box-shadow: 
    0 16px 64px rgba(0, 0, 0, 0.15),
    inset 0 1px 0 rgba(255, 255, 255, 0.3);
}

.fa-todo-card:hover::before {
  opacity: 1;
}

.fa-todo-card.completed {
  opacity: 0.7;
  background: var(--fa-success-bg);
  border-color: var(--fa-success-border);
}

.fa-todo-card.high-priority::before {
  background: linear-gradient(
    90deg,
    var(--fa-error) 0%,
    var(--fa-warning) 100%
  );
  opacity: 1;
}

/* Input Fields */
.fa-input {
  background: var(--fa-white-glass);
  backdrop-filter: blur(16px);
  border: 1px solid rgba(255, 255, 255, 0.3);
  border-radius: var(--fa-radius-lg);
  padding: var(--fa-space-4) var(--fa-space-5);
  font-family: var(--fa-font-primary);
  font-size: var(--fa-text-base);
  color: var(--fa-gray-700);
  width: 100%;
  transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
  box-shadow: 
    inset 0 2px 4px rgba(0, 0, 0, 0.1),
    0 1px 0 rgba(255, 255, 255, 0.2);
}

.fa-input:focus {
  outline: none;
  border-color: var(--fa-blue-400);
  box-shadow: 
    inset 0 2px 4px rgba(0, 0, 0, 0.1),
    0 0 0 3px rgba(0, 123, 255, 0.1),
    0 1px 0 rgba(255, 255, 255, 0.2);
}

.fa-input::placeholder {
  color: var(--fa-gray-400);
  font-style: italic;
}

/* Hover Effects */
.fa-hover-lift:hover {
  transform: translateY(-4px);
}

.fa-hover-glow:hover {
  animation: fa-glow-pulse 2s infinite;
}

/* Animations */
@keyframes fa-fade-in {
  from { opacity: 0; }
  to { opacity: 1; }
}

@keyframes fa-slide-up {
  from {
    opacity: 0;
    transform: translateY(20px) scale(0.95);
  }
  to {
    opacity: 1;
    transform: translateY(0) scale(1);
  }
}

@keyframes fa-slide-in-right {
  from {
    opacity: 0;
    transform: translateX(20px);
  }
  to {
    opacity: 1;
    transform: translateX(0);
  }
}

@keyframes fa-glow-pulse {
  0%, 100% {
    box-shadow: 0 0 20px rgba(0, 123, 255, 0.3);
  }
  50% {
    box-shadow: 0 0 40px rgba(0, 123, 255, 0.6);
  }
}

@keyframes fa-float {
  0%, 100% {
    transform: translateY(0);
  }
  50% {
    transform: translateY(-10px);
  }
}

/* Transitions */
.fa-transition-all {
  transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
}

.fa-transition-fast {
  transition: all 0.15s cubic-bezier(0.4, 0, 0.2, 1);
}

.fa-transition-slow {
  transition: all 0.5s cubic-bezier(0.4, 0, 0.2, 1);
}

/* Icon Styles */
.fa-icon {
  display: inline-block;
  width: 1.5rem;
  height: 1.5rem;
  stroke: currentColor;
  fill: none;
  stroke-width: 2;
  stroke-linecap: round;
  stroke-linejoin: round;
  transition: all 0.3s ease;
}

.fa-icon-sm {
  width: 1rem;
  height: 1rem;
  stroke-width: 2.5;
}

.fa-icon-lg {
  width: 2rem;
  height: 2rem;
  stroke-width: 1.5;
}

.fa-icon-xl {
  width: 3rem;
  height: 3rem;
  stroke-width: 1;
}

/* Nature-inspired icon colors */
.fa-icon-nature {
  color: var(--fa-green-500);
}

.fa-icon-water {
  color: var(--fa-aqua-500);
}

.fa-icon-sky {
  color: var(--fa-blue-500);
}