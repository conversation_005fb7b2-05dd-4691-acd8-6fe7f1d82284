// Mock the ipcRenderer for testing
const mockInvoke = jest.fn();

jest.mock('electron', () => ({
  ipcRenderer: {
    invoke: mockInvoke,
  },
}));

import { authService } from '../auth.service';

describe('Authentication Service', () => {
  beforeEach(() => {
    jest.clearAllMocks();
  });

  it('should login successfully', async () => {
    const mockSession = {
      sessionId: 'test-session-id',
      userId: 'test-user-id',
      username: 'testuser',
      createdAt: new Date(),
      expiresAt: new Date(Date.now() + 3600000),
      deviceFingerprint: 'test-fingerprint',
      ipAddress: '127.0.0.1',
      userAgent: 'test-agent',
      isActive: true,
      permissions: ['read', 'write']
    };

    const mockUser = {
      id: 'test-user-id',
      username: 'testuser',
      password_hash: 'hashed-password',
      created_at: new Date(),
      updated_at: new Date(),
      is_active: true,
      email_verified: false
    };

    const mockProfile = {
      user_id: 'test-user-id',
      full_name: 'Test User',
      email: '<EMAIL>',
      theme_preference: 'light',
      notification_enabled: true,
      timezone: 'UTC',
      language_preference: 'en'
    };

    mockInvoke.mockResolvedValue({
      session: mockSession,
      user: mockUser,
      profile: mockProfile
    });

    const credentials = { username: 'testuser', password: 'password123' };
    const result = await authService.login(credentials);

    expect(result).toEqual(mockSession);
    expect(mockInvoke).toHaveBeenCalledWith('auth:login', credentials);
  });

  it('should handle login error', async () => {
    mockInvoke.mockResolvedValue({
      error: 'Invalid credentials'
    });

    const credentials = { username: 'testuser', password: 'wrongpassword' };
    
    await expect(authService.login(credentials)).rejects.toThrow('Invalid credentials');
  });

  it('should logout successfully', async () => {
    mockInvoke.mockResolvedValue(undefined);

    await authService.logout();

    expect(mockInvoke).toHaveBeenCalledWith('auth:logout', expect.any(String));
  });

  it('should validate session successfully', async () => {
    const mockSession = {
      sessionId: 'test-session-id',
      userId: 'test-user-id',
      username: 'testuser',
      createdAt: new Date(),
      expiresAt: new Date(Date.now() + 3600000),
      deviceFingerprint: 'test-fingerprint',
      ipAddress: '127.0.0.1',
      userAgent: 'test-agent',
      isActive: true,
      permissions: ['read', 'write']
    };

    const mockUser = {
      id: 'test-user-id',
      username: 'testuser',
      password_hash: 'hashed-password',
      created_at: new Date(),
      updated_at: new Date(),
      is_active: true,
      email_verified: false
    };

    const mockProfile = {
      user_id: 'test-user-id',
      full_name: 'Test User',
      email: '<EMAIL>',
      theme_preference: 'light',
      notification_enabled: true,
      timezone: 'UTC',
      language_preference: 'en'
    };

    mockInvoke.mockResolvedValue({
      isValid: true,
      session: mockSession,
      user: mockUser,
      profile: mockProfile
    });

    const result = await authService.validateSession();

    expect(result).toBe(true);
    expect(mockInvoke).toHaveBeenCalledWith('auth:validate-session', 'test-session-id');
  });
});