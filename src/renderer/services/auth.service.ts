import { User, UserProfile } from '@/shared/types';
import { AuthSession, LoginCredentials, RegisterData } from '@/main/auth';
import { ipcRenderer } from 'electron';

export class AuthenticationService {
  private static instance: AuthenticationService;
  private session: AuthSession | null = null;
  private user: User | null = null;
  private profile: UserProfile | null = null;
  private requiresMFA: boolean = false;

  private constructor() {}

  public static getInstance(): AuthenticationService {
    if (!AuthenticationService.instance) {
      AuthenticationService.instance = new AuthenticationService();
    }
    return AuthenticationService.instance;
  }

  public async login(credentials: LoginCredentials): Promise<AuthSession> {
    try {
      const response = await ipcRenderer.invoke('auth:login', credentials);
      
      if (response.error) {
        throw new Error(response.error);
      }
      
      // Check if MFA is required
      if (response.requiresMFA) {
        this.requiresMFA = true;
        throw new Error('MFA_REQUIRED');
      }
      
      this.session = response.session;
      this.user = response.user;
      this.profile = response.profile;
      
      // Store session in localStorage for persistence
      if (this.session) {
        localStorage.setItem('authSession', JSON.stringify(this.session));
      }
      
      return this.session as AuthSession;
    } catch (error) {
      console.error('Login failed:', error);
      throw error;
    }
  }

  public async verifyMFA(code: string): Promise<AuthSession> {
    try {
      if (!this.requiresMFA) {
        throw new Error('MFA not required');
      }
      
      const response = await ipcRenderer.invoke('auth:verify-mfa', {
        sessionId: this.session?.sessionId,
        code
      });
      
      if (response.error) {
        throw new Error(response.error);
      }
      
      this.session = response.session;
      this.user = response.user;
      this.profile = response.profile;
      this.requiresMFA = false;
      
      // Store session in localStorage for persistence
      if (this.session) {
        localStorage.setItem('authSession', JSON.stringify(this.session));
      }
      
      return this.session as AuthSession;
    } catch (error) {
      console.error('MFA verification failed:', error);
      throw error;
    }
  }

  public async register(data: RegisterData): Promise<{ user: User; profile: UserProfile }> {
    try {
      const response = await ipcRenderer.invoke('auth:register', data);
      
      if (response.error) {
        throw new Error(response.error);
      }
      
      return response;
    } catch (error) {
      console.error('Registration failed:', error);
      throw error;
    }
  }

  public async logout(): Promise<void> {
    try {
      if (this.session) {
        await ipcRenderer.invoke('auth:logout', this.session.sessionId);
      }
      
      this.session = null;
      this.user = null;
      this.profile = null;
      this.requiresMFA = false;
      
      // Clear session from localStorage
      localStorage.removeItem('authSession');
    } catch (error) {
      console.error('Logout failed:', error);
      // Still clear local session even if server call fails
      this.session = null;
      this.user = null;
      this.profile = null;
      this.requiresMFA = false;
      localStorage.removeItem('authSession');
      throw error;
    }
  }

  public async validateSession(): Promise<boolean> {
    try {
      if (!this.session) {
        // Try to restore session from localStorage
        const storedSession = localStorage.getItem('authSession');
        if (storedSession) {
          this.session = JSON.parse(storedSession);
        } else {
          return false;
        }
      }
      
      const response = await ipcRenderer.invoke('auth:validate-session', this.session?.sessionId);
      
      if (response.error || !response.isValid) {
        this.session = null;
        this.user = null;
        this.profile = null;
        this.requiresMFA = false;
        localStorage.removeItem('authSession');
        return false;
      }
      
      this.session = response.session;
      this.user = response.user;
      this.profile = response.profile;
      
      // Update stored session
      localStorage.setItem('authSession', JSON.stringify(this.session));
      
      return true;
    } catch (error) {
      console.error('Session validation failed:', error);
      this.session = null;
      this.user = null;
      this.profile = null;
      this.requiresMFA = false;
      localStorage.removeItem('authSession');
      return false;
    }
  }

  public async refreshSession(): Promise<AuthSession | null> {
    try {
      if (!this.session) {
        return null;
      }
      
      const response = await ipcRenderer.invoke('auth:refresh-session', this.session.sessionId);
      
      if (response.error) {
        throw new Error(response.error);
      }
      
      this.session = response.session;
      
      // Update stored session
      localStorage.setItem('authSession', JSON.stringify(this.session));
      
      return this.session;
    } catch (error) {
      console.error('Session refresh failed:', error);
      return null;
    }
  }

  public async changePassword(currentPassword: string, newPassword: string): Promise<void> {
    try {
      if (!this.user) {
        throw new Error('User not authenticated');
      }
      
      const response = await ipcRenderer.invoke('auth:change-password', {
        userId: this.user.id,
        currentPassword,
        newPassword
      });
      
      if (response.error) {
        throw new Error(response.error);
      }
    } catch (error) {
      console.error('Password change failed:', error);
      throw error;
    }
  }

  public async resetPassword(username: string, newPassword: string): Promise<void> {
    try {
      const response = await ipcRenderer.invoke('auth:reset-password', {
        username,
        newPassword
      });
      
      if (response.error) {
        throw new Error(response.error);
      }
    } catch (error) {
      console.error('Password reset failed:', error);
      throw error;
    }
  }

  public getSession(): AuthSession | null {
    return this.session;
  }

  public getUser(): User | null {
    return this.user;
  }

  public getProfile(): UserProfile | null {
    return this.profile;
  }

  public isAuthenticated(): boolean {
    return !!this.session && !!this.user && !this.requiresMFA;
  }

  public requiresMultiFactorAuth(): boolean {
    return this.requiresMFA;
  }

  public async initialize(): Promise<void> {
    // Try to restore session on app startup
    await this.validateSession();
  }
}

export const authService = AuthenticationService.getInstance();