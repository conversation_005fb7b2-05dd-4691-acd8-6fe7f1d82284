import { contextBridge, ipc<PERSON><PERSON><PERSON> } from 'electron';

// Define the API that will be exposed to the renderer process
const electronAPI = {
  // Database operations
  database: {
    query: (sql: string, params?: any[]) => ipcRenderer.invoke('db:query', sql, params),
    transaction: (operations: any[]) => ipc<PERSON><PERSON>er.invoke('db:transaction', operations),
  },

  // Security operations
  security: {
    encrypt: (data: string) => ipcRenderer.invoke('security:encrypt', data),
    decrypt: (encryptedData: string) => ipcRenderer.invoke('security:decrypt', encryptedData),
  },

  // MCP operations
  mcp: {
    sync: (data: any) => ipc<PERSON>enderer.invoke('mcp:sync', data),
  },

  // System operations
  system: {
    getInfo: () => ipcRenderer.invoke('system:getInfo'),
    quit: () => ipc<PERSON>enderer.invoke('app:quit'),
    minimize: () => ipc<PERSON><PERSON><PERSON>.invoke('app:minimize'),
    maximize: () => ipc<PERSON><PERSON>er.invoke('app:maximize'),
    close: () => ipc<PERSON>enderer.invoke('app:close'),
  },

  // Event listeners
  on: (channel: string, callback: (event: any, ...args: any[]) => void) => {
    const validChannels = [
      'menu-new-todo',
      'menu-import',
      'menu-export',
      'sync-status-changed',
      'theme-changed',
    ];
    
    if (validChannels.includes(channel)) {
      ipcRenderer.on(channel, callback);
    }
  },

  // Remove event listeners
  removeAllListeners: (channel: string) => {
    ipcRenderer.removeAllListeners(channel);
  },
};

// Expose the API to the renderer process
contextBridge.exposeInMainWorld('electronAPI', electronAPI);

// Export the type for the exposed API
export type ElectronAPIType = typeof electronAPI;

// Declare global interface for TypeScript
declare global {
  interface Window {
    electronAPI: typeof electronAPI;
  }
}