var qm=Object.defineProperty;var Zm=(e,t,n)=>t in e?qm(e,t,{enumerable:!0,configurable:!0,writable:!0,value:n}):e[t]=n;var Qt=(e,t,n)=>Zm(e,typeof t!="symbol"?t+"":t,n);(function(){const t=document.createElement("link").relList;if(t&&t.supports&&t.supports("modulepreload"))return;for(const s of document.querySelectorAll('link[rel="modulepreload"]'))r(s);new MutationObserver(s=>{for(const i of s)if(i.type==="childList")for(const o of i.addedNodes)o.tagName==="LINK"&&o.rel==="modulepreload"&&r(o)}).observe(document,{childList:!0,subtree:!0});function n(s){const i={};return s.integrity&&(i.integrity=s.integrity),s.referrerPolicy&&(i.referrerPolicy=s.referrerPolicy),s.crossOrigin==="use-credentials"?i.credentials="include":s.crossOrigin==="anonymous"?i.credentials="omit":i.credentials="same-origin",i}function r(s){if(s.ep)return;s.ep=!0;const i=n(s);fetch(s.href,i)}})();function Jm(e){return e&&e.__esModule&&Object.prototype.hasOwnProperty.call(e,"default")?e.default:e}function ey(e){if(e.__esModule)return e;var t=e.default;if(typeof t=="function"){var n=function r(){return this instanceof r?Reflect.construct(t,arguments,this.constructor):t.apply(this,arguments)};n.prototype=t.prototype}else n={};return Object.defineProperty(n,"__esModule",{value:!0}),Object.keys(e).forEach(function(r){var s=Object.getOwnPropertyDescriptor(e,r);Object.defineProperty(n,r,s.get?s:{enumerable:!0,get:function(){return e[r]}})}),n}var td={exports:{}},Ai={},nd={exports:{}},F={};/**
 * @license React
 * react.production.min.js
 *
 * Copyright (c) Facebook, Inc. and its affiliates.
 *
 * This source code is licensed under the MIT license found in the
 * LICENSE file in the root directory of this source tree.
 */var ss=Symbol.for("react.element"),ty=Symbol.for("react.portal"),ny=Symbol.for("react.fragment"),ry=Symbol.for("react.strict_mode"),sy=Symbol.for("react.profiler"),iy=Symbol.for("react.provider"),oy=Symbol.for("react.context"),ay=Symbol.for("react.forward_ref"),ly=Symbol.for("react.suspense"),uy=Symbol.for("react.memo"),cy=Symbol.for("react.lazy"),Eu=Symbol.iterator;function fy(e){return e===null||typeof e!="object"?null:(e=Eu&&e[Eu]||e["@@iterator"],typeof e=="function"?e:null)}var rd={isMounted:function(){return!1},enqueueForceUpdate:function(){},enqueueReplaceState:function(){},enqueueSetState:function(){}},sd=Object.assign,id={};function Zn(e,t,n){this.props=e,this.context=t,this.refs=id,this.updater=n||rd}Zn.prototype.isReactComponent={};Zn.prototype.setState=function(e,t){if(typeof e!="object"&&typeof e!="function"&&e!=null)throw Error("setState(...): takes an object of state variables to update or a function which returns an object of state variables.");this.updater.enqueueSetState(this,e,t,"setState")};Zn.prototype.forceUpdate=function(e){this.updater.enqueueForceUpdate(this,e,"forceUpdate")};function od(){}od.prototype=Zn.prototype;function Ya(e,t,n){this.props=e,this.context=t,this.refs=id,this.updater=n||rd}var qa=Ya.prototype=new od;qa.constructor=Ya;sd(qa,Zn.prototype);qa.isPureReactComponent=!0;var ju=Array.isArray,ad=Object.prototype.hasOwnProperty,Za={current:null},ld={key:!0,ref:!0,__self:!0,__source:!0};function ud(e,t,n){var r,s={},i=null,o=null;if(t!=null)for(r in t.ref!==void 0&&(o=t.ref),t.key!==void 0&&(i=""+t.key),t)ad.call(t,r)&&!ld.hasOwnProperty(r)&&(s[r]=t[r]);var a=arguments.length-2;if(a===1)s.children=n;else if(1<a){for(var l=Array(a),u=0;u<a;u++)l[u]=arguments[u+2];s.children=l}if(e&&e.defaultProps)for(r in a=e.defaultProps,a)s[r]===void 0&&(s[r]=a[r]);return{$$typeof:ss,type:e,key:i,ref:o,props:s,_owner:Za.current}}function dy(e,t){return{$$typeof:ss,type:e.type,key:t,ref:e.ref,props:e.props,_owner:e._owner}}function Ja(e){return typeof e=="object"&&e!==null&&e.$$typeof===ss}function hy(e){var t={"=":"=0",":":"=2"};return"$"+e.replace(/[=:]/g,function(n){return t[n]})}var Nu=/\/+/g;function Ji(e,t){return typeof e=="object"&&e!==null&&e.key!=null?hy(""+e.key):t.toString(36)}function Is(e,t,n,r,s){var i=typeof e;(i==="undefined"||i==="boolean")&&(e=null);var o=!1;if(e===null)o=!0;else switch(i){case"string":case"number":o=!0;break;case"object":switch(e.$$typeof){case ss:case ty:o=!0}}if(o)return o=e,s=s(o),e=r===""?"."+Ji(o,0):r,ju(s)?(n="",e!=null&&(n=e.replace(Nu,"$&/")+"/"),Is(s,t,n,"",function(u){return u})):s!=null&&(Ja(s)&&(s=dy(s,n+(!s.key||o&&o.key===s.key?"":(""+s.key).replace(Nu,"$&/")+"/")+e)),t.push(s)),1;if(o=0,r=r===""?".":r+":",ju(e))for(var a=0;a<e.length;a++){i=e[a];var l=r+Ji(i,a);o+=Is(i,t,n,l,s)}else if(l=fy(e),typeof l=="function")for(e=l.call(e),a=0;!(i=e.next()).done;)i=i.value,l=r+Ji(i,a++),o+=Is(i,t,n,l,s);else if(i==="object")throw t=String(e),Error("Objects are not valid as a React child (found: "+(t==="[object Object]"?"object with keys {"+Object.keys(e).join(", ")+"}":t)+"). If you meant to render a collection of children, use an array instead.");return o}function ys(e,t,n){if(e==null)return e;var r=[],s=0;return Is(e,r,"","",function(i){return t.call(n,i,s++)}),r}function py(e){if(e._status===-1){var t=e._result;t=t(),t.then(function(n){(e._status===0||e._status===-1)&&(e._status=1,e._result=n)},function(n){(e._status===0||e._status===-1)&&(e._status=2,e._result=n)}),e._status===-1&&(e._status=0,e._result=t)}if(e._status===1)return e._result.default;throw e._result}var ke={current:null},Os={transition:null},my={ReactCurrentDispatcher:ke,ReactCurrentBatchConfig:Os,ReactCurrentOwner:Za};function cd(){throw Error("act(...) is not supported in production builds of React.")}F.Children={map:ys,forEach:function(e,t,n){ys(e,function(){t.apply(this,arguments)},n)},count:function(e){var t=0;return ys(e,function(){t++}),t},toArray:function(e){return ys(e,function(t){return t})||[]},only:function(e){if(!Ja(e))throw Error("React.Children.only expected to receive a single React element child.");return e}};F.Component=Zn;F.Fragment=ny;F.Profiler=sy;F.PureComponent=Ya;F.StrictMode=ry;F.Suspense=ly;F.__SECRET_INTERNALS_DO_NOT_USE_OR_YOU_WILL_BE_FIRED=my;F.act=cd;F.cloneElement=function(e,t,n){if(e==null)throw Error("React.cloneElement(...): The argument must be a React element, but you passed "+e+".");var r=sd({},e.props),s=e.key,i=e.ref,o=e._owner;if(t!=null){if(t.ref!==void 0&&(i=t.ref,o=Za.current),t.key!==void 0&&(s=""+t.key),e.type&&e.type.defaultProps)var a=e.type.defaultProps;for(l in t)ad.call(t,l)&&!ld.hasOwnProperty(l)&&(r[l]=t[l]===void 0&&a!==void 0?a[l]:t[l])}var l=arguments.length-2;if(l===1)r.children=n;else if(1<l){a=Array(l);for(var u=0;u<l;u++)a[u]=arguments[u+2];r.children=a}return{$$typeof:ss,type:e.type,key:s,ref:i,props:r,_owner:o}};F.createContext=function(e){return e={$$typeof:oy,_currentValue:e,_currentValue2:e,_threadCount:0,Provider:null,Consumer:null,_defaultValue:null,_globalName:null},e.Provider={$$typeof:iy,_context:e},e.Consumer=e};F.createElement=ud;F.createFactory=function(e){var t=ud.bind(null,e);return t.type=e,t};F.createRef=function(){return{current:null}};F.forwardRef=function(e){return{$$typeof:ay,render:e}};F.isValidElement=Ja;F.lazy=function(e){return{$$typeof:cy,_payload:{_status:-1,_result:e},_init:py}};F.memo=function(e,t){return{$$typeof:uy,type:e,compare:t===void 0?null:t}};F.startTransition=function(e){var t=Os.transition;Os.transition={};try{e()}finally{Os.transition=t}};F.unstable_act=cd;F.useCallback=function(e,t){return ke.current.useCallback(e,t)};F.useContext=function(e){return ke.current.useContext(e)};F.useDebugValue=function(){};F.useDeferredValue=function(e){return ke.current.useDeferredValue(e)};F.useEffect=function(e,t){return ke.current.useEffect(e,t)};F.useId=function(){return ke.current.useId()};F.useImperativeHandle=function(e,t,n){return ke.current.useImperativeHandle(e,t,n)};F.useInsertionEffect=function(e,t){return ke.current.useInsertionEffect(e,t)};F.useLayoutEffect=function(e,t){return ke.current.useLayoutEffect(e,t)};F.useMemo=function(e,t){return ke.current.useMemo(e,t)};F.useReducer=function(e,t,n){return ke.current.useReducer(e,t,n)};F.useRef=function(e){return ke.current.useRef(e)};F.useState=function(e){return ke.current.useState(e)};F.useSyncExternalStore=function(e,t,n){return ke.current.useSyncExternalStore(e,t,n)};F.useTransition=function(){return ke.current.useTransition()};F.version="18.3.1";nd.exports=F;var k=nd.exports;const yy=Jm(k);/**
 * @license React
 * react-jsx-runtime.production.min.js
 *
 * Copyright (c) Facebook, Inc. and its affiliates.
 *
 * This source code is licensed under the MIT license found in the
 * LICENSE file in the root directory of this source tree.
 */var gy=k,vy=Symbol.for("react.element"),xy=Symbol.for("react.fragment"),wy=Object.prototype.hasOwnProperty,Sy=gy.__SECRET_INTERNALS_DO_NOT_USE_OR_YOU_WILL_BE_FIRED.ReactCurrentOwner,ky={key:!0,ref:!0,__self:!0,__source:!0};function fd(e,t,n){var r,s={},i=null,o=null;n!==void 0&&(i=""+n),t.key!==void 0&&(i=""+t.key),t.ref!==void 0&&(o=t.ref);for(r in t)wy.call(t,r)&&!ky.hasOwnProperty(r)&&(s[r]=t[r]);if(e&&e.defaultProps)for(r in t=e.defaultProps,t)s[r]===void 0&&(s[r]=t[r]);return{$$typeof:vy,type:e,key:i,ref:o,props:s,_owner:Sy.current}}Ai.Fragment=xy;Ai.jsx=fd;Ai.jsxs=fd;td.exports=Ai;var f=td.exports,zo={},dd={exports:{}},_e={},hd={exports:{}},pd={};/**
 * @license React
 * scheduler.production.min.js
 *
 * Copyright (c) Facebook, Inc. and its affiliates.
 *
 * This source code is licensed under the MIT license found in the
 * LICENSE file in the root directory of this source tree.
 */(function(e){function t(N,V){var _=N.length;N.push(V);e:for(;0<_;){var Z=_-1>>>1,ie=N[Z];if(0<s(ie,V))N[Z]=V,N[_]=ie,_=Z;else break e}}function n(N){return N.length===0?null:N[0]}function r(N){if(N.length===0)return null;var V=N[0],_=N.pop();if(_!==V){N[0]=_;e:for(var Z=0,ie=N.length,ps=ie>>>1;Z<ps;){var Kt=2*(Z+1)-1,Zi=N[Kt],Gt=Kt+1,ms=N[Gt];if(0>s(Zi,_))Gt<ie&&0>s(ms,Zi)?(N[Z]=ms,N[Gt]=_,Z=Gt):(N[Z]=Zi,N[Kt]=_,Z=Kt);else if(Gt<ie&&0>s(ms,_))N[Z]=ms,N[Gt]=_,Z=Gt;else break e}}return V}function s(N,V){var _=N.sortIndex-V.sortIndex;return _!==0?_:N.id-V.id}if(typeof performance=="object"&&typeof performance.now=="function"){var i=performance;e.unstable_now=function(){return i.now()}}else{var o=Date,a=o.now();e.unstable_now=function(){return o.now()-a}}var l=[],u=[],c=1,d=null,h=3,g=!1,v=!1,x=!1,P=typeof setTimeout=="function"?setTimeout:null,m=typeof clearTimeout=="function"?clearTimeout:null,p=typeof setImmediate<"u"?setImmediate:null;typeof navigator<"u"&&navigator.scheduling!==void 0&&navigator.scheduling.isInputPending!==void 0&&navigator.scheduling.isInputPending.bind(navigator.scheduling);function y(N){for(var V=n(u);V!==null;){if(V.callback===null)r(u);else if(V.startTime<=N)r(u),V.sortIndex=V.expirationTime,t(l,V);else break;V=n(u)}}function w(N){if(x=!1,y(N),!v)if(n(l)!==null)v=!0,hs(S);else{var V=n(u);V!==null&&ne(w,V.startTime-N)}}function S(N,V){v=!1,x&&(x=!1,m(T),T=-1),g=!0;var _=h;try{for(y(V),d=n(l);d!==null&&(!(d.expirationTime>V)||N&&!O());){var Z=d.callback;if(typeof Z=="function"){d.callback=null,h=d.priorityLevel;var ie=Z(d.expirationTime<=V);V=e.unstable_now(),typeof ie=="function"?d.callback=ie:d===n(l)&&r(l),y(V)}else r(l);d=n(l)}if(d!==null)var ps=!0;else{var Kt=n(u);Kt!==null&&ne(w,Kt.startTime-V),ps=!1}return ps}finally{d=null,h=_,g=!1}}var C=!1,j=null,T=-1,D=5,A=-1;function O(){return!(e.unstable_now()-A<D)}function de(){if(j!==null){var N=e.unstable_now();A=N;var V=!0;try{V=j(!0,N)}finally{V?We():(C=!1,j=null)}}else C=!1}var We;if(typeof p=="function")We=function(){p(de)};else if(typeof MessageChannel<"u"){var sr=new MessageChannel,Cu=sr.port2;sr.port1.onmessage=de,We=function(){Cu.postMessage(null)}}else We=function(){P(de,0)};function hs(N){j=N,C||(C=!0,We())}function ne(N,V){T=P(function(){N(e.unstable_now())},V)}e.unstable_IdlePriority=5,e.unstable_ImmediatePriority=1,e.unstable_LowPriority=4,e.unstable_NormalPriority=3,e.unstable_Profiling=null,e.unstable_UserBlockingPriority=2,e.unstable_cancelCallback=function(N){N.callback=null},e.unstable_continueExecution=function(){v||g||(v=!0,hs(S))},e.unstable_forceFrameRate=function(N){0>N||125<N?console.error("forceFrameRate takes a positive int between 0 and 125, forcing frame rates higher than 125 fps is not supported"):D=0<N?Math.floor(1e3/N):5},e.unstable_getCurrentPriorityLevel=function(){return h},e.unstable_getFirstCallbackNode=function(){return n(l)},e.unstable_next=function(N){switch(h){case 1:case 2:case 3:var V=3;break;default:V=h}var _=h;h=V;try{return N()}finally{h=_}},e.unstable_pauseExecution=function(){},e.unstable_requestPaint=function(){},e.unstable_runWithPriority=function(N,V){switch(N){case 1:case 2:case 3:case 4:case 5:break;default:N=3}var _=h;h=N;try{return V()}finally{h=_}},e.unstable_scheduleCallback=function(N,V,_){var Z=e.unstable_now();switch(typeof _=="object"&&_!==null?(_=_.delay,_=typeof _=="number"&&0<_?Z+_:Z):_=Z,N){case 1:var ie=-1;break;case 2:ie=250;break;case 5:ie=**********;break;case 4:ie=1e4;break;default:ie=5e3}return ie=_+ie,N={id:c++,callback:V,priorityLevel:N,startTime:_,expirationTime:ie,sortIndex:-1},_>Z?(N.sortIndex=_,t(u,N),n(l)===null&&N===n(u)&&(x?(m(T),T=-1):x=!0,ne(w,_-Z))):(N.sortIndex=ie,t(l,N),v||g||(v=!0,hs(S))),N},e.unstable_shouldYield=O,e.unstable_wrapCallback=function(N){var V=h;return function(){var _=h;h=V;try{return N.apply(this,arguments)}finally{h=_}}}})(pd);hd.exports=pd;var Py=hd.exports;/**
 * @license React
 * react-dom.production.min.js
 *
 * Copyright (c) Facebook, Inc. and its affiliates.
 *
 * This source code is licensed under the MIT license found in the
 * LICENSE file in the root directory of this source tree.
 */var Ty=k,Le=Py;function E(e){for(var t="https://reactjs.org/docs/error-decoder.html?invariant="+e,n=1;n<arguments.length;n++)t+="&args[]="+encodeURIComponent(arguments[n]);return"Minified React error #"+e+"; visit "+t+" for the full message or use the non-minified dev environment for full errors and additional helpful warnings."}var md=new Set,Vr={};function mn(e,t){bn(e,t),bn(e+"Capture",t)}function bn(e,t){for(Vr[e]=t,e=0;e<t.length;e++)md.add(t[e])}var pt=!(typeof window>"u"||typeof window.document>"u"||typeof window.document.createElement>"u"),Bo=Object.prototype.hasOwnProperty,Cy=/^[:A-Z_a-z\u00C0-\u00D6\u00D8-\u00F6\u00F8-\u02FF\u0370-\u037D\u037F-\u1FFF\u200C-\u200D\u2070-\u218F\u2C00-\u2FEF\u3001-\uD7FF\uF900-\uFDCF\uFDF0-\uFFFD][:A-Z_a-z\u00C0-\u00D6\u00D8-\u00F6\u00F8-\u02FF\u0370-\u037D\u037F-\u1FFF\u200C-\u200D\u2070-\u218F\u2C00-\u2FEF\u3001-\uD7FF\uF900-\uFDCF\uFDF0-\uFFFD\-.0-9\u00B7\u0300-\u036F\u203F-\u2040]*$/,Au={},Mu={};function Ey(e){return Bo.call(Mu,e)?!0:Bo.call(Au,e)?!1:Cy.test(e)?Mu[e]=!0:(Au[e]=!0,!1)}function jy(e,t,n,r){if(n!==null&&n.type===0)return!1;switch(typeof t){case"function":case"symbol":return!0;case"boolean":return r?!1:n!==null?!n.acceptsBooleans:(e=e.toLowerCase().slice(0,5),e!=="data-"&&e!=="aria-");default:return!1}}function Ny(e,t,n,r){if(t===null||typeof t>"u"||jy(e,t,n,r))return!0;if(r)return!1;if(n!==null)switch(n.type){case 3:return!t;case 4:return t===!1;case 5:return isNaN(t);case 6:return isNaN(t)||1>t}return!1}function Pe(e,t,n,r,s,i,o){this.acceptsBooleans=t===2||t===3||t===4,this.attributeName=r,this.attributeNamespace=s,this.mustUseProperty=n,this.propertyName=e,this.type=t,this.sanitizeURL=i,this.removeEmptyString=o}var fe={};"children dangerouslySetInnerHTML defaultValue defaultChecked innerHTML suppressContentEditableWarning suppressHydrationWarning style".split(" ").forEach(function(e){fe[e]=new Pe(e,0,!1,e,null,!1,!1)});[["acceptCharset","accept-charset"],["className","class"],["htmlFor","for"],["httpEquiv","http-equiv"]].forEach(function(e){var t=e[0];fe[t]=new Pe(t,1,!1,e[1],null,!1,!1)});["contentEditable","draggable","spellCheck","value"].forEach(function(e){fe[e]=new Pe(e,2,!1,e.toLowerCase(),null,!1,!1)});["autoReverse","externalResourcesRequired","focusable","preserveAlpha"].forEach(function(e){fe[e]=new Pe(e,2,!1,e,null,!1,!1)});"allowFullScreen async autoFocus autoPlay controls default defer disabled disablePictureInPicture disableRemotePlayback formNoValidate hidden loop noModule noValidate open playsInline readOnly required reversed scoped seamless itemScope".split(" ").forEach(function(e){fe[e]=new Pe(e,3,!1,e.toLowerCase(),null,!1,!1)});["checked","multiple","muted","selected"].forEach(function(e){fe[e]=new Pe(e,3,!0,e,null,!1,!1)});["capture","download"].forEach(function(e){fe[e]=new Pe(e,4,!1,e,null,!1,!1)});["cols","rows","size","span"].forEach(function(e){fe[e]=new Pe(e,6,!1,e,null,!1,!1)});["rowSpan","start"].forEach(function(e){fe[e]=new Pe(e,5,!1,e.toLowerCase(),null,!1,!1)});var el=/[\-:]([a-z])/g;function tl(e){return e[1].toUpperCase()}"accent-height alignment-baseline arabic-form baseline-shift cap-height clip-path clip-rule color-interpolation color-interpolation-filters color-profile color-rendering dominant-baseline enable-background fill-opacity fill-rule flood-color flood-opacity font-family font-size font-size-adjust font-stretch font-style font-variant font-weight glyph-name glyph-orientation-horizontal glyph-orientation-vertical horiz-adv-x horiz-origin-x image-rendering letter-spacing lighting-color marker-end marker-mid marker-start overline-position overline-thickness paint-order panose-1 pointer-events rendering-intent shape-rendering stop-color stop-opacity strikethrough-position strikethrough-thickness stroke-dasharray stroke-dashoffset stroke-linecap stroke-linejoin stroke-miterlimit stroke-opacity stroke-width text-anchor text-decoration text-rendering underline-position underline-thickness unicode-bidi unicode-range units-per-em v-alphabetic v-hanging v-ideographic v-mathematical vector-effect vert-adv-y vert-origin-x vert-origin-y word-spacing writing-mode xmlns:xlink x-height".split(" ").forEach(function(e){var t=e.replace(el,tl);fe[t]=new Pe(t,1,!1,e,null,!1,!1)});"xlink:actuate xlink:arcrole xlink:role xlink:show xlink:title xlink:type".split(" ").forEach(function(e){var t=e.replace(el,tl);fe[t]=new Pe(t,1,!1,e,"http://www.w3.org/1999/xlink",!1,!1)});["xml:base","xml:lang","xml:space"].forEach(function(e){var t=e.replace(el,tl);fe[t]=new Pe(t,1,!1,e,"http://www.w3.org/XML/1998/namespace",!1,!1)});["tabIndex","crossOrigin"].forEach(function(e){fe[e]=new Pe(e,1,!1,e.toLowerCase(),null,!1,!1)});fe.xlinkHref=new Pe("xlinkHref",1,!1,"xlink:href","http://www.w3.org/1999/xlink",!0,!1);["src","href","action","formAction"].forEach(function(e){fe[e]=new Pe(e,1,!1,e.toLowerCase(),null,!0,!0)});function nl(e,t,n,r){var s=fe.hasOwnProperty(t)?fe[t]:null;(s!==null?s.type!==0:r||!(2<t.length)||t[0]!=="o"&&t[0]!=="O"||t[1]!=="n"&&t[1]!=="N")&&(Ny(t,n,s,r)&&(n=null),r||s===null?Ey(t)&&(n===null?e.removeAttribute(t):e.setAttribute(t,""+n)):s.mustUseProperty?e[s.propertyName]=n===null?s.type===3?!1:"":n:(t=s.attributeName,r=s.attributeNamespace,n===null?e.removeAttribute(t):(s=s.type,n=s===3||s===4&&n===!0?"":""+n,r?e.setAttributeNS(r,t,n):e.setAttribute(t,n))))}var xt=Ty.__SECRET_INTERNALS_DO_NOT_USE_OR_YOU_WILL_BE_FIRED,gs=Symbol.for("react.element"),xn=Symbol.for("react.portal"),wn=Symbol.for("react.fragment"),rl=Symbol.for("react.strict_mode"),Uo=Symbol.for("react.profiler"),yd=Symbol.for("react.provider"),gd=Symbol.for("react.context"),sl=Symbol.for("react.forward_ref"),bo=Symbol.for("react.suspense"),$o=Symbol.for("react.suspense_list"),il=Symbol.for("react.memo"),Pt=Symbol.for("react.lazy"),vd=Symbol.for("react.offscreen"),Du=Symbol.iterator;function ir(e){return e===null||typeof e!="object"?null:(e=Du&&e[Du]||e["@@iterator"],typeof e=="function"?e:null)}var X=Object.assign,eo;function pr(e){if(eo===void 0)try{throw Error()}catch(n){var t=n.stack.trim().match(/\n( *(at )?)/);eo=t&&t[1]||""}return`
`+eo+e}var to=!1;function no(e,t){if(!e||to)return"";to=!0;var n=Error.prepareStackTrace;Error.prepareStackTrace=void 0;try{if(t)if(t=function(){throw Error()},Object.defineProperty(t.prototype,"props",{set:function(){throw Error()}}),typeof Reflect=="object"&&Reflect.construct){try{Reflect.construct(t,[])}catch(u){var r=u}Reflect.construct(e,[],t)}else{try{t.call()}catch(u){r=u}e.call(t.prototype)}else{try{throw Error()}catch(u){r=u}e()}}catch(u){if(u&&r&&typeof u.stack=="string"){for(var s=u.stack.split(`
`),i=r.stack.split(`
`),o=s.length-1,a=i.length-1;1<=o&&0<=a&&s[o]!==i[a];)a--;for(;1<=o&&0<=a;o--,a--)if(s[o]!==i[a]){if(o!==1||a!==1)do if(o--,a--,0>a||s[o]!==i[a]){var l=`
`+s[o].replace(" at new "," at ");return e.displayName&&l.includes("<anonymous>")&&(l=l.replace("<anonymous>",e.displayName)),l}while(1<=o&&0<=a);break}}}finally{to=!1,Error.prepareStackTrace=n}return(e=e?e.displayName||e.name:"")?pr(e):""}function Ay(e){switch(e.tag){case 5:return pr(e.type);case 16:return pr("Lazy");case 13:return pr("Suspense");case 19:return pr("SuspenseList");case 0:case 2:case 15:return e=no(e.type,!1),e;case 11:return e=no(e.type.render,!1),e;case 1:return e=no(e.type,!0),e;default:return""}}function Ho(e){if(e==null)return null;if(typeof e=="function")return e.displayName||e.name||null;if(typeof e=="string")return e;switch(e){case wn:return"Fragment";case xn:return"Portal";case Uo:return"Profiler";case rl:return"StrictMode";case bo:return"Suspense";case $o:return"SuspenseList"}if(typeof e=="object")switch(e.$$typeof){case gd:return(e.displayName||"Context")+".Consumer";case yd:return(e._context.displayName||"Context")+".Provider";case sl:var t=e.render;return e=e.displayName,e||(e=t.displayName||t.name||"",e=e!==""?"ForwardRef("+e+")":"ForwardRef"),e;case il:return t=e.displayName||null,t!==null?t:Ho(e.type)||"Memo";case Pt:t=e._payload,e=e._init;try{return Ho(e(t))}catch{}}return null}function My(e){var t=e.type;switch(e.tag){case 24:return"Cache";case 9:return(t.displayName||"Context")+".Consumer";case 10:return(t._context.displayName||"Context")+".Provider";case 18:return"DehydratedFragment";case 11:return e=t.render,e=e.displayName||e.name||"",t.displayName||(e!==""?"ForwardRef("+e+")":"ForwardRef");case 7:return"Fragment";case 5:return t;case 4:return"Portal";case 3:return"Root";case 6:return"Text";case 16:return Ho(t);case 8:return t===rl?"StrictMode":"Mode";case 22:return"Offscreen";case 12:return"Profiler";case 21:return"Scope";case 13:return"Suspense";case 19:return"SuspenseList";case 25:return"TracingMarker";case 1:case 0:case 17:case 2:case 14:case 15:if(typeof t=="function")return t.displayName||t.name||null;if(typeof t=="string")return t}return null}function It(e){switch(typeof e){case"boolean":case"number":case"string":case"undefined":return e;case"object":return e;default:return""}}function xd(e){var t=e.type;return(e=e.nodeName)&&e.toLowerCase()==="input"&&(t==="checkbox"||t==="radio")}function Dy(e){var t=xd(e)?"checked":"value",n=Object.getOwnPropertyDescriptor(e.constructor.prototype,t),r=""+e[t];if(!e.hasOwnProperty(t)&&typeof n<"u"&&typeof n.get=="function"&&typeof n.set=="function"){var s=n.get,i=n.set;return Object.defineProperty(e,t,{configurable:!0,get:function(){return s.call(this)},set:function(o){r=""+o,i.call(this,o)}}),Object.defineProperty(e,t,{enumerable:n.enumerable}),{getValue:function(){return r},setValue:function(o){r=""+o},stopTracking:function(){e._valueTracker=null,delete e[t]}}}}function vs(e){e._valueTracker||(e._valueTracker=Dy(e))}function wd(e){if(!e)return!1;var t=e._valueTracker;if(!t)return!0;var n=t.getValue(),r="";return e&&(r=xd(e)?e.checked?"true":"false":e.value),e=r,e!==n?(t.setValue(e),!0):!1}function Zs(e){if(e=e||(typeof document<"u"?document:void 0),typeof e>"u")return null;try{return e.activeElement||e.body}catch{return e.body}}function Wo(e,t){var n=t.checked;return X({},t,{defaultChecked:void 0,defaultValue:void 0,value:void 0,checked:n??e._wrapperState.initialChecked})}function Ru(e,t){var n=t.defaultValue==null?"":t.defaultValue,r=t.checked!=null?t.checked:t.defaultChecked;n=It(t.value!=null?t.value:n),e._wrapperState={initialChecked:r,initialValue:n,controlled:t.type==="checkbox"||t.type==="radio"?t.checked!=null:t.value!=null}}function Sd(e,t){t=t.checked,t!=null&&nl(e,"checked",t,!1)}function Ko(e,t){Sd(e,t);var n=It(t.value),r=t.type;if(n!=null)r==="number"?(n===0&&e.value===""||e.value!=n)&&(e.value=""+n):e.value!==""+n&&(e.value=""+n);else if(r==="submit"||r==="reset"){e.removeAttribute("value");return}t.hasOwnProperty("value")?Go(e,t.type,n):t.hasOwnProperty("defaultValue")&&Go(e,t.type,It(t.defaultValue)),t.checked==null&&t.defaultChecked!=null&&(e.defaultChecked=!!t.defaultChecked)}function Lu(e,t,n){if(t.hasOwnProperty("value")||t.hasOwnProperty("defaultValue")){var r=t.type;if(!(r!=="submit"&&r!=="reset"||t.value!==void 0&&t.value!==null))return;t=""+e._wrapperState.initialValue,n||t===e.value||(e.value=t),e.defaultValue=t}n=e.name,n!==""&&(e.name=""),e.defaultChecked=!!e._wrapperState.initialChecked,n!==""&&(e.name=n)}function Go(e,t,n){(t!=="number"||Zs(e.ownerDocument)!==e)&&(n==null?e.defaultValue=""+e._wrapperState.initialValue:e.defaultValue!==""+n&&(e.defaultValue=""+n))}var mr=Array.isArray;function Fn(e,t,n,r){if(e=e.options,t){t={};for(var s=0;s<n.length;s++)t["$"+n[s]]=!0;for(n=0;n<e.length;n++)s=t.hasOwnProperty("$"+e[n].value),e[n].selected!==s&&(e[n].selected=s),s&&r&&(e[n].defaultSelected=!0)}else{for(n=""+It(n),t=null,s=0;s<e.length;s++){if(e[s].value===n){e[s].selected=!0,r&&(e[s].defaultSelected=!0);return}t!==null||e[s].disabled||(t=e[s])}t!==null&&(t.selected=!0)}}function Qo(e,t){if(t.dangerouslySetInnerHTML!=null)throw Error(E(91));return X({},t,{value:void 0,defaultValue:void 0,children:""+e._wrapperState.initialValue})}function Vu(e,t){var n=t.value;if(n==null){if(n=t.children,t=t.defaultValue,n!=null){if(t!=null)throw Error(E(92));if(mr(n)){if(1<n.length)throw Error(E(93));n=n[0]}t=n}t==null&&(t=""),n=t}e._wrapperState={initialValue:It(n)}}function kd(e,t){var n=It(t.value),r=It(t.defaultValue);n!=null&&(n=""+n,n!==e.value&&(e.value=n),t.defaultValue==null&&e.defaultValue!==n&&(e.defaultValue=n)),r!=null&&(e.defaultValue=""+r)}function _u(e){var t=e.textContent;t===e._wrapperState.initialValue&&t!==""&&t!==null&&(e.value=t)}function Pd(e){switch(e){case"svg":return"http://www.w3.org/2000/svg";case"math":return"http://www.w3.org/1998/Math/MathML";default:return"http://www.w3.org/1999/xhtml"}}function Xo(e,t){return e==null||e==="http://www.w3.org/1999/xhtml"?Pd(t):e==="http://www.w3.org/2000/svg"&&t==="foreignObject"?"http://www.w3.org/1999/xhtml":e}var xs,Td=function(e){return typeof MSApp<"u"&&MSApp.execUnsafeLocalFunction?function(t,n,r,s){MSApp.execUnsafeLocalFunction(function(){return e(t,n,r,s)})}:e}(function(e,t){if(e.namespaceURI!=="http://www.w3.org/2000/svg"||"innerHTML"in e)e.innerHTML=t;else{for(xs=xs||document.createElement("div"),xs.innerHTML="<svg>"+t.valueOf().toString()+"</svg>",t=xs.firstChild;e.firstChild;)e.removeChild(e.firstChild);for(;t.firstChild;)e.appendChild(t.firstChild)}});function _r(e,t){if(t){var n=e.firstChild;if(n&&n===e.lastChild&&n.nodeType===3){n.nodeValue=t;return}}e.textContent=t}var Sr={animationIterationCount:!0,aspectRatio:!0,borderImageOutset:!0,borderImageSlice:!0,borderImageWidth:!0,boxFlex:!0,boxFlexGroup:!0,boxOrdinalGroup:!0,columnCount:!0,columns:!0,flex:!0,flexGrow:!0,flexPositive:!0,flexShrink:!0,flexNegative:!0,flexOrder:!0,gridArea:!0,gridRow:!0,gridRowEnd:!0,gridRowSpan:!0,gridRowStart:!0,gridColumn:!0,gridColumnEnd:!0,gridColumnSpan:!0,gridColumnStart:!0,fontWeight:!0,lineClamp:!0,lineHeight:!0,opacity:!0,order:!0,orphans:!0,tabSize:!0,widows:!0,zIndex:!0,zoom:!0,fillOpacity:!0,floodOpacity:!0,stopOpacity:!0,strokeDasharray:!0,strokeDashoffset:!0,strokeMiterlimit:!0,strokeOpacity:!0,strokeWidth:!0},Ry=["Webkit","ms","Moz","O"];Object.keys(Sr).forEach(function(e){Ry.forEach(function(t){t=t+e.charAt(0).toUpperCase()+e.substring(1),Sr[t]=Sr[e]})});function Cd(e,t,n){return t==null||typeof t=="boolean"||t===""?"":n||typeof t!="number"||t===0||Sr.hasOwnProperty(e)&&Sr[e]?(""+t).trim():t+"px"}function Ed(e,t){e=e.style;for(var n in t)if(t.hasOwnProperty(n)){var r=n.indexOf("--")===0,s=Cd(n,t[n],r);n==="float"&&(n="cssFloat"),r?e.setProperty(n,s):e[n]=s}}var Ly=X({menuitem:!0},{area:!0,base:!0,br:!0,col:!0,embed:!0,hr:!0,img:!0,input:!0,keygen:!0,link:!0,meta:!0,param:!0,source:!0,track:!0,wbr:!0});function Yo(e,t){if(t){if(Ly[e]&&(t.children!=null||t.dangerouslySetInnerHTML!=null))throw Error(E(137,e));if(t.dangerouslySetInnerHTML!=null){if(t.children!=null)throw Error(E(60));if(typeof t.dangerouslySetInnerHTML!="object"||!("__html"in t.dangerouslySetInnerHTML))throw Error(E(61))}if(t.style!=null&&typeof t.style!="object")throw Error(E(62))}}function qo(e,t){if(e.indexOf("-")===-1)return typeof t.is=="string";switch(e){case"annotation-xml":case"color-profile":case"font-face":case"font-face-src":case"font-face-uri":case"font-face-format":case"font-face-name":case"missing-glyph":return!1;default:return!0}}var Zo=null;function ol(e){return e=e.target||e.srcElement||window,e.correspondingUseElement&&(e=e.correspondingUseElement),e.nodeType===3?e.parentNode:e}var Jo=null,In=null,On=null;function Fu(e){if(e=as(e)){if(typeof Jo!="function")throw Error(E(280));var t=e.stateNode;t&&(t=Vi(t),Jo(e.stateNode,e.type,t))}}function jd(e){In?On?On.push(e):On=[e]:In=e}function Nd(){if(In){var e=In,t=On;if(On=In=null,Fu(e),t)for(e=0;e<t.length;e++)Fu(t[e])}}function Ad(e,t){return e(t)}function Md(){}var ro=!1;function Dd(e,t,n){if(ro)return e(t,n);ro=!0;try{return Ad(e,t,n)}finally{ro=!1,(In!==null||On!==null)&&(Md(),Nd())}}function Fr(e,t){var n=e.stateNode;if(n===null)return null;var r=Vi(n);if(r===null)return null;n=r[t];e:switch(t){case"onClick":case"onClickCapture":case"onDoubleClick":case"onDoubleClickCapture":case"onMouseDown":case"onMouseDownCapture":case"onMouseMove":case"onMouseMoveCapture":case"onMouseUp":case"onMouseUpCapture":case"onMouseEnter":(r=!r.disabled)||(e=e.type,r=!(e==="button"||e==="input"||e==="select"||e==="textarea")),e=!r;break e;default:e=!1}if(e)return null;if(n&&typeof n!="function")throw Error(E(231,t,typeof n));return n}var ea=!1;if(pt)try{var or={};Object.defineProperty(or,"passive",{get:function(){ea=!0}}),window.addEventListener("test",or,or),window.removeEventListener("test",or,or)}catch{ea=!1}function Vy(e,t,n,r,s,i,o,a,l){var u=Array.prototype.slice.call(arguments,3);try{t.apply(n,u)}catch(c){this.onError(c)}}var kr=!1,Js=null,ei=!1,ta=null,_y={onError:function(e){kr=!0,Js=e}};function Fy(e,t,n,r,s,i,o,a,l){kr=!1,Js=null,Vy.apply(_y,arguments)}function Iy(e,t,n,r,s,i,o,a,l){if(Fy.apply(this,arguments),kr){if(kr){var u=Js;kr=!1,Js=null}else throw Error(E(198));ei||(ei=!0,ta=u)}}function yn(e){var t=e,n=e;if(e.alternate)for(;t.return;)t=t.return;else{e=t;do t=e,t.flags&4098&&(n=t.return),e=t.return;while(e)}return t.tag===3?n:null}function Rd(e){if(e.tag===13){var t=e.memoizedState;if(t===null&&(e=e.alternate,e!==null&&(t=e.memoizedState)),t!==null)return t.dehydrated}return null}function Iu(e){if(yn(e)!==e)throw Error(E(188))}function Oy(e){var t=e.alternate;if(!t){if(t=yn(e),t===null)throw Error(E(188));return t!==e?null:e}for(var n=e,r=t;;){var s=n.return;if(s===null)break;var i=s.alternate;if(i===null){if(r=s.return,r!==null){n=r;continue}break}if(s.child===i.child){for(i=s.child;i;){if(i===n)return Iu(s),e;if(i===r)return Iu(s),t;i=i.sibling}throw Error(E(188))}if(n.return!==r.return)n=s,r=i;else{for(var o=!1,a=s.child;a;){if(a===n){o=!0,n=s,r=i;break}if(a===r){o=!0,r=s,n=i;break}a=a.sibling}if(!o){for(a=i.child;a;){if(a===n){o=!0,n=i,r=s;break}if(a===r){o=!0,r=i,n=s;break}a=a.sibling}if(!o)throw Error(E(189))}}if(n.alternate!==r)throw Error(E(190))}if(n.tag!==3)throw Error(E(188));return n.stateNode.current===n?e:t}function Ld(e){return e=Oy(e),e!==null?Vd(e):null}function Vd(e){if(e.tag===5||e.tag===6)return e;for(e=e.child;e!==null;){var t=Vd(e);if(t!==null)return t;e=e.sibling}return null}var _d=Le.unstable_scheduleCallback,Ou=Le.unstable_cancelCallback,zy=Le.unstable_shouldYield,By=Le.unstable_requestPaint,ee=Le.unstable_now,Uy=Le.unstable_getCurrentPriorityLevel,al=Le.unstable_ImmediatePriority,Fd=Le.unstable_UserBlockingPriority,ti=Le.unstable_NormalPriority,by=Le.unstable_LowPriority,Id=Le.unstable_IdlePriority,Mi=null,rt=null;function $y(e){if(rt&&typeof rt.onCommitFiberRoot=="function")try{rt.onCommitFiberRoot(Mi,e,void 0,(e.current.flags&128)===128)}catch{}}var qe=Math.clz32?Math.clz32:Ky,Hy=Math.log,Wy=Math.LN2;function Ky(e){return e>>>=0,e===0?32:31-(Hy(e)/Wy|0)|0}var ws=64,Ss=4194304;function yr(e){switch(e&-e){case 1:return 1;case 2:return 2;case 4:return 4;case 8:return 8;case 16:return 16;case 32:return 32;case 64:case 128:case 256:case 512:case 1024:case 2048:case 4096:case 8192:case 16384:case 32768:case 65536:case 131072:case 262144:case 524288:case 1048576:case 2097152:return e&4194240;case 4194304:case 8388608:case 16777216:case 33554432:case 67108864:return e&130023424;case 134217728:return 134217728;case 268435456:return 268435456;case 536870912:return 536870912;case 1073741824:return 1073741824;default:return e}}function ni(e,t){var n=e.pendingLanes;if(n===0)return 0;var r=0,s=e.suspendedLanes,i=e.pingedLanes,o=n&268435455;if(o!==0){var a=o&~s;a!==0?r=yr(a):(i&=o,i!==0&&(r=yr(i)))}else o=n&~s,o!==0?r=yr(o):i!==0&&(r=yr(i));if(r===0)return 0;if(t!==0&&t!==r&&!(t&s)&&(s=r&-r,i=t&-t,s>=i||s===16&&(i&4194240)!==0))return t;if(r&4&&(r|=n&16),t=e.entangledLanes,t!==0)for(e=e.entanglements,t&=r;0<t;)n=31-qe(t),s=1<<n,r|=e[n],t&=~s;return r}function Gy(e,t){switch(e){case 1:case 2:case 4:return t+250;case 8:case 16:case 32:case 64:case 128:case 256:case 512:case 1024:case 2048:case 4096:case 8192:case 16384:case 32768:case 65536:case 131072:case 262144:case 524288:case 1048576:case 2097152:return t+5e3;case 4194304:case 8388608:case 16777216:case 33554432:case 67108864:return-1;case 134217728:case 268435456:case 536870912:case 1073741824:return-1;default:return-1}}function Qy(e,t){for(var n=e.suspendedLanes,r=e.pingedLanes,s=e.expirationTimes,i=e.pendingLanes;0<i;){var o=31-qe(i),a=1<<o,l=s[o];l===-1?(!(a&n)||a&r)&&(s[o]=Gy(a,t)):l<=t&&(e.expiredLanes|=a),i&=~a}}function na(e){return e=e.pendingLanes&-1073741825,e!==0?e:e&1073741824?1073741824:0}function Od(){var e=ws;return ws<<=1,!(ws&4194240)&&(ws=64),e}function so(e){for(var t=[],n=0;31>n;n++)t.push(e);return t}function is(e,t,n){e.pendingLanes|=t,t!==536870912&&(e.suspendedLanes=0,e.pingedLanes=0),e=e.eventTimes,t=31-qe(t),e[t]=n}function Xy(e,t){var n=e.pendingLanes&~t;e.pendingLanes=t,e.suspendedLanes=0,e.pingedLanes=0,e.expiredLanes&=t,e.mutableReadLanes&=t,e.entangledLanes&=t,t=e.entanglements;var r=e.eventTimes;for(e=e.expirationTimes;0<n;){var s=31-qe(n),i=1<<s;t[s]=0,r[s]=-1,e[s]=-1,n&=~i}}function ll(e,t){var n=e.entangledLanes|=t;for(e=e.entanglements;n;){var r=31-qe(n),s=1<<r;s&t|e[r]&t&&(e[r]|=t),n&=~s}}var z=0;function zd(e){return e&=-e,1<e?4<e?e&268435455?16:536870912:4:1}var Bd,ul,Ud,bd,$d,ra=!1,ks=[],At=null,Mt=null,Dt=null,Ir=new Map,Or=new Map,Ct=[],Yy="mousedown mouseup touchcancel touchend touchstart auxclick dblclick pointercancel pointerdown pointerup dragend dragstart drop compositionend compositionstart keydown keypress keyup input textInput copy cut paste click change contextmenu reset submit".split(" ");function zu(e,t){switch(e){case"focusin":case"focusout":At=null;break;case"dragenter":case"dragleave":Mt=null;break;case"mouseover":case"mouseout":Dt=null;break;case"pointerover":case"pointerout":Ir.delete(t.pointerId);break;case"gotpointercapture":case"lostpointercapture":Or.delete(t.pointerId)}}function ar(e,t,n,r,s,i){return e===null||e.nativeEvent!==i?(e={blockedOn:t,domEventName:n,eventSystemFlags:r,nativeEvent:i,targetContainers:[s]},t!==null&&(t=as(t),t!==null&&ul(t)),e):(e.eventSystemFlags|=r,t=e.targetContainers,s!==null&&t.indexOf(s)===-1&&t.push(s),e)}function qy(e,t,n,r,s){switch(t){case"focusin":return At=ar(At,e,t,n,r,s),!0;case"dragenter":return Mt=ar(Mt,e,t,n,r,s),!0;case"mouseover":return Dt=ar(Dt,e,t,n,r,s),!0;case"pointerover":var i=s.pointerId;return Ir.set(i,ar(Ir.get(i)||null,e,t,n,r,s)),!0;case"gotpointercapture":return i=s.pointerId,Or.set(i,ar(Or.get(i)||null,e,t,n,r,s)),!0}return!1}function Hd(e){var t=tn(e.target);if(t!==null){var n=yn(t);if(n!==null){if(t=n.tag,t===13){if(t=Rd(n),t!==null){e.blockedOn=t,$d(e.priority,function(){Ud(n)});return}}else if(t===3&&n.stateNode.current.memoizedState.isDehydrated){e.blockedOn=n.tag===3?n.stateNode.containerInfo:null;return}}}e.blockedOn=null}function zs(e){if(e.blockedOn!==null)return!1;for(var t=e.targetContainers;0<t.length;){var n=sa(e.domEventName,e.eventSystemFlags,t[0],e.nativeEvent);if(n===null){n=e.nativeEvent;var r=new n.constructor(n.type,n);Zo=r,n.target.dispatchEvent(r),Zo=null}else return t=as(n),t!==null&&ul(t),e.blockedOn=n,!1;t.shift()}return!0}function Bu(e,t,n){zs(e)&&n.delete(t)}function Zy(){ra=!1,At!==null&&zs(At)&&(At=null),Mt!==null&&zs(Mt)&&(Mt=null),Dt!==null&&zs(Dt)&&(Dt=null),Ir.forEach(Bu),Or.forEach(Bu)}function lr(e,t){e.blockedOn===t&&(e.blockedOn=null,ra||(ra=!0,Le.unstable_scheduleCallback(Le.unstable_NormalPriority,Zy)))}function zr(e){function t(s){return lr(s,e)}if(0<ks.length){lr(ks[0],e);for(var n=1;n<ks.length;n++){var r=ks[n];r.blockedOn===e&&(r.blockedOn=null)}}for(At!==null&&lr(At,e),Mt!==null&&lr(Mt,e),Dt!==null&&lr(Dt,e),Ir.forEach(t),Or.forEach(t),n=0;n<Ct.length;n++)r=Ct[n],r.blockedOn===e&&(r.blockedOn=null);for(;0<Ct.length&&(n=Ct[0],n.blockedOn===null);)Hd(n),n.blockedOn===null&&Ct.shift()}var zn=xt.ReactCurrentBatchConfig,ri=!0;function Jy(e,t,n,r){var s=z,i=zn.transition;zn.transition=null;try{z=1,cl(e,t,n,r)}finally{z=s,zn.transition=i}}function eg(e,t,n,r){var s=z,i=zn.transition;zn.transition=null;try{z=4,cl(e,t,n,r)}finally{z=s,zn.transition=i}}function cl(e,t,n,r){if(ri){var s=sa(e,t,n,r);if(s===null)mo(e,t,r,si,n),zu(e,r);else if(qy(s,e,t,n,r))r.stopPropagation();else if(zu(e,r),t&4&&-1<Yy.indexOf(e)){for(;s!==null;){var i=as(s);if(i!==null&&Bd(i),i=sa(e,t,n,r),i===null&&mo(e,t,r,si,n),i===s)break;s=i}s!==null&&r.stopPropagation()}else mo(e,t,r,null,n)}}var si=null;function sa(e,t,n,r){if(si=null,e=ol(r),e=tn(e),e!==null)if(t=yn(e),t===null)e=null;else if(n=t.tag,n===13){if(e=Rd(t),e!==null)return e;e=null}else if(n===3){if(t.stateNode.current.memoizedState.isDehydrated)return t.tag===3?t.stateNode.containerInfo:null;e=null}else t!==e&&(e=null);return si=e,null}function Wd(e){switch(e){case"cancel":case"click":case"close":case"contextmenu":case"copy":case"cut":case"auxclick":case"dblclick":case"dragend":case"dragstart":case"drop":case"focusin":case"focusout":case"input":case"invalid":case"keydown":case"keypress":case"keyup":case"mousedown":case"mouseup":case"paste":case"pause":case"play":case"pointercancel":case"pointerdown":case"pointerup":case"ratechange":case"reset":case"resize":case"seeked":case"submit":case"touchcancel":case"touchend":case"touchstart":case"volumechange":case"change":case"selectionchange":case"textInput":case"compositionstart":case"compositionend":case"compositionupdate":case"beforeblur":case"afterblur":case"beforeinput":case"blur":case"fullscreenchange":case"focus":case"hashchange":case"popstate":case"select":case"selectstart":return 1;case"drag":case"dragenter":case"dragexit":case"dragleave":case"dragover":case"mousemove":case"mouseout":case"mouseover":case"pointermove":case"pointerout":case"pointerover":case"scroll":case"toggle":case"touchmove":case"wheel":case"mouseenter":case"mouseleave":case"pointerenter":case"pointerleave":return 4;case"message":switch(Uy()){case al:return 1;case Fd:return 4;case ti:case by:return 16;case Id:return 536870912;default:return 16}default:return 16}}var jt=null,fl=null,Bs=null;function Kd(){if(Bs)return Bs;var e,t=fl,n=t.length,r,s="value"in jt?jt.value:jt.textContent,i=s.length;for(e=0;e<n&&t[e]===s[e];e++);var o=n-e;for(r=1;r<=o&&t[n-r]===s[i-r];r++);return Bs=s.slice(e,1<r?1-r:void 0)}function Us(e){var t=e.keyCode;return"charCode"in e?(e=e.charCode,e===0&&t===13&&(e=13)):e=t,e===10&&(e=13),32<=e||e===13?e:0}function Ps(){return!0}function Uu(){return!1}function Fe(e){function t(n,r,s,i,o){this._reactName=n,this._targetInst=s,this.type=r,this.nativeEvent=i,this.target=o,this.currentTarget=null;for(var a in e)e.hasOwnProperty(a)&&(n=e[a],this[a]=n?n(i):i[a]);return this.isDefaultPrevented=(i.defaultPrevented!=null?i.defaultPrevented:i.returnValue===!1)?Ps:Uu,this.isPropagationStopped=Uu,this}return X(t.prototype,{preventDefault:function(){this.defaultPrevented=!0;var n=this.nativeEvent;n&&(n.preventDefault?n.preventDefault():typeof n.returnValue!="unknown"&&(n.returnValue=!1),this.isDefaultPrevented=Ps)},stopPropagation:function(){var n=this.nativeEvent;n&&(n.stopPropagation?n.stopPropagation():typeof n.cancelBubble!="unknown"&&(n.cancelBubble=!0),this.isPropagationStopped=Ps)},persist:function(){},isPersistent:Ps}),t}var Jn={eventPhase:0,bubbles:0,cancelable:0,timeStamp:function(e){return e.timeStamp||Date.now()},defaultPrevented:0,isTrusted:0},dl=Fe(Jn),os=X({},Jn,{view:0,detail:0}),tg=Fe(os),io,oo,ur,Di=X({},os,{screenX:0,screenY:0,clientX:0,clientY:0,pageX:0,pageY:0,ctrlKey:0,shiftKey:0,altKey:0,metaKey:0,getModifierState:hl,button:0,buttons:0,relatedTarget:function(e){return e.relatedTarget===void 0?e.fromElement===e.srcElement?e.toElement:e.fromElement:e.relatedTarget},movementX:function(e){return"movementX"in e?e.movementX:(e!==ur&&(ur&&e.type==="mousemove"?(io=e.screenX-ur.screenX,oo=e.screenY-ur.screenY):oo=io=0,ur=e),io)},movementY:function(e){return"movementY"in e?e.movementY:oo}}),bu=Fe(Di),ng=X({},Di,{dataTransfer:0}),rg=Fe(ng),sg=X({},os,{relatedTarget:0}),ao=Fe(sg),ig=X({},Jn,{animationName:0,elapsedTime:0,pseudoElement:0}),og=Fe(ig),ag=X({},Jn,{clipboardData:function(e){return"clipboardData"in e?e.clipboardData:window.clipboardData}}),lg=Fe(ag),ug=X({},Jn,{data:0}),$u=Fe(ug),cg={Esc:"Escape",Spacebar:" ",Left:"ArrowLeft",Up:"ArrowUp",Right:"ArrowRight",Down:"ArrowDown",Del:"Delete",Win:"OS",Menu:"ContextMenu",Apps:"ContextMenu",Scroll:"ScrollLock",MozPrintableKey:"Unidentified"},fg={8:"Backspace",9:"Tab",12:"Clear",13:"Enter",16:"Shift",17:"Control",18:"Alt",19:"Pause",20:"CapsLock",27:"Escape",32:" ",33:"PageUp",34:"PageDown",35:"End",36:"Home",37:"ArrowLeft",38:"ArrowUp",39:"ArrowRight",40:"ArrowDown",45:"Insert",46:"Delete",112:"F1",113:"F2",114:"F3",115:"F4",116:"F5",117:"F6",118:"F7",119:"F8",120:"F9",121:"F10",122:"F11",123:"F12",144:"NumLock",145:"ScrollLock",224:"Meta"},dg={Alt:"altKey",Control:"ctrlKey",Meta:"metaKey",Shift:"shiftKey"};function hg(e){var t=this.nativeEvent;return t.getModifierState?t.getModifierState(e):(e=dg[e])?!!t[e]:!1}function hl(){return hg}var pg=X({},os,{key:function(e){if(e.key){var t=cg[e.key]||e.key;if(t!=="Unidentified")return t}return e.type==="keypress"?(e=Us(e),e===13?"Enter":String.fromCharCode(e)):e.type==="keydown"||e.type==="keyup"?fg[e.keyCode]||"Unidentified":""},code:0,location:0,ctrlKey:0,shiftKey:0,altKey:0,metaKey:0,repeat:0,locale:0,getModifierState:hl,charCode:function(e){return e.type==="keypress"?Us(e):0},keyCode:function(e){return e.type==="keydown"||e.type==="keyup"?e.keyCode:0},which:function(e){return e.type==="keypress"?Us(e):e.type==="keydown"||e.type==="keyup"?e.keyCode:0}}),mg=Fe(pg),yg=X({},Di,{pointerId:0,width:0,height:0,pressure:0,tangentialPressure:0,tiltX:0,tiltY:0,twist:0,pointerType:0,isPrimary:0}),Hu=Fe(yg),gg=X({},os,{touches:0,targetTouches:0,changedTouches:0,altKey:0,metaKey:0,ctrlKey:0,shiftKey:0,getModifierState:hl}),vg=Fe(gg),xg=X({},Jn,{propertyName:0,elapsedTime:0,pseudoElement:0}),wg=Fe(xg),Sg=X({},Di,{deltaX:function(e){return"deltaX"in e?e.deltaX:"wheelDeltaX"in e?-e.wheelDeltaX:0},deltaY:function(e){return"deltaY"in e?e.deltaY:"wheelDeltaY"in e?-e.wheelDeltaY:"wheelDelta"in e?-e.wheelDelta:0},deltaZ:0,deltaMode:0}),kg=Fe(Sg),Pg=[9,13,27,32],pl=pt&&"CompositionEvent"in window,Pr=null;pt&&"documentMode"in document&&(Pr=document.documentMode);var Tg=pt&&"TextEvent"in window&&!Pr,Gd=pt&&(!pl||Pr&&8<Pr&&11>=Pr),Wu=" ",Ku=!1;function Qd(e,t){switch(e){case"keyup":return Pg.indexOf(t.keyCode)!==-1;case"keydown":return t.keyCode!==229;case"keypress":case"mousedown":case"focusout":return!0;default:return!1}}function Xd(e){return e=e.detail,typeof e=="object"&&"data"in e?e.data:null}var Sn=!1;function Cg(e,t){switch(e){case"compositionend":return Xd(t);case"keypress":return t.which!==32?null:(Ku=!0,Wu);case"textInput":return e=t.data,e===Wu&&Ku?null:e;default:return null}}function Eg(e,t){if(Sn)return e==="compositionend"||!pl&&Qd(e,t)?(e=Kd(),Bs=fl=jt=null,Sn=!1,e):null;switch(e){case"paste":return null;case"keypress":if(!(t.ctrlKey||t.altKey||t.metaKey)||t.ctrlKey&&t.altKey){if(t.char&&1<t.char.length)return t.char;if(t.which)return String.fromCharCode(t.which)}return null;case"compositionend":return Gd&&t.locale!=="ko"?null:t.data;default:return null}}var jg={color:!0,date:!0,datetime:!0,"datetime-local":!0,email:!0,month:!0,number:!0,password:!0,range:!0,search:!0,tel:!0,text:!0,time:!0,url:!0,week:!0};function Gu(e){var t=e&&e.nodeName&&e.nodeName.toLowerCase();return t==="input"?!!jg[e.type]:t==="textarea"}function Yd(e,t,n,r){jd(r),t=ii(t,"onChange"),0<t.length&&(n=new dl("onChange","change",null,n,r),e.push({event:n,listeners:t}))}var Tr=null,Br=null;function Ng(e){ah(e,0)}function Ri(e){var t=Tn(e);if(wd(t))return e}function Ag(e,t){if(e==="change")return t}var qd=!1;if(pt){var lo;if(pt){var uo="oninput"in document;if(!uo){var Qu=document.createElement("div");Qu.setAttribute("oninput","return;"),uo=typeof Qu.oninput=="function"}lo=uo}else lo=!1;qd=lo&&(!document.documentMode||9<document.documentMode)}function Xu(){Tr&&(Tr.detachEvent("onpropertychange",Zd),Br=Tr=null)}function Zd(e){if(e.propertyName==="value"&&Ri(Br)){var t=[];Yd(t,Br,e,ol(e)),Dd(Ng,t)}}function Mg(e,t,n){e==="focusin"?(Xu(),Tr=t,Br=n,Tr.attachEvent("onpropertychange",Zd)):e==="focusout"&&Xu()}function Dg(e){if(e==="selectionchange"||e==="keyup"||e==="keydown")return Ri(Br)}function Rg(e,t){if(e==="click")return Ri(t)}function Lg(e,t){if(e==="input"||e==="change")return Ri(t)}function Vg(e,t){return e===t&&(e!==0||1/e===1/t)||e!==e&&t!==t}var Je=typeof Object.is=="function"?Object.is:Vg;function Ur(e,t){if(Je(e,t))return!0;if(typeof e!="object"||e===null||typeof t!="object"||t===null)return!1;var n=Object.keys(e),r=Object.keys(t);if(n.length!==r.length)return!1;for(r=0;r<n.length;r++){var s=n[r];if(!Bo.call(t,s)||!Je(e[s],t[s]))return!1}return!0}function Yu(e){for(;e&&e.firstChild;)e=e.firstChild;return e}function qu(e,t){var n=Yu(e);e=0;for(var r;n;){if(n.nodeType===3){if(r=e+n.textContent.length,e<=t&&r>=t)return{node:n,offset:t-e};e=r}e:{for(;n;){if(n.nextSibling){n=n.nextSibling;break e}n=n.parentNode}n=void 0}n=Yu(n)}}function Jd(e,t){return e&&t?e===t?!0:e&&e.nodeType===3?!1:t&&t.nodeType===3?Jd(e,t.parentNode):"contains"in e?e.contains(t):e.compareDocumentPosition?!!(e.compareDocumentPosition(t)&16):!1:!1}function eh(){for(var e=window,t=Zs();t instanceof e.HTMLIFrameElement;){try{var n=typeof t.contentWindow.location.href=="string"}catch{n=!1}if(n)e=t.contentWindow;else break;t=Zs(e.document)}return t}function ml(e){var t=e&&e.nodeName&&e.nodeName.toLowerCase();return t&&(t==="input"&&(e.type==="text"||e.type==="search"||e.type==="tel"||e.type==="url"||e.type==="password")||t==="textarea"||e.contentEditable==="true")}function _g(e){var t=eh(),n=e.focusedElem,r=e.selectionRange;if(t!==n&&n&&n.ownerDocument&&Jd(n.ownerDocument.documentElement,n)){if(r!==null&&ml(n)){if(t=r.start,e=r.end,e===void 0&&(e=t),"selectionStart"in n)n.selectionStart=t,n.selectionEnd=Math.min(e,n.value.length);else if(e=(t=n.ownerDocument||document)&&t.defaultView||window,e.getSelection){e=e.getSelection();var s=n.textContent.length,i=Math.min(r.start,s);r=r.end===void 0?i:Math.min(r.end,s),!e.extend&&i>r&&(s=r,r=i,i=s),s=qu(n,i);var o=qu(n,r);s&&o&&(e.rangeCount!==1||e.anchorNode!==s.node||e.anchorOffset!==s.offset||e.focusNode!==o.node||e.focusOffset!==o.offset)&&(t=t.createRange(),t.setStart(s.node,s.offset),e.removeAllRanges(),i>r?(e.addRange(t),e.extend(o.node,o.offset)):(t.setEnd(o.node,o.offset),e.addRange(t)))}}for(t=[],e=n;e=e.parentNode;)e.nodeType===1&&t.push({element:e,left:e.scrollLeft,top:e.scrollTop});for(typeof n.focus=="function"&&n.focus(),n=0;n<t.length;n++)e=t[n],e.element.scrollLeft=e.left,e.element.scrollTop=e.top}}var Fg=pt&&"documentMode"in document&&11>=document.documentMode,kn=null,ia=null,Cr=null,oa=!1;function Zu(e,t,n){var r=n.window===n?n.document:n.nodeType===9?n:n.ownerDocument;oa||kn==null||kn!==Zs(r)||(r=kn,"selectionStart"in r&&ml(r)?r={start:r.selectionStart,end:r.selectionEnd}:(r=(r.ownerDocument&&r.ownerDocument.defaultView||window).getSelection(),r={anchorNode:r.anchorNode,anchorOffset:r.anchorOffset,focusNode:r.focusNode,focusOffset:r.focusOffset}),Cr&&Ur(Cr,r)||(Cr=r,r=ii(ia,"onSelect"),0<r.length&&(t=new dl("onSelect","select",null,t,n),e.push({event:t,listeners:r}),t.target=kn)))}function Ts(e,t){var n={};return n[e.toLowerCase()]=t.toLowerCase(),n["Webkit"+e]="webkit"+t,n["Moz"+e]="moz"+t,n}var Pn={animationend:Ts("Animation","AnimationEnd"),animationiteration:Ts("Animation","AnimationIteration"),animationstart:Ts("Animation","AnimationStart"),transitionend:Ts("Transition","TransitionEnd")},co={},th={};pt&&(th=document.createElement("div").style,"AnimationEvent"in window||(delete Pn.animationend.animation,delete Pn.animationiteration.animation,delete Pn.animationstart.animation),"TransitionEvent"in window||delete Pn.transitionend.transition);function Li(e){if(co[e])return co[e];if(!Pn[e])return e;var t=Pn[e],n;for(n in t)if(t.hasOwnProperty(n)&&n in th)return co[e]=t[n];return e}var nh=Li("animationend"),rh=Li("animationiteration"),sh=Li("animationstart"),ih=Li("transitionend"),oh=new Map,Ju="abort auxClick cancel canPlay canPlayThrough click close contextMenu copy cut drag dragEnd dragEnter dragExit dragLeave dragOver dragStart drop durationChange emptied encrypted ended error gotPointerCapture input invalid keyDown keyPress keyUp load loadedData loadedMetadata loadStart lostPointerCapture mouseDown mouseMove mouseOut mouseOver mouseUp paste pause play playing pointerCancel pointerDown pointerMove pointerOut pointerOver pointerUp progress rateChange reset resize seeked seeking stalled submit suspend timeUpdate touchCancel touchEnd touchStart volumeChange scroll toggle touchMove waiting wheel".split(" ");function bt(e,t){oh.set(e,t),mn(t,[e])}for(var fo=0;fo<Ju.length;fo++){var ho=Ju[fo],Ig=ho.toLowerCase(),Og=ho[0].toUpperCase()+ho.slice(1);bt(Ig,"on"+Og)}bt(nh,"onAnimationEnd");bt(rh,"onAnimationIteration");bt(sh,"onAnimationStart");bt("dblclick","onDoubleClick");bt("focusin","onFocus");bt("focusout","onBlur");bt(ih,"onTransitionEnd");bn("onMouseEnter",["mouseout","mouseover"]);bn("onMouseLeave",["mouseout","mouseover"]);bn("onPointerEnter",["pointerout","pointerover"]);bn("onPointerLeave",["pointerout","pointerover"]);mn("onChange","change click focusin focusout input keydown keyup selectionchange".split(" "));mn("onSelect","focusout contextmenu dragend focusin keydown keyup mousedown mouseup selectionchange".split(" "));mn("onBeforeInput",["compositionend","keypress","textInput","paste"]);mn("onCompositionEnd","compositionend focusout keydown keypress keyup mousedown".split(" "));mn("onCompositionStart","compositionstart focusout keydown keypress keyup mousedown".split(" "));mn("onCompositionUpdate","compositionupdate focusout keydown keypress keyup mousedown".split(" "));var gr="abort canplay canplaythrough durationchange emptied encrypted ended error loadeddata loadedmetadata loadstart pause play playing progress ratechange resize seeked seeking stalled suspend timeupdate volumechange waiting".split(" "),zg=new Set("cancel close invalid load scroll toggle".split(" ").concat(gr));function ec(e,t,n){var r=e.type||"unknown-event";e.currentTarget=n,Iy(r,t,void 0,e),e.currentTarget=null}function ah(e,t){t=(t&4)!==0;for(var n=0;n<e.length;n++){var r=e[n],s=r.event;r=r.listeners;e:{var i=void 0;if(t)for(var o=r.length-1;0<=o;o--){var a=r[o],l=a.instance,u=a.currentTarget;if(a=a.listener,l!==i&&s.isPropagationStopped())break e;ec(s,a,u),i=l}else for(o=0;o<r.length;o++){if(a=r[o],l=a.instance,u=a.currentTarget,a=a.listener,l!==i&&s.isPropagationStopped())break e;ec(s,a,u),i=l}}}if(ei)throw e=ta,ei=!1,ta=null,e}function U(e,t){var n=t[fa];n===void 0&&(n=t[fa]=new Set);var r=e+"__bubble";n.has(r)||(lh(t,e,2,!1),n.add(r))}function po(e,t,n){var r=0;t&&(r|=4),lh(n,e,r,t)}var Cs="_reactListening"+Math.random().toString(36).slice(2);function br(e){if(!e[Cs]){e[Cs]=!0,md.forEach(function(n){n!=="selectionchange"&&(zg.has(n)||po(n,!1,e),po(n,!0,e))});var t=e.nodeType===9?e:e.ownerDocument;t===null||t[Cs]||(t[Cs]=!0,po("selectionchange",!1,t))}}function lh(e,t,n,r){switch(Wd(t)){case 1:var s=Jy;break;case 4:s=eg;break;default:s=cl}n=s.bind(null,t,n,e),s=void 0,!ea||t!=="touchstart"&&t!=="touchmove"&&t!=="wheel"||(s=!0),r?s!==void 0?e.addEventListener(t,n,{capture:!0,passive:s}):e.addEventListener(t,n,!0):s!==void 0?e.addEventListener(t,n,{passive:s}):e.addEventListener(t,n,!1)}function mo(e,t,n,r,s){var i=r;if(!(t&1)&&!(t&2)&&r!==null)e:for(;;){if(r===null)return;var o=r.tag;if(o===3||o===4){var a=r.stateNode.containerInfo;if(a===s||a.nodeType===8&&a.parentNode===s)break;if(o===4)for(o=r.return;o!==null;){var l=o.tag;if((l===3||l===4)&&(l=o.stateNode.containerInfo,l===s||l.nodeType===8&&l.parentNode===s))return;o=o.return}for(;a!==null;){if(o=tn(a),o===null)return;if(l=o.tag,l===5||l===6){r=i=o;continue e}a=a.parentNode}}r=r.return}Dd(function(){var u=i,c=ol(n),d=[];e:{var h=oh.get(e);if(h!==void 0){var g=dl,v=e;switch(e){case"keypress":if(Us(n)===0)break e;case"keydown":case"keyup":g=mg;break;case"focusin":v="focus",g=ao;break;case"focusout":v="blur",g=ao;break;case"beforeblur":case"afterblur":g=ao;break;case"click":if(n.button===2)break e;case"auxclick":case"dblclick":case"mousedown":case"mousemove":case"mouseup":case"mouseout":case"mouseover":case"contextmenu":g=bu;break;case"drag":case"dragend":case"dragenter":case"dragexit":case"dragleave":case"dragover":case"dragstart":case"drop":g=rg;break;case"touchcancel":case"touchend":case"touchmove":case"touchstart":g=vg;break;case nh:case rh:case sh:g=og;break;case ih:g=wg;break;case"scroll":g=tg;break;case"wheel":g=kg;break;case"copy":case"cut":case"paste":g=lg;break;case"gotpointercapture":case"lostpointercapture":case"pointercancel":case"pointerdown":case"pointermove":case"pointerout":case"pointerover":case"pointerup":g=Hu}var x=(t&4)!==0,P=!x&&e==="scroll",m=x?h!==null?h+"Capture":null:h;x=[];for(var p=u,y;p!==null;){y=p;var w=y.stateNode;if(y.tag===5&&w!==null&&(y=w,m!==null&&(w=Fr(p,m),w!=null&&x.push($r(p,w,y)))),P)break;p=p.return}0<x.length&&(h=new g(h,v,null,n,c),d.push({event:h,listeners:x}))}}if(!(t&7)){e:{if(h=e==="mouseover"||e==="pointerover",g=e==="mouseout"||e==="pointerout",h&&n!==Zo&&(v=n.relatedTarget||n.fromElement)&&(tn(v)||v[mt]))break e;if((g||h)&&(h=c.window===c?c:(h=c.ownerDocument)?h.defaultView||h.parentWindow:window,g?(v=n.relatedTarget||n.toElement,g=u,v=v?tn(v):null,v!==null&&(P=yn(v),v!==P||v.tag!==5&&v.tag!==6)&&(v=null)):(g=null,v=u),g!==v)){if(x=bu,w="onMouseLeave",m="onMouseEnter",p="mouse",(e==="pointerout"||e==="pointerover")&&(x=Hu,w="onPointerLeave",m="onPointerEnter",p="pointer"),P=g==null?h:Tn(g),y=v==null?h:Tn(v),h=new x(w,p+"leave",g,n,c),h.target=P,h.relatedTarget=y,w=null,tn(c)===u&&(x=new x(m,p+"enter",v,n,c),x.target=y,x.relatedTarget=P,w=x),P=w,g&&v)t:{for(x=g,m=v,p=0,y=x;y;y=vn(y))p++;for(y=0,w=m;w;w=vn(w))y++;for(;0<p-y;)x=vn(x),p--;for(;0<y-p;)m=vn(m),y--;for(;p--;){if(x===m||m!==null&&x===m.alternate)break t;x=vn(x),m=vn(m)}x=null}else x=null;g!==null&&tc(d,h,g,x,!1),v!==null&&P!==null&&tc(d,P,v,x,!0)}}e:{if(h=u?Tn(u):window,g=h.nodeName&&h.nodeName.toLowerCase(),g==="select"||g==="input"&&h.type==="file")var S=Ag;else if(Gu(h))if(qd)S=Lg;else{S=Dg;var C=Mg}else(g=h.nodeName)&&g.toLowerCase()==="input"&&(h.type==="checkbox"||h.type==="radio")&&(S=Rg);if(S&&(S=S(e,u))){Yd(d,S,n,c);break e}C&&C(e,h,u),e==="focusout"&&(C=h._wrapperState)&&C.controlled&&h.type==="number"&&Go(h,"number",h.value)}switch(C=u?Tn(u):window,e){case"focusin":(Gu(C)||C.contentEditable==="true")&&(kn=C,ia=u,Cr=null);break;case"focusout":Cr=ia=kn=null;break;case"mousedown":oa=!0;break;case"contextmenu":case"mouseup":case"dragend":oa=!1,Zu(d,n,c);break;case"selectionchange":if(Fg)break;case"keydown":case"keyup":Zu(d,n,c)}var j;if(pl)e:{switch(e){case"compositionstart":var T="onCompositionStart";break e;case"compositionend":T="onCompositionEnd";break e;case"compositionupdate":T="onCompositionUpdate";break e}T=void 0}else Sn?Qd(e,n)&&(T="onCompositionEnd"):e==="keydown"&&n.keyCode===229&&(T="onCompositionStart");T&&(Gd&&n.locale!=="ko"&&(Sn||T!=="onCompositionStart"?T==="onCompositionEnd"&&Sn&&(j=Kd()):(jt=c,fl="value"in jt?jt.value:jt.textContent,Sn=!0)),C=ii(u,T),0<C.length&&(T=new $u(T,e,null,n,c),d.push({event:T,listeners:C}),j?T.data=j:(j=Xd(n),j!==null&&(T.data=j)))),(j=Tg?Cg(e,n):Eg(e,n))&&(u=ii(u,"onBeforeInput"),0<u.length&&(c=new $u("onBeforeInput","beforeinput",null,n,c),d.push({event:c,listeners:u}),c.data=j))}ah(d,t)})}function $r(e,t,n){return{instance:e,listener:t,currentTarget:n}}function ii(e,t){for(var n=t+"Capture",r=[];e!==null;){var s=e,i=s.stateNode;s.tag===5&&i!==null&&(s=i,i=Fr(e,n),i!=null&&r.unshift($r(e,i,s)),i=Fr(e,t),i!=null&&r.push($r(e,i,s))),e=e.return}return r}function vn(e){if(e===null)return null;do e=e.return;while(e&&e.tag!==5);return e||null}function tc(e,t,n,r,s){for(var i=t._reactName,o=[];n!==null&&n!==r;){var a=n,l=a.alternate,u=a.stateNode;if(l!==null&&l===r)break;a.tag===5&&u!==null&&(a=u,s?(l=Fr(n,i),l!=null&&o.unshift($r(n,l,a))):s||(l=Fr(n,i),l!=null&&o.push($r(n,l,a)))),n=n.return}o.length!==0&&e.push({event:t,listeners:o})}var Bg=/\r\n?/g,Ug=/\u0000|\uFFFD/g;function nc(e){return(typeof e=="string"?e:""+e).replace(Bg,`
`).replace(Ug,"")}function Es(e,t,n){if(t=nc(t),nc(e)!==t&&n)throw Error(E(425))}function oi(){}var aa=null,la=null;function ua(e,t){return e==="textarea"||e==="noscript"||typeof t.children=="string"||typeof t.children=="number"||typeof t.dangerouslySetInnerHTML=="object"&&t.dangerouslySetInnerHTML!==null&&t.dangerouslySetInnerHTML.__html!=null}var ca=typeof setTimeout=="function"?setTimeout:void 0,bg=typeof clearTimeout=="function"?clearTimeout:void 0,rc=typeof Promise=="function"?Promise:void 0,$g=typeof queueMicrotask=="function"?queueMicrotask:typeof rc<"u"?function(e){return rc.resolve(null).then(e).catch(Hg)}:ca;function Hg(e){setTimeout(function(){throw e})}function yo(e,t){var n=t,r=0;do{var s=n.nextSibling;if(e.removeChild(n),s&&s.nodeType===8)if(n=s.data,n==="/$"){if(r===0){e.removeChild(s),zr(t);return}r--}else n!=="$"&&n!=="$?"&&n!=="$!"||r++;n=s}while(n);zr(t)}function Rt(e){for(;e!=null;e=e.nextSibling){var t=e.nodeType;if(t===1||t===3)break;if(t===8){if(t=e.data,t==="$"||t==="$!"||t==="$?")break;if(t==="/$")return null}}return e}function sc(e){e=e.previousSibling;for(var t=0;e;){if(e.nodeType===8){var n=e.data;if(n==="$"||n==="$!"||n==="$?"){if(t===0)return e;t--}else n==="/$"&&t++}e=e.previousSibling}return null}var er=Math.random().toString(36).slice(2),nt="__reactFiber$"+er,Hr="__reactProps$"+er,mt="__reactContainer$"+er,fa="__reactEvents$"+er,Wg="__reactListeners$"+er,Kg="__reactHandles$"+er;function tn(e){var t=e[nt];if(t)return t;for(var n=e.parentNode;n;){if(t=n[mt]||n[nt]){if(n=t.alternate,t.child!==null||n!==null&&n.child!==null)for(e=sc(e);e!==null;){if(n=e[nt])return n;e=sc(e)}return t}e=n,n=e.parentNode}return null}function as(e){return e=e[nt]||e[mt],!e||e.tag!==5&&e.tag!==6&&e.tag!==13&&e.tag!==3?null:e}function Tn(e){if(e.tag===5||e.tag===6)return e.stateNode;throw Error(E(33))}function Vi(e){return e[Hr]||null}var da=[],Cn=-1;function $t(e){return{current:e}}function b(e){0>Cn||(e.current=da[Cn],da[Cn]=null,Cn--)}function B(e,t){Cn++,da[Cn]=e.current,e.current=t}var Ot={},ve=$t(Ot),Ee=$t(!1),cn=Ot;function $n(e,t){var n=e.type.contextTypes;if(!n)return Ot;var r=e.stateNode;if(r&&r.__reactInternalMemoizedUnmaskedChildContext===t)return r.__reactInternalMemoizedMaskedChildContext;var s={},i;for(i in n)s[i]=t[i];return r&&(e=e.stateNode,e.__reactInternalMemoizedUnmaskedChildContext=t,e.__reactInternalMemoizedMaskedChildContext=s),s}function je(e){return e=e.childContextTypes,e!=null}function ai(){b(Ee),b(ve)}function ic(e,t,n){if(ve.current!==Ot)throw Error(E(168));B(ve,t),B(Ee,n)}function uh(e,t,n){var r=e.stateNode;if(t=t.childContextTypes,typeof r.getChildContext!="function")return n;r=r.getChildContext();for(var s in r)if(!(s in t))throw Error(E(108,My(e)||"Unknown",s));return X({},n,r)}function li(e){return e=(e=e.stateNode)&&e.__reactInternalMemoizedMergedChildContext||Ot,cn=ve.current,B(ve,e),B(Ee,Ee.current),!0}function oc(e,t,n){var r=e.stateNode;if(!r)throw Error(E(169));n?(e=uh(e,t,cn),r.__reactInternalMemoizedMergedChildContext=e,b(Ee),b(ve),B(ve,e)):b(Ee),B(Ee,n)}var lt=null,_i=!1,go=!1;function ch(e){lt===null?lt=[e]:lt.push(e)}function Gg(e){_i=!0,ch(e)}function Ht(){if(!go&&lt!==null){go=!0;var e=0,t=z;try{var n=lt;for(z=1;e<n.length;e++){var r=n[e];do r=r(!0);while(r!==null)}lt=null,_i=!1}catch(s){throw lt!==null&&(lt=lt.slice(e+1)),_d(al,Ht),s}finally{z=t,go=!1}}return null}var En=[],jn=0,ui=null,ci=0,ze=[],Be=0,fn=null,ut=1,ct="";function Yt(e,t){En[jn++]=ci,En[jn++]=ui,ui=e,ci=t}function fh(e,t,n){ze[Be++]=ut,ze[Be++]=ct,ze[Be++]=fn,fn=e;var r=ut;e=ct;var s=32-qe(r)-1;r&=~(1<<s),n+=1;var i=32-qe(t)+s;if(30<i){var o=s-s%5;i=(r&(1<<o)-1).toString(32),r>>=o,s-=o,ut=1<<32-qe(t)+s|n<<s|r,ct=i+e}else ut=1<<i|n<<s|r,ct=e}function yl(e){e.return!==null&&(Yt(e,1),fh(e,1,0))}function gl(e){for(;e===ui;)ui=En[--jn],En[jn]=null,ci=En[--jn],En[jn]=null;for(;e===fn;)fn=ze[--Be],ze[Be]=null,ct=ze[--Be],ze[Be]=null,ut=ze[--Be],ze[Be]=null}var De=null,Me=null,W=!1,Ye=null;function dh(e,t){var n=Ue(5,null,null,0);n.elementType="DELETED",n.stateNode=t,n.return=e,t=e.deletions,t===null?(e.deletions=[n],e.flags|=16):t.push(n)}function ac(e,t){switch(e.tag){case 5:var n=e.type;return t=t.nodeType!==1||n.toLowerCase()!==t.nodeName.toLowerCase()?null:t,t!==null?(e.stateNode=t,De=e,Me=Rt(t.firstChild),!0):!1;case 6:return t=e.pendingProps===""||t.nodeType!==3?null:t,t!==null?(e.stateNode=t,De=e,Me=null,!0):!1;case 13:return t=t.nodeType!==8?null:t,t!==null?(n=fn!==null?{id:ut,overflow:ct}:null,e.memoizedState={dehydrated:t,treeContext:n,retryLane:1073741824},n=Ue(18,null,null,0),n.stateNode=t,n.return=e,e.child=n,De=e,Me=null,!0):!1;default:return!1}}function ha(e){return(e.mode&1)!==0&&(e.flags&128)===0}function pa(e){if(W){var t=Me;if(t){var n=t;if(!ac(e,t)){if(ha(e))throw Error(E(418));t=Rt(n.nextSibling);var r=De;t&&ac(e,t)?dh(r,n):(e.flags=e.flags&-4097|2,W=!1,De=e)}}else{if(ha(e))throw Error(E(418));e.flags=e.flags&-4097|2,W=!1,De=e}}}function lc(e){for(e=e.return;e!==null&&e.tag!==5&&e.tag!==3&&e.tag!==13;)e=e.return;De=e}function js(e){if(e!==De)return!1;if(!W)return lc(e),W=!0,!1;var t;if((t=e.tag!==3)&&!(t=e.tag!==5)&&(t=e.type,t=t!=="head"&&t!=="body"&&!ua(e.type,e.memoizedProps)),t&&(t=Me)){if(ha(e))throw hh(),Error(E(418));for(;t;)dh(e,t),t=Rt(t.nextSibling)}if(lc(e),e.tag===13){if(e=e.memoizedState,e=e!==null?e.dehydrated:null,!e)throw Error(E(317));e:{for(e=e.nextSibling,t=0;e;){if(e.nodeType===8){var n=e.data;if(n==="/$"){if(t===0){Me=Rt(e.nextSibling);break e}t--}else n!=="$"&&n!=="$!"&&n!=="$?"||t++}e=e.nextSibling}Me=null}}else Me=De?Rt(e.stateNode.nextSibling):null;return!0}function hh(){for(var e=Me;e;)e=Rt(e.nextSibling)}function Hn(){Me=De=null,W=!1}function vl(e){Ye===null?Ye=[e]:Ye.push(e)}var Qg=xt.ReactCurrentBatchConfig;function cr(e,t,n){if(e=n.ref,e!==null&&typeof e!="function"&&typeof e!="object"){if(n._owner){if(n=n._owner,n){if(n.tag!==1)throw Error(E(309));var r=n.stateNode}if(!r)throw Error(E(147,e));var s=r,i=""+e;return t!==null&&t.ref!==null&&typeof t.ref=="function"&&t.ref._stringRef===i?t.ref:(t=function(o){var a=s.refs;o===null?delete a[i]:a[i]=o},t._stringRef=i,t)}if(typeof e!="string")throw Error(E(284));if(!n._owner)throw Error(E(290,e))}return e}function Ns(e,t){throw e=Object.prototype.toString.call(t),Error(E(31,e==="[object Object]"?"object with keys {"+Object.keys(t).join(", ")+"}":e))}function uc(e){var t=e._init;return t(e._payload)}function ph(e){function t(m,p){if(e){var y=m.deletions;y===null?(m.deletions=[p],m.flags|=16):y.push(p)}}function n(m,p){if(!e)return null;for(;p!==null;)t(m,p),p=p.sibling;return null}function r(m,p){for(m=new Map;p!==null;)p.key!==null?m.set(p.key,p):m.set(p.index,p),p=p.sibling;return m}function s(m,p){return m=Ft(m,p),m.index=0,m.sibling=null,m}function i(m,p,y){return m.index=y,e?(y=m.alternate,y!==null?(y=y.index,y<p?(m.flags|=2,p):y):(m.flags|=2,p)):(m.flags|=1048576,p)}function o(m){return e&&m.alternate===null&&(m.flags|=2),m}function a(m,p,y,w){return p===null||p.tag!==6?(p=To(y,m.mode,w),p.return=m,p):(p=s(p,y),p.return=m,p)}function l(m,p,y,w){var S=y.type;return S===wn?c(m,p,y.props.children,w,y.key):p!==null&&(p.elementType===S||typeof S=="object"&&S!==null&&S.$$typeof===Pt&&uc(S)===p.type)?(w=s(p,y.props),w.ref=cr(m,p,y),w.return=m,w):(w=Qs(y.type,y.key,y.props,null,m.mode,w),w.ref=cr(m,p,y),w.return=m,w)}function u(m,p,y,w){return p===null||p.tag!==4||p.stateNode.containerInfo!==y.containerInfo||p.stateNode.implementation!==y.implementation?(p=Co(y,m.mode,w),p.return=m,p):(p=s(p,y.children||[]),p.return=m,p)}function c(m,p,y,w,S){return p===null||p.tag!==7?(p=an(y,m.mode,w,S),p.return=m,p):(p=s(p,y),p.return=m,p)}function d(m,p,y){if(typeof p=="string"&&p!==""||typeof p=="number")return p=To(""+p,m.mode,y),p.return=m,p;if(typeof p=="object"&&p!==null){switch(p.$$typeof){case gs:return y=Qs(p.type,p.key,p.props,null,m.mode,y),y.ref=cr(m,null,p),y.return=m,y;case xn:return p=Co(p,m.mode,y),p.return=m,p;case Pt:var w=p._init;return d(m,w(p._payload),y)}if(mr(p)||ir(p))return p=an(p,m.mode,y,null),p.return=m,p;Ns(m,p)}return null}function h(m,p,y,w){var S=p!==null?p.key:null;if(typeof y=="string"&&y!==""||typeof y=="number")return S!==null?null:a(m,p,""+y,w);if(typeof y=="object"&&y!==null){switch(y.$$typeof){case gs:return y.key===S?l(m,p,y,w):null;case xn:return y.key===S?u(m,p,y,w):null;case Pt:return S=y._init,h(m,p,S(y._payload),w)}if(mr(y)||ir(y))return S!==null?null:c(m,p,y,w,null);Ns(m,y)}return null}function g(m,p,y,w,S){if(typeof w=="string"&&w!==""||typeof w=="number")return m=m.get(y)||null,a(p,m,""+w,S);if(typeof w=="object"&&w!==null){switch(w.$$typeof){case gs:return m=m.get(w.key===null?y:w.key)||null,l(p,m,w,S);case xn:return m=m.get(w.key===null?y:w.key)||null,u(p,m,w,S);case Pt:var C=w._init;return g(m,p,y,C(w._payload),S)}if(mr(w)||ir(w))return m=m.get(y)||null,c(p,m,w,S,null);Ns(p,w)}return null}function v(m,p,y,w){for(var S=null,C=null,j=p,T=p=0,D=null;j!==null&&T<y.length;T++){j.index>T?(D=j,j=null):D=j.sibling;var A=h(m,j,y[T],w);if(A===null){j===null&&(j=D);break}e&&j&&A.alternate===null&&t(m,j),p=i(A,p,T),C===null?S=A:C.sibling=A,C=A,j=D}if(T===y.length)return n(m,j),W&&Yt(m,T),S;if(j===null){for(;T<y.length;T++)j=d(m,y[T],w),j!==null&&(p=i(j,p,T),C===null?S=j:C.sibling=j,C=j);return W&&Yt(m,T),S}for(j=r(m,j);T<y.length;T++)D=g(j,m,T,y[T],w),D!==null&&(e&&D.alternate!==null&&j.delete(D.key===null?T:D.key),p=i(D,p,T),C===null?S=D:C.sibling=D,C=D);return e&&j.forEach(function(O){return t(m,O)}),W&&Yt(m,T),S}function x(m,p,y,w){var S=ir(y);if(typeof S!="function")throw Error(E(150));if(y=S.call(y),y==null)throw Error(E(151));for(var C=S=null,j=p,T=p=0,D=null,A=y.next();j!==null&&!A.done;T++,A=y.next()){j.index>T?(D=j,j=null):D=j.sibling;var O=h(m,j,A.value,w);if(O===null){j===null&&(j=D);break}e&&j&&O.alternate===null&&t(m,j),p=i(O,p,T),C===null?S=O:C.sibling=O,C=O,j=D}if(A.done)return n(m,j),W&&Yt(m,T),S;if(j===null){for(;!A.done;T++,A=y.next())A=d(m,A.value,w),A!==null&&(p=i(A,p,T),C===null?S=A:C.sibling=A,C=A);return W&&Yt(m,T),S}for(j=r(m,j);!A.done;T++,A=y.next())A=g(j,m,T,A.value,w),A!==null&&(e&&A.alternate!==null&&j.delete(A.key===null?T:A.key),p=i(A,p,T),C===null?S=A:C.sibling=A,C=A);return e&&j.forEach(function(de){return t(m,de)}),W&&Yt(m,T),S}function P(m,p,y,w){if(typeof y=="object"&&y!==null&&y.type===wn&&y.key===null&&(y=y.props.children),typeof y=="object"&&y!==null){switch(y.$$typeof){case gs:e:{for(var S=y.key,C=p;C!==null;){if(C.key===S){if(S=y.type,S===wn){if(C.tag===7){n(m,C.sibling),p=s(C,y.props.children),p.return=m,m=p;break e}}else if(C.elementType===S||typeof S=="object"&&S!==null&&S.$$typeof===Pt&&uc(S)===C.type){n(m,C.sibling),p=s(C,y.props),p.ref=cr(m,C,y),p.return=m,m=p;break e}n(m,C);break}else t(m,C);C=C.sibling}y.type===wn?(p=an(y.props.children,m.mode,w,y.key),p.return=m,m=p):(w=Qs(y.type,y.key,y.props,null,m.mode,w),w.ref=cr(m,p,y),w.return=m,m=w)}return o(m);case xn:e:{for(C=y.key;p!==null;){if(p.key===C)if(p.tag===4&&p.stateNode.containerInfo===y.containerInfo&&p.stateNode.implementation===y.implementation){n(m,p.sibling),p=s(p,y.children||[]),p.return=m,m=p;break e}else{n(m,p);break}else t(m,p);p=p.sibling}p=Co(y,m.mode,w),p.return=m,m=p}return o(m);case Pt:return C=y._init,P(m,p,C(y._payload),w)}if(mr(y))return v(m,p,y,w);if(ir(y))return x(m,p,y,w);Ns(m,y)}return typeof y=="string"&&y!==""||typeof y=="number"?(y=""+y,p!==null&&p.tag===6?(n(m,p.sibling),p=s(p,y),p.return=m,m=p):(n(m,p),p=To(y,m.mode,w),p.return=m,m=p),o(m)):n(m,p)}return P}var Wn=ph(!0),mh=ph(!1),fi=$t(null),di=null,Nn=null,xl=null;function wl(){xl=Nn=di=null}function Sl(e){var t=fi.current;b(fi),e._currentValue=t}function ma(e,t,n){for(;e!==null;){var r=e.alternate;if((e.childLanes&t)!==t?(e.childLanes|=t,r!==null&&(r.childLanes|=t)):r!==null&&(r.childLanes&t)!==t&&(r.childLanes|=t),e===n)break;e=e.return}}function Bn(e,t){di=e,xl=Nn=null,e=e.dependencies,e!==null&&e.firstContext!==null&&(e.lanes&t&&(Ce=!0),e.firstContext=null)}function $e(e){var t=e._currentValue;if(xl!==e)if(e={context:e,memoizedValue:t,next:null},Nn===null){if(di===null)throw Error(E(308));Nn=e,di.dependencies={lanes:0,firstContext:e}}else Nn=Nn.next=e;return t}var nn=null;function kl(e){nn===null?nn=[e]:nn.push(e)}function yh(e,t,n,r){var s=t.interleaved;return s===null?(n.next=n,kl(t)):(n.next=s.next,s.next=n),t.interleaved=n,yt(e,r)}function yt(e,t){e.lanes|=t;var n=e.alternate;for(n!==null&&(n.lanes|=t),n=e,e=e.return;e!==null;)e.childLanes|=t,n=e.alternate,n!==null&&(n.childLanes|=t),n=e,e=e.return;return n.tag===3?n.stateNode:null}var Tt=!1;function Pl(e){e.updateQueue={baseState:e.memoizedState,firstBaseUpdate:null,lastBaseUpdate:null,shared:{pending:null,interleaved:null,lanes:0},effects:null}}function gh(e,t){e=e.updateQueue,t.updateQueue===e&&(t.updateQueue={baseState:e.baseState,firstBaseUpdate:e.firstBaseUpdate,lastBaseUpdate:e.lastBaseUpdate,shared:e.shared,effects:e.effects})}function ft(e,t){return{eventTime:e,lane:t,tag:0,payload:null,callback:null,next:null}}function Lt(e,t,n){var r=e.updateQueue;if(r===null)return null;if(r=r.shared,I&2){var s=r.pending;return s===null?t.next=t:(t.next=s.next,s.next=t),r.pending=t,yt(e,n)}return s=r.interleaved,s===null?(t.next=t,kl(r)):(t.next=s.next,s.next=t),r.interleaved=t,yt(e,n)}function bs(e,t,n){if(t=t.updateQueue,t!==null&&(t=t.shared,(n&4194240)!==0)){var r=t.lanes;r&=e.pendingLanes,n|=r,t.lanes=n,ll(e,n)}}function cc(e,t){var n=e.updateQueue,r=e.alternate;if(r!==null&&(r=r.updateQueue,n===r)){var s=null,i=null;if(n=n.firstBaseUpdate,n!==null){do{var o={eventTime:n.eventTime,lane:n.lane,tag:n.tag,payload:n.payload,callback:n.callback,next:null};i===null?s=i=o:i=i.next=o,n=n.next}while(n!==null);i===null?s=i=t:i=i.next=t}else s=i=t;n={baseState:r.baseState,firstBaseUpdate:s,lastBaseUpdate:i,shared:r.shared,effects:r.effects},e.updateQueue=n;return}e=n.lastBaseUpdate,e===null?n.firstBaseUpdate=t:e.next=t,n.lastBaseUpdate=t}function hi(e,t,n,r){var s=e.updateQueue;Tt=!1;var i=s.firstBaseUpdate,o=s.lastBaseUpdate,a=s.shared.pending;if(a!==null){s.shared.pending=null;var l=a,u=l.next;l.next=null,o===null?i=u:o.next=u,o=l;var c=e.alternate;c!==null&&(c=c.updateQueue,a=c.lastBaseUpdate,a!==o&&(a===null?c.firstBaseUpdate=u:a.next=u,c.lastBaseUpdate=l))}if(i!==null){var d=s.baseState;o=0,c=u=l=null,a=i;do{var h=a.lane,g=a.eventTime;if((r&h)===h){c!==null&&(c=c.next={eventTime:g,lane:0,tag:a.tag,payload:a.payload,callback:a.callback,next:null});e:{var v=e,x=a;switch(h=t,g=n,x.tag){case 1:if(v=x.payload,typeof v=="function"){d=v.call(g,d,h);break e}d=v;break e;case 3:v.flags=v.flags&-65537|128;case 0:if(v=x.payload,h=typeof v=="function"?v.call(g,d,h):v,h==null)break e;d=X({},d,h);break e;case 2:Tt=!0}}a.callback!==null&&a.lane!==0&&(e.flags|=64,h=s.effects,h===null?s.effects=[a]:h.push(a))}else g={eventTime:g,lane:h,tag:a.tag,payload:a.payload,callback:a.callback,next:null},c===null?(u=c=g,l=d):c=c.next=g,o|=h;if(a=a.next,a===null){if(a=s.shared.pending,a===null)break;h=a,a=h.next,h.next=null,s.lastBaseUpdate=h,s.shared.pending=null}}while(!0);if(c===null&&(l=d),s.baseState=l,s.firstBaseUpdate=u,s.lastBaseUpdate=c,t=s.shared.interleaved,t!==null){s=t;do o|=s.lane,s=s.next;while(s!==t)}else i===null&&(s.shared.lanes=0);hn|=o,e.lanes=o,e.memoizedState=d}}function fc(e,t,n){if(e=t.effects,t.effects=null,e!==null)for(t=0;t<e.length;t++){var r=e[t],s=r.callback;if(s!==null){if(r.callback=null,r=n,typeof s!="function")throw Error(E(191,s));s.call(r)}}}var ls={},st=$t(ls),Wr=$t(ls),Kr=$t(ls);function rn(e){if(e===ls)throw Error(E(174));return e}function Tl(e,t){switch(B(Kr,t),B(Wr,e),B(st,ls),e=t.nodeType,e){case 9:case 11:t=(t=t.documentElement)?t.namespaceURI:Xo(null,"");break;default:e=e===8?t.parentNode:t,t=e.namespaceURI||null,e=e.tagName,t=Xo(t,e)}b(st),B(st,t)}function Kn(){b(st),b(Wr),b(Kr)}function vh(e){rn(Kr.current);var t=rn(st.current),n=Xo(t,e.type);t!==n&&(B(Wr,e),B(st,n))}function Cl(e){Wr.current===e&&(b(st),b(Wr))}var K=$t(0);function pi(e){for(var t=e;t!==null;){if(t.tag===13){var n=t.memoizedState;if(n!==null&&(n=n.dehydrated,n===null||n.data==="$?"||n.data==="$!"))return t}else if(t.tag===19&&t.memoizedProps.revealOrder!==void 0){if(t.flags&128)return t}else if(t.child!==null){t.child.return=t,t=t.child;continue}if(t===e)break;for(;t.sibling===null;){if(t.return===null||t.return===e)return null;t=t.return}t.sibling.return=t.return,t=t.sibling}return null}var vo=[];function El(){for(var e=0;e<vo.length;e++)vo[e]._workInProgressVersionPrimary=null;vo.length=0}var $s=xt.ReactCurrentDispatcher,xo=xt.ReactCurrentBatchConfig,dn=0,Q=null,re=null,oe=null,mi=!1,Er=!1,Gr=0,Xg=0;function he(){throw Error(E(321))}function jl(e,t){if(t===null)return!1;for(var n=0;n<t.length&&n<e.length;n++)if(!Je(e[n],t[n]))return!1;return!0}function Nl(e,t,n,r,s,i){if(dn=i,Q=t,t.memoizedState=null,t.updateQueue=null,t.lanes=0,$s.current=e===null||e.memoizedState===null?Jg:e0,e=n(r,s),Er){i=0;do{if(Er=!1,Gr=0,25<=i)throw Error(E(301));i+=1,oe=re=null,t.updateQueue=null,$s.current=t0,e=n(r,s)}while(Er)}if($s.current=yi,t=re!==null&&re.next!==null,dn=0,oe=re=Q=null,mi=!1,t)throw Error(E(300));return e}function Al(){var e=Gr!==0;return Gr=0,e}function tt(){var e={memoizedState:null,baseState:null,baseQueue:null,queue:null,next:null};return oe===null?Q.memoizedState=oe=e:oe=oe.next=e,oe}function He(){if(re===null){var e=Q.alternate;e=e!==null?e.memoizedState:null}else e=re.next;var t=oe===null?Q.memoizedState:oe.next;if(t!==null)oe=t,re=e;else{if(e===null)throw Error(E(310));re=e,e={memoizedState:re.memoizedState,baseState:re.baseState,baseQueue:re.baseQueue,queue:re.queue,next:null},oe===null?Q.memoizedState=oe=e:oe=oe.next=e}return oe}function Qr(e,t){return typeof t=="function"?t(e):t}function wo(e){var t=He(),n=t.queue;if(n===null)throw Error(E(311));n.lastRenderedReducer=e;var r=re,s=r.baseQueue,i=n.pending;if(i!==null){if(s!==null){var o=s.next;s.next=i.next,i.next=o}r.baseQueue=s=i,n.pending=null}if(s!==null){i=s.next,r=r.baseState;var a=o=null,l=null,u=i;do{var c=u.lane;if((dn&c)===c)l!==null&&(l=l.next={lane:0,action:u.action,hasEagerState:u.hasEagerState,eagerState:u.eagerState,next:null}),r=u.hasEagerState?u.eagerState:e(r,u.action);else{var d={lane:c,action:u.action,hasEagerState:u.hasEagerState,eagerState:u.eagerState,next:null};l===null?(a=l=d,o=r):l=l.next=d,Q.lanes|=c,hn|=c}u=u.next}while(u!==null&&u!==i);l===null?o=r:l.next=a,Je(r,t.memoizedState)||(Ce=!0),t.memoizedState=r,t.baseState=o,t.baseQueue=l,n.lastRenderedState=r}if(e=n.interleaved,e!==null){s=e;do i=s.lane,Q.lanes|=i,hn|=i,s=s.next;while(s!==e)}else s===null&&(n.lanes=0);return[t.memoizedState,n.dispatch]}function So(e){var t=He(),n=t.queue;if(n===null)throw Error(E(311));n.lastRenderedReducer=e;var r=n.dispatch,s=n.pending,i=t.memoizedState;if(s!==null){n.pending=null;var o=s=s.next;do i=e(i,o.action),o=o.next;while(o!==s);Je(i,t.memoizedState)||(Ce=!0),t.memoizedState=i,t.baseQueue===null&&(t.baseState=i),n.lastRenderedState=i}return[i,r]}function xh(){}function wh(e,t){var n=Q,r=He(),s=t(),i=!Je(r.memoizedState,s);if(i&&(r.memoizedState=s,Ce=!0),r=r.queue,Ml(Ph.bind(null,n,r,e),[e]),r.getSnapshot!==t||i||oe!==null&&oe.memoizedState.tag&1){if(n.flags|=2048,Xr(9,kh.bind(null,n,r,s,t),void 0,null),ae===null)throw Error(E(349));dn&30||Sh(n,t,s)}return s}function Sh(e,t,n){e.flags|=16384,e={getSnapshot:t,value:n},t=Q.updateQueue,t===null?(t={lastEffect:null,stores:null},Q.updateQueue=t,t.stores=[e]):(n=t.stores,n===null?t.stores=[e]:n.push(e))}function kh(e,t,n,r){t.value=n,t.getSnapshot=r,Th(t)&&Ch(e)}function Ph(e,t,n){return n(function(){Th(t)&&Ch(e)})}function Th(e){var t=e.getSnapshot;e=e.value;try{var n=t();return!Je(e,n)}catch{return!0}}function Ch(e){var t=yt(e,1);t!==null&&Ze(t,e,1,-1)}function dc(e){var t=tt();return typeof e=="function"&&(e=e()),t.memoizedState=t.baseState=e,e={pending:null,interleaved:null,lanes:0,dispatch:null,lastRenderedReducer:Qr,lastRenderedState:e},t.queue=e,e=e.dispatch=Zg.bind(null,Q,e),[t.memoizedState,e]}function Xr(e,t,n,r){return e={tag:e,create:t,destroy:n,deps:r,next:null},t=Q.updateQueue,t===null?(t={lastEffect:null,stores:null},Q.updateQueue=t,t.lastEffect=e.next=e):(n=t.lastEffect,n===null?t.lastEffect=e.next=e:(r=n.next,n.next=e,e.next=r,t.lastEffect=e)),e}function Eh(){return He().memoizedState}function Hs(e,t,n,r){var s=tt();Q.flags|=e,s.memoizedState=Xr(1|t,n,void 0,r===void 0?null:r)}function Fi(e,t,n,r){var s=He();r=r===void 0?null:r;var i=void 0;if(re!==null){var o=re.memoizedState;if(i=o.destroy,r!==null&&jl(r,o.deps)){s.memoizedState=Xr(t,n,i,r);return}}Q.flags|=e,s.memoizedState=Xr(1|t,n,i,r)}function hc(e,t){return Hs(8390656,8,e,t)}function Ml(e,t){return Fi(2048,8,e,t)}function jh(e,t){return Fi(4,2,e,t)}function Nh(e,t){return Fi(4,4,e,t)}function Ah(e,t){if(typeof t=="function")return e=e(),t(e),function(){t(null)};if(t!=null)return e=e(),t.current=e,function(){t.current=null}}function Mh(e,t,n){return n=n!=null?n.concat([e]):null,Fi(4,4,Ah.bind(null,t,e),n)}function Dl(){}function Dh(e,t){var n=He();t=t===void 0?null:t;var r=n.memoizedState;return r!==null&&t!==null&&jl(t,r[1])?r[0]:(n.memoizedState=[e,t],e)}function Rh(e,t){var n=He();t=t===void 0?null:t;var r=n.memoizedState;return r!==null&&t!==null&&jl(t,r[1])?r[0]:(e=e(),n.memoizedState=[e,t],e)}function Lh(e,t,n){return dn&21?(Je(n,t)||(n=Od(),Q.lanes|=n,hn|=n,e.baseState=!0),t):(e.baseState&&(e.baseState=!1,Ce=!0),e.memoizedState=n)}function Yg(e,t){var n=z;z=n!==0&&4>n?n:4,e(!0);var r=xo.transition;xo.transition={};try{e(!1),t()}finally{z=n,xo.transition=r}}function Vh(){return He().memoizedState}function qg(e,t,n){var r=_t(e);if(n={lane:r,action:n,hasEagerState:!1,eagerState:null,next:null},_h(e))Fh(t,n);else if(n=yh(e,t,n,r),n!==null){var s=Se();Ze(n,e,r,s),Ih(n,t,r)}}function Zg(e,t,n){var r=_t(e),s={lane:r,action:n,hasEagerState:!1,eagerState:null,next:null};if(_h(e))Fh(t,s);else{var i=e.alternate;if(e.lanes===0&&(i===null||i.lanes===0)&&(i=t.lastRenderedReducer,i!==null))try{var o=t.lastRenderedState,a=i(o,n);if(s.hasEagerState=!0,s.eagerState=a,Je(a,o)){var l=t.interleaved;l===null?(s.next=s,kl(t)):(s.next=l.next,l.next=s),t.interleaved=s;return}}catch{}finally{}n=yh(e,t,s,r),n!==null&&(s=Se(),Ze(n,e,r,s),Ih(n,t,r))}}function _h(e){var t=e.alternate;return e===Q||t!==null&&t===Q}function Fh(e,t){Er=mi=!0;var n=e.pending;n===null?t.next=t:(t.next=n.next,n.next=t),e.pending=t}function Ih(e,t,n){if(n&4194240){var r=t.lanes;r&=e.pendingLanes,n|=r,t.lanes=n,ll(e,n)}}var yi={readContext:$e,useCallback:he,useContext:he,useEffect:he,useImperativeHandle:he,useInsertionEffect:he,useLayoutEffect:he,useMemo:he,useReducer:he,useRef:he,useState:he,useDebugValue:he,useDeferredValue:he,useTransition:he,useMutableSource:he,useSyncExternalStore:he,useId:he,unstable_isNewReconciler:!1},Jg={readContext:$e,useCallback:function(e,t){return tt().memoizedState=[e,t===void 0?null:t],e},useContext:$e,useEffect:hc,useImperativeHandle:function(e,t,n){return n=n!=null?n.concat([e]):null,Hs(4194308,4,Ah.bind(null,t,e),n)},useLayoutEffect:function(e,t){return Hs(4194308,4,e,t)},useInsertionEffect:function(e,t){return Hs(4,2,e,t)},useMemo:function(e,t){var n=tt();return t=t===void 0?null:t,e=e(),n.memoizedState=[e,t],e},useReducer:function(e,t,n){var r=tt();return t=n!==void 0?n(t):t,r.memoizedState=r.baseState=t,e={pending:null,interleaved:null,lanes:0,dispatch:null,lastRenderedReducer:e,lastRenderedState:t},r.queue=e,e=e.dispatch=qg.bind(null,Q,e),[r.memoizedState,e]},useRef:function(e){var t=tt();return e={current:e},t.memoizedState=e},useState:dc,useDebugValue:Dl,useDeferredValue:function(e){return tt().memoizedState=e},useTransition:function(){var e=dc(!1),t=e[0];return e=Yg.bind(null,e[1]),tt().memoizedState=e,[t,e]},useMutableSource:function(){},useSyncExternalStore:function(e,t,n){var r=Q,s=tt();if(W){if(n===void 0)throw Error(E(407));n=n()}else{if(n=t(),ae===null)throw Error(E(349));dn&30||Sh(r,t,n)}s.memoizedState=n;var i={value:n,getSnapshot:t};return s.queue=i,hc(Ph.bind(null,r,i,e),[e]),r.flags|=2048,Xr(9,kh.bind(null,r,i,n,t),void 0,null),n},useId:function(){var e=tt(),t=ae.identifierPrefix;if(W){var n=ct,r=ut;n=(r&~(1<<32-qe(r)-1)).toString(32)+n,t=":"+t+"R"+n,n=Gr++,0<n&&(t+="H"+n.toString(32)),t+=":"}else n=Xg++,t=":"+t+"r"+n.toString(32)+":";return e.memoizedState=t},unstable_isNewReconciler:!1},e0={readContext:$e,useCallback:Dh,useContext:$e,useEffect:Ml,useImperativeHandle:Mh,useInsertionEffect:jh,useLayoutEffect:Nh,useMemo:Rh,useReducer:wo,useRef:Eh,useState:function(){return wo(Qr)},useDebugValue:Dl,useDeferredValue:function(e){var t=He();return Lh(t,re.memoizedState,e)},useTransition:function(){var e=wo(Qr)[0],t=He().memoizedState;return[e,t]},useMutableSource:xh,useSyncExternalStore:wh,useId:Vh,unstable_isNewReconciler:!1},t0={readContext:$e,useCallback:Dh,useContext:$e,useEffect:Ml,useImperativeHandle:Mh,useInsertionEffect:jh,useLayoutEffect:Nh,useMemo:Rh,useReducer:So,useRef:Eh,useState:function(){return So(Qr)},useDebugValue:Dl,useDeferredValue:function(e){var t=He();return re===null?t.memoizedState=e:Lh(t,re.memoizedState,e)},useTransition:function(){var e=So(Qr)[0],t=He().memoizedState;return[e,t]},useMutableSource:xh,useSyncExternalStore:wh,useId:Vh,unstable_isNewReconciler:!1};function Qe(e,t){if(e&&e.defaultProps){t=X({},t),e=e.defaultProps;for(var n in e)t[n]===void 0&&(t[n]=e[n]);return t}return t}function ya(e,t,n,r){t=e.memoizedState,n=n(r,t),n=n==null?t:X({},t,n),e.memoizedState=n,e.lanes===0&&(e.updateQueue.baseState=n)}var Ii={isMounted:function(e){return(e=e._reactInternals)?yn(e)===e:!1},enqueueSetState:function(e,t,n){e=e._reactInternals;var r=Se(),s=_t(e),i=ft(r,s);i.payload=t,n!=null&&(i.callback=n),t=Lt(e,i,s),t!==null&&(Ze(t,e,s,r),bs(t,e,s))},enqueueReplaceState:function(e,t,n){e=e._reactInternals;var r=Se(),s=_t(e),i=ft(r,s);i.tag=1,i.payload=t,n!=null&&(i.callback=n),t=Lt(e,i,s),t!==null&&(Ze(t,e,s,r),bs(t,e,s))},enqueueForceUpdate:function(e,t){e=e._reactInternals;var n=Se(),r=_t(e),s=ft(n,r);s.tag=2,t!=null&&(s.callback=t),t=Lt(e,s,r),t!==null&&(Ze(t,e,r,n),bs(t,e,r))}};function pc(e,t,n,r,s,i,o){return e=e.stateNode,typeof e.shouldComponentUpdate=="function"?e.shouldComponentUpdate(r,i,o):t.prototype&&t.prototype.isPureReactComponent?!Ur(n,r)||!Ur(s,i):!0}function Oh(e,t,n){var r=!1,s=Ot,i=t.contextType;return typeof i=="object"&&i!==null?i=$e(i):(s=je(t)?cn:ve.current,r=t.contextTypes,i=(r=r!=null)?$n(e,s):Ot),t=new t(n,i),e.memoizedState=t.state!==null&&t.state!==void 0?t.state:null,t.updater=Ii,e.stateNode=t,t._reactInternals=e,r&&(e=e.stateNode,e.__reactInternalMemoizedUnmaskedChildContext=s,e.__reactInternalMemoizedMaskedChildContext=i),t}function mc(e,t,n,r){e=t.state,typeof t.componentWillReceiveProps=="function"&&t.componentWillReceiveProps(n,r),typeof t.UNSAFE_componentWillReceiveProps=="function"&&t.UNSAFE_componentWillReceiveProps(n,r),t.state!==e&&Ii.enqueueReplaceState(t,t.state,null)}function ga(e,t,n,r){var s=e.stateNode;s.props=n,s.state=e.memoizedState,s.refs={},Pl(e);var i=t.contextType;typeof i=="object"&&i!==null?s.context=$e(i):(i=je(t)?cn:ve.current,s.context=$n(e,i)),s.state=e.memoizedState,i=t.getDerivedStateFromProps,typeof i=="function"&&(ya(e,t,i,n),s.state=e.memoizedState),typeof t.getDerivedStateFromProps=="function"||typeof s.getSnapshotBeforeUpdate=="function"||typeof s.UNSAFE_componentWillMount!="function"&&typeof s.componentWillMount!="function"||(t=s.state,typeof s.componentWillMount=="function"&&s.componentWillMount(),typeof s.UNSAFE_componentWillMount=="function"&&s.UNSAFE_componentWillMount(),t!==s.state&&Ii.enqueueReplaceState(s,s.state,null),hi(e,n,s,r),s.state=e.memoizedState),typeof s.componentDidMount=="function"&&(e.flags|=4194308)}function Gn(e,t){try{var n="",r=t;do n+=Ay(r),r=r.return;while(r);var s=n}catch(i){s=`
Error generating stack: `+i.message+`
`+i.stack}return{value:e,source:t,stack:s,digest:null}}function ko(e,t,n){return{value:e,source:null,stack:n??null,digest:t??null}}function va(e,t){try{console.error(t.value)}catch(n){setTimeout(function(){throw n})}}var n0=typeof WeakMap=="function"?WeakMap:Map;function zh(e,t,n){n=ft(-1,n),n.tag=3,n.payload={element:null};var r=t.value;return n.callback=function(){vi||(vi=!0,Na=r),va(e,t)},n}function Bh(e,t,n){n=ft(-1,n),n.tag=3;var r=e.type.getDerivedStateFromError;if(typeof r=="function"){var s=t.value;n.payload=function(){return r(s)},n.callback=function(){va(e,t)}}var i=e.stateNode;return i!==null&&typeof i.componentDidCatch=="function"&&(n.callback=function(){va(e,t),typeof r!="function"&&(Vt===null?Vt=new Set([this]):Vt.add(this));var o=t.stack;this.componentDidCatch(t.value,{componentStack:o!==null?o:""})}),n}function yc(e,t,n){var r=e.pingCache;if(r===null){r=e.pingCache=new n0;var s=new Set;r.set(t,s)}else s=r.get(t),s===void 0&&(s=new Set,r.set(t,s));s.has(n)||(s.add(n),e=y0.bind(null,e,t,n),t.then(e,e))}function gc(e){do{var t;if((t=e.tag===13)&&(t=e.memoizedState,t=t!==null?t.dehydrated!==null:!0),t)return e;e=e.return}while(e!==null);return null}function vc(e,t,n,r,s){return e.mode&1?(e.flags|=65536,e.lanes=s,e):(e===t?e.flags|=65536:(e.flags|=128,n.flags|=131072,n.flags&=-52805,n.tag===1&&(n.alternate===null?n.tag=17:(t=ft(-1,1),t.tag=2,Lt(n,t,1))),n.lanes|=1),e)}var r0=xt.ReactCurrentOwner,Ce=!1;function we(e,t,n,r){t.child=e===null?mh(t,null,n,r):Wn(t,e.child,n,r)}function xc(e,t,n,r,s){n=n.render;var i=t.ref;return Bn(t,s),r=Nl(e,t,n,r,i,s),n=Al(),e!==null&&!Ce?(t.updateQueue=e.updateQueue,t.flags&=-2053,e.lanes&=~s,gt(e,t,s)):(W&&n&&yl(t),t.flags|=1,we(e,t,r,s),t.child)}function wc(e,t,n,r,s){if(e===null){var i=n.type;return typeof i=="function"&&!zl(i)&&i.defaultProps===void 0&&n.compare===null&&n.defaultProps===void 0?(t.tag=15,t.type=i,Uh(e,t,i,r,s)):(e=Qs(n.type,null,r,t,t.mode,s),e.ref=t.ref,e.return=t,t.child=e)}if(i=e.child,!(e.lanes&s)){var o=i.memoizedProps;if(n=n.compare,n=n!==null?n:Ur,n(o,r)&&e.ref===t.ref)return gt(e,t,s)}return t.flags|=1,e=Ft(i,r),e.ref=t.ref,e.return=t,t.child=e}function Uh(e,t,n,r,s){if(e!==null){var i=e.memoizedProps;if(Ur(i,r)&&e.ref===t.ref)if(Ce=!1,t.pendingProps=r=i,(e.lanes&s)!==0)e.flags&131072&&(Ce=!0);else return t.lanes=e.lanes,gt(e,t,s)}return xa(e,t,n,r,s)}function bh(e,t,n){var r=t.pendingProps,s=r.children,i=e!==null?e.memoizedState:null;if(r.mode==="hidden")if(!(t.mode&1))t.memoizedState={baseLanes:0,cachePool:null,transitions:null},B(Mn,Ae),Ae|=n;else{if(!(n&1073741824))return e=i!==null?i.baseLanes|n:n,t.lanes=t.childLanes=1073741824,t.memoizedState={baseLanes:e,cachePool:null,transitions:null},t.updateQueue=null,B(Mn,Ae),Ae|=e,null;t.memoizedState={baseLanes:0,cachePool:null,transitions:null},r=i!==null?i.baseLanes:n,B(Mn,Ae),Ae|=r}else i!==null?(r=i.baseLanes|n,t.memoizedState=null):r=n,B(Mn,Ae),Ae|=r;return we(e,t,s,n),t.child}function $h(e,t){var n=t.ref;(e===null&&n!==null||e!==null&&e.ref!==n)&&(t.flags|=512,t.flags|=2097152)}function xa(e,t,n,r,s){var i=je(n)?cn:ve.current;return i=$n(t,i),Bn(t,s),n=Nl(e,t,n,r,i,s),r=Al(),e!==null&&!Ce?(t.updateQueue=e.updateQueue,t.flags&=-2053,e.lanes&=~s,gt(e,t,s)):(W&&r&&yl(t),t.flags|=1,we(e,t,n,s),t.child)}function Sc(e,t,n,r,s){if(je(n)){var i=!0;li(t)}else i=!1;if(Bn(t,s),t.stateNode===null)Ws(e,t),Oh(t,n,r),ga(t,n,r,s),r=!0;else if(e===null){var o=t.stateNode,a=t.memoizedProps;o.props=a;var l=o.context,u=n.contextType;typeof u=="object"&&u!==null?u=$e(u):(u=je(n)?cn:ve.current,u=$n(t,u));var c=n.getDerivedStateFromProps,d=typeof c=="function"||typeof o.getSnapshotBeforeUpdate=="function";d||typeof o.UNSAFE_componentWillReceiveProps!="function"&&typeof o.componentWillReceiveProps!="function"||(a!==r||l!==u)&&mc(t,o,r,u),Tt=!1;var h=t.memoizedState;o.state=h,hi(t,r,o,s),l=t.memoizedState,a!==r||h!==l||Ee.current||Tt?(typeof c=="function"&&(ya(t,n,c,r),l=t.memoizedState),(a=Tt||pc(t,n,a,r,h,l,u))?(d||typeof o.UNSAFE_componentWillMount!="function"&&typeof o.componentWillMount!="function"||(typeof o.componentWillMount=="function"&&o.componentWillMount(),typeof o.UNSAFE_componentWillMount=="function"&&o.UNSAFE_componentWillMount()),typeof o.componentDidMount=="function"&&(t.flags|=4194308)):(typeof o.componentDidMount=="function"&&(t.flags|=4194308),t.memoizedProps=r,t.memoizedState=l),o.props=r,o.state=l,o.context=u,r=a):(typeof o.componentDidMount=="function"&&(t.flags|=4194308),r=!1)}else{o=t.stateNode,gh(e,t),a=t.memoizedProps,u=t.type===t.elementType?a:Qe(t.type,a),o.props=u,d=t.pendingProps,h=o.context,l=n.contextType,typeof l=="object"&&l!==null?l=$e(l):(l=je(n)?cn:ve.current,l=$n(t,l));var g=n.getDerivedStateFromProps;(c=typeof g=="function"||typeof o.getSnapshotBeforeUpdate=="function")||typeof o.UNSAFE_componentWillReceiveProps!="function"&&typeof o.componentWillReceiveProps!="function"||(a!==d||h!==l)&&mc(t,o,r,l),Tt=!1,h=t.memoizedState,o.state=h,hi(t,r,o,s);var v=t.memoizedState;a!==d||h!==v||Ee.current||Tt?(typeof g=="function"&&(ya(t,n,g,r),v=t.memoizedState),(u=Tt||pc(t,n,u,r,h,v,l)||!1)?(c||typeof o.UNSAFE_componentWillUpdate!="function"&&typeof o.componentWillUpdate!="function"||(typeof o.componentWillUpdate=="function"&&o.componentWillUpdate(r,v,l),typeof o.UNSAFE_componentWillUpdate=="function"&&o.UNSAFE_componentWillUpdate(r,v,l)),typeof o.componentDidUpdate=="function"&&(t.flags|=4),typeof o.getSnapshotBeforeUpdate=="function"&&(t.flags|=1024)):(typeof o.componentDidUpdate!="function"||a===e.memoizedProps&&h===e.memoizedState||(t.flags|=4),typeof o.getSnapshotBeforeUpdate!="function"||a===e.memoizedProps&&h===e.memoizedState||(t.flags|=1024),t.memoizedProps=r,t.memoizedState=v),o.props=r,o.state=v,o.context=l,r=u):(typeof o.componentDidUpdate!="function"||a===e.memoizedProps&&h===e.memoizedState||(t.flags|=4),typeof o.getSnapshotBeforeUpdate!="function"||a===e.memoizedProps&&h===e.memoizedState||(t.flags|=1024),r=!1)}return wa(e,t,n,r,i,s)}function wa(e,t,n,r,s,i){$h(e,t);var o=(t.flags&128)!==0;if(!r&&!o)return s&&oc(t,n,!1),gt(e,t,i);r=t.stateNode,r0.current=t;var a=o&&typeof n.getDerivedStateFromError!="function"?null:r.render();return t.flags|=1,e!==null&&o?(t.child=Wn(t,e.child,null,i),t.child=Wn(t,null,a,i)):we(e,t,a,i),t.memoizedState=r.state,s&&oc(t,n,!0),t.child}function Hh(e){var t=e.stateNode;t.pendingContext?ic(e,t.pendingContext,t.pendingContext!==t.context):t.context&&ic(e,t.context,!1),Tl(e,t.containerInfo)}function kc(e,t,n,r,s){return Hn(),vl(s),t.flags|=256,we(e,t,n,r),t.child}var Sa={dehydrated:null,treeContext:null,retryLane:0};function ka(e){return{baseLanes:e,cachePool:null,transitions:null}}function Wh(e,t,n){var r=t.pendingProps,s=K.current,i=!1,o=(t.flags&128)!==0,a;if((a=o)||(a=e!==null&&e.memoizedState===null?!1:(s&2)!==0),a?(i=!0,t.flags&=-129):(e===null||e.memoizedState!==null)&&(s|=1),B(K,s&1),e===null)return pa(t),e=t.memoizedState,e!==null&&(e=e.dehydrated,e!==null)?(t.mode&1?e.data==="$!"?t.lanes=8:t.lanes=1073741824:t.lanes=1,null):(o=r.children,e=r.fallback,i?(r=t.mode,i=t.child,o={mode:"hidden",children:o},!(r&1)&&i!==null?(i.childLanes=0,i.pendingProps=o):i=Bi(o,r,0,null),e=an(e,r,n,null),i.return=t,e.return=t,i.sibling=e,t.child=i,t.child.memoizedState=ka(n),t.memoizedState=Sa,e):Rl(t,o));if(s=e.memoizedState,s!==null&&(a=s.dehydrated,a!==null))return s0(e,t,o,r,a,s,n);if(i){i=r.fallback,o=t.mode,s=e.child,a=s.sibling;var l={mode:"hidden",children:r.children};return!(o&1)&&t.child!==s?(r=t.child,r.childLanes=0,r.pendingProps=l,t.deletions=null):(r=Ft(s,l),r.subtreeFlags=s.subtreeFlags&14680064),a!==null?i=Ft(a,i):(i=an(i,o,n,null),i.flags|=2),i.return=t,r.return=t,r.sibling=i,t.child=r,r=i,i=t.child,o=e.child.memoizedState,o=o===null?ka(n):{baseLanes:o.baseLanes|n,cachePool:null,transitions:o.transitions},i.memoizedState=o,i.childLanes=e.childLanes&~n,t.memoizedState=Sa,r}return i=e.child,e=i.sibling,r=Ft(i,{mode:"visible",children:r.children}),!(t.mode&1)&&(r.lanes=n),r.return=t,r.sibling=null,e!==null&&(n=t.deletions,n===null?(t.deletions=[e],t.flags|=16):n.push(e)),t.child=r,t.memoizedState=null,r}function Rl(e,t){return t=Bi({mode:"visible",children:t},e.mode,0,null),t.return=e,e.child=t}function As(e,t,n,r){return r!==null&&vl(r),Wn(t,e.child,null,n),e=Rl(t,t.pendingProps.children),e.flags|=2,t.memoizedState=null,e}function s0(e,t,n,r,s,i,o){if(n)return t.flags&256?(t.flags&=-257,r=ko(Error(E(422))),As(e,t,o,r)):t.memoizedState!==null?(t.child=e.child,t.flags|=128,null):(i=r.fallback,s=t.mode,r=Bi({mode:"visible",children:r.children},s,0,null),i=an(i,s,o,null),i.flags|=2,r.return=t,i.return=t,r.sibling=i,t.child=r,t.mode&1&&Wn(t,e.child,null,o),t.child.memoizedState=ka(o),t.memoizedState=Sa,i);if(!(t.mode&1))return As(e,t,o,null);if(s.data==="$!"){if(r=s.nextSibling&&s.nextSibling.dataset,r)var a=r.dgst;return r=a,i=Error(E(419)),r=ko(i,r,void 0),As(e,t,o,r)}if(a=(o&e.childLanes)!==0,Ce||a){if(r=ae,r!==null){switch(o&-o){case 4:s=2;break;case 16:s=8;break;case 64:case 128:case 256:case 512:case 1024:case 2048:case 4096:case 8192:case 16384:case 32768:case 65536:case 131072:case 262144:case 524288:case 1048576:case 2097152:case 4194304:case 8388608:case 16777216:case 33554432:case 67108864:s=32;break;case 536870912:s=268435456;break;default:s=0}s=s&(r.suspendedLanes|o)?0:s,s!==0&&s!==i.retryLane&&(i.retryLane=s,yt(e,s),Ze(r,e,s,-1))}return Ol(),r=ko(Error(E(421))),As(e,t,o,r)}return s.data==="$?"?(t.flags|=128,t.child=e.child,t=g0.bind(null,e),s._reactRetry=t,null):(e=i.treeContext,Me=Rt(s.nextSibling),De=t,W=!0,Ye=null,e!==null&&(ze[Be++]=ut,ze[Be++]=ct,ze[Be++]=fn,ut=e.id,ct=e.overflow,fn=t),t=Rl(t,r.children),t.flags|=4096,t)}function Pc(e,t,n){e.lanes|=t;var r=e.alternate;r!==null&&(r.lanes|=t),ma(e.return,t,n)}function Po(e,t,n,r,s){var i=e.memoizedState;i===null?e.memoizedState={isBackwards:t,rendering:null,renderingStartTime:0,last:r,tail:n,tailMode:s}:(i.isBackwards=t,i.rendering=null,i.renderingStartTime=0,i.last=r,i.tail=n,i.tailMode=s)}function Kh(e,t,n){var r=t.pendingProps,s=r.revealOrder,i=r.tail;if(we(e,t,r.children,n),r=K.current,r&2)r=r&1|2,t.flags|=128;else{if(e!==null&&e.flags&128)e:for(e=t.child;e!==null;){if(e.tag===13)e.memoizedState!==null&&Pc(e,n,t);else if(e.tag===19)Pc(e,n,t);else if(e.child!==null){e.child.return=e,e=e.child;continue}if(e===t)break e;for(;e.sibling===null;){if(e.return===null||e.return===t)break e;e=e.return}e.sibling.return=e.return,e=e.sibling}r&=1}if(B(K,r),!(t.mode&1))t.memoizedState=null;else switch(s){case"forwards":for(n=t.child,s=null;n!==null;)e=n.alternate,e!==null&&pi(e)===null&&(s=n),n=n.sibling;n=s,n===null?(s=t.child,t.child=null):(s=n.sibling,n.sibling=null),Po(t,!1,s,n,i);break;case"backwards":for(n=null,s=t.child,t.child=null;s!==null;){if(e=s.alternate,e!==null&&pi(e)===null){t.child=s;break}e=s.sibling,s.sibling=n,n=s,s=e}Po(t,!0,n,null,i);break;case"together":Po(t,!1,null,null,void 0);break;default:t.memoizedState=null}return t.child}function Ws(e,t){!(t.mode&1)&&e!==null&&(e.alternate=null,t.alternate=null,t.flags|=2)}function gt(e,t,n){if(e!==null&&(t.dependencies=e.dependencies),hn|=t.lanes,!(n&t.childLanes))return null;if(e!==null&&t.child!==e.child)throw Error(E(153));if(t.child!==null){for(e=t.child,n=Ft(e,e.pendingProps),t.child=n,n.return=t;e.sibling!==null;)e=e.sibling,n=n.sibling=Ft(e,e.pendingProps),n.return=t;n.sibling=null}return t.child}function i0(e,t,n){switch(t.tag){case 3:Hh(t),Hn();break;case 5:vh(t);break;case 1:je(t.type)&&li(t);break;case 4:Tl(t,t.stateNode.containerInfo);break;case 10:var r=t.type._context,s=t.memoizedProps.value;B(fi,r._currentValue),r._currentValue=s;break;case 13:if(r=t.memoizedState,r!==null)return r.dehydrated!==null?(B(K,K.current&1),t.flags|=128,null):n&t.child.childLanes?Wh(e,t,n):(B(K,K.current&1),e=gt(e,t,n),e!==null?e.sibling:null);B(K,K.current&1);break;case 19:if(r=(n&t.childLanes)!==0,e.flags&128){if(r)return Kh(e,t,n);t.flags|=128}if(s=t.memoizedState,s!==null&&(s.rendering=null,s.tail=null,s.lastEffect=null),B(K,K.current),r)break;return null;case 22:case 23:return t.lanes=0,bh(e,t,n)}return gt(e,t,n)}var Gh,Pa,Qh,Xh;Gh=function(e,t){for(var n=t.child;n!==null;){if(n.tag===5||n.tag===6)e.appendChild(n.stateNode);else if(n.tag!==4&&n.child!==null){n.child.return=n,n=n.child;continue}if(n===t)break;for(;n.sibling===null;){if(n.return===null||n.return===t)return;n=n.return}n.sibling.return=n.return,n=n.sibling}};Pa=function(){};Qh=function(e,t,n,r){var s=e.memoizedProps;if(s!==r){e=t.stateNode,rn(st.current);var i=null;switch(n){case"input":s=Wo(e,s),r=Wo(e,r),i=[];break;case"select":s=X({},s,{value:void 0}),r=X({},r,{value:void 0}),i=[];break;case"textarea":s=Qo(e,s),r=Qo(e,r),i=[];break;default:typeof s.onClick!="function"&&typeof r.onClick=="function"&&(e.onclick=oi)}Yo(n,r);var o;n=null;for(u in s)if(!r.hasOwnProperty(u)&&s.hasOwnProperty(u)&&s[u]!=null)if(u==="style"){var a=s[u];for(o in a)a.hasOwnProperty(o)&&(n||(n={}),n[o]="")}else u!=="dangerouslySetInnerHTML"&&u!=="children"&&u!=="suppressContentEditableWarning"&&u!=="suppressHydrationWarning"&&u!=="autoFocus"&&(Vr.hasOwnProperty(u)?i||(i=[]):(i=i||[]).push(u,null));for(u in r){var l=r[u];if(a=s!=null?s[u]:void 0,r.hasOwnProperty(u)&&l!==a&&(l!=null||a!=null))if(u==="style")if(a){for(o in a)!a.hasOwnProperty(o)||l&&l.hasOwnProperty(o)||(n||(n={}),n[o]="");for(o in l)l.hasOwnProperty(o)&&a[o]!==l[o]&&(n||(n={}),n[o]=l[o])}else n||(i||(i=[]),i.push(u,n)),n=l;else u==="dangerouslySetInnerHTML"?(l=l?l.__html:void 0,a=a?a.__html:void 0,l!=null&&a!==l&&(i=i||[]).push(u,l)):u==="children"?typeof l!="string"&&typeof l!="number"||(i=i||[]).push(u,""+l):u!=="suppressContentEditableWarning"&&u!=="suppressHydrationWarning"&&(Vr.hasOwnProperty(u)?(l!=null&&u==="onScroll"&&U("scroll",e),i||a===l||(i=[])):(i=i||[]).push(u,l))}n&&(i=i||[]).push("style",n);var u=i;(t.updateQueue=u)&&(t.flags|=4)}};Xh=function(e,t,n,r){n!==r&&(t.flags|=4)};function fr(e,t){if(!W)switch(e.tailMode){case"hidden":t=e.tail;for(var n=null;t!==null;)t.alternate!==null&&(n=t),t=t.sibling;n===null?e.tail=null:n.sibling=null;break;case"collapsed":n=e.tail;for(var r=null;n!==null;)n.alternate!==null&&(r=n),n=n.sibling;r===null?t||e.tail===null?e.tail=null:e.tail.sibling=null:r.sibling=null}}function pe(e){var t=e.alternate!==null&&e.alternate.child===e.child,n=0,r=0;if(t)for(var s=e.child;s!==null;)n|=s.lanes|s.childLanes,r|=s.subtreeFlags&14680064,r|=s.flags&14680064,s.return=e,s=s.sibling;else for(s=e.child;s!==null;)n|=s.lanes|s.childLanes,r|=s.subtreeFlags,r|=s.flags,s.return=e,s=s.sibling;return e.subtreeFlags|=r,e.childLanes=n,t}function o0(e,t,n){var r=t.pendingProps;switch(gl(t),t.tag){case 2:case 16:case 15:case 0:case 11:case 7:case 8:case 12:case 9:case 14:return pe(t),null;case 1:return je(t.type)&&ai(),pe(t),null;case 3:return r=t.stateNode,Kn(),b(Ee),b(ve),El(),r.pendingContext&&(r.context=r.pendingContext,r.pendingContext=null),(e===null||e.child===null)&&(js(t)?t.flags|=4:e===null||e.memoizedState.isDehydrated&&!(t.flags&256)||(t.flags|=1024,Ye!==null&&(Da(Ye),Ye=null))),Pa(e,t),pe(t),null;case 5:Cl(t);var s=rn(Kr.current);if(n=t.type,e!==null&&t.stateNode!=null)Qh(e,t,n,r,s),e.ref!==t.ref&&(t.flags|=512,t.flags|=2097152);else{if(!r){if(t.stateNode===null)throw Error(E(166));return pe(t),null}if(e=rn(st.current),js(t)){r=t.stateNode,n=t.type;var i=t.memoizedProps;switch(r[nt]=t,r[Hr]=i,e=(t.mode&1)!==0,n){case"dialog":U("cancel",r),U("close",r);break;case"iframe":case"object":case"embed":U("load",r);break;case"video":case"audio":for(s=0;s<gr.length;s++)U(gr[s],r);break;case"source":U("error",r);break;case"img":case"image":case"link":U("error",r),U("load",r);break;case"details":U("toggle",r);break;case"input":Ru(r,i),U("invalid",r);break;case"select":r._wrapperState={wasMultiple:!!i.multiple},U("invalid",r);break;case"textarea":Vu(r,i),U("invalid",r)}Yo(n,i),s=null;for(var o in i)if(i.hasOwnProperty(o)){var a=i[o];o==="children"?typeof a=="string"?r.textContent!==a&&(i.suppressHydrationWarning!==!0&&Es(r.textContent,a,e),s=["children",a]):typeof a=="number"&&r.textContent!==""+a&&(i.suppressHydrationWarning!==!0&&Es(r.textContent,a,e),s=["children",""+a]):Vr.hasOwnProperty(o)&&a!=null&&o==="onScroll"&&U("scroll",r)}switch(n){case"input":vs(r),Lu(r,i,!0);break;case"textarea":vs(r),_u(r);break;case"select":case"option":break;default:typeof i.onClick=="function"&&(r.onclick=oi)}r=s,t.updateQueue=r,r!==null&&(t.flags|=4)}else{o=s.nodeType===9?s:s.ownerDocument,e==="http://www.w3.org/1999/xhtml"&&(e=Pd(n)),e==="http://www.w3.org/1999/xhtml"?n==="script"?(e=o.createElement("div"),e.innerHTML="<script><\/script>",e=e.removeChild(e.firstChild)):typeof r.is=="string"?e=o.createElement(n,{is:r.is}):(e=o.createElement(n),n==="select"&&(o=e,r.multiple?o.multiple=!0:r.size&&(o.size=r.size))):e=o.createElementNS(e,n),e[nt]=t,e[Hr]=r,Gh(e,t,!1,!1),t.stateNode=e;e:{switch(o=qo(n,r),n){case"dialog":U("cancel",e),U("close",e),s=r;break;case"iframe":case"object":case"embed":U("load",e),s=r;break;case"video":case"audio":for(s=0;s<gr.length;s++)U(gr[s],e);s=r;break;case"source":U("error",e),s=r;break;case"img":case"image":case"link":U("error",e),U("load",e),s=r;break;case"details":U("toggle",e),s=r;break;case"input":Ru(e,r),s=Wo(e,r),U("invalid",e);break;case"option":s=r;break;case"select":e._wrapperState={wasMultiple:!!r.multiple},s=X({},r,{value:void 0}),U("invalid",e);break;case"textarea":Vu(e,r),s=Qo(e,r),U("invalid",e);break;default:s=r}Yo(n,s),a=s;for(i in a)if(a.hasOwnProperty(i)){var l=a[i];i==="style"?Ed(e,l):i==="dangerouslySetInnerHTML"?(l=l?l.__html:void 0,l!=null&&Td(e,l)):i==="children"?typeof l=="string"?(n!=="textarea"||l!=="")&&_r(e,l):typeof l=="number"&&_r(e,""+l):i!=="suppressContentEditableWarning"&&i!=="suppressHydrationWarning"&&i!=="autoFocus"&&(Vr.hasOwnProperty(i)?l!=null&&i==="onScroll"&&U("scroll",e):l!=null&&nl(e,i,l,o))}switch(n){case"input":vs(e),Lu(e,r,!1);break;case"textarea":vs(e),_u(e);break;case"option":r.value!=null&&e.setAttribute("value",""+It(r.value));break;case"select":e.multiple=!!r.multiple,i=r.value,i!=null?Fn(e,!!r.multiple,i,!1):r.defaultValue!=null&&Fn(e,!!r.multiple,r.defaultValue,!0);break;default:typeof s.onClick=="function"&&(e.onclick=oi)}switch(n){case"button":case"input":case"select":case"textarea":r=!!r.autoFocus;break e;case"img":r=!0;break e;default:r=!1}}r&&(t.flags|=4)}t.ref!==null&&(t.flags|=512,t.flags|=2097152)}return pe(t),null;case 6:if(e&&t.stateNode!=null)Xh(e,t,e.memoizedProps,r);else{if(typeof r!="string"&&t.stateNode===null)throw Error(E(166));if(n=rn(Kr.current),rn(st.current),js(t)){if(r=t.stateNode,n=t.memoizedProps,r[nt]=t,(i=r.nodeValue!==n)&&(e=De,e!==null))switch(e.tag){case 3:Es(r.nodeValue,n,(e.mode&1)!==0);break;case 5:e.memoizedProps.suppressHydrationWarning!==!0&&Es(r.nodeValue,n,(e.mode&1)!==0)}i&&(t.flags|=4)}else r=(n.nodeType===9?n:n.ownerDocument).createTextNode(r),r[nt]=t,t.stateNode=r}return pe(t),null;case 13:if(b(K),r=t.memoizedState,e===null||e.memoizedState!==null&&e.memoizedState.dehydrated!==null){if(W&&Me!==null&&t.mode&1&&!(t.flags&128))hh(),Hn(),t.flags|=98560,i=!1;else if(i=js(t),r!==null&&r.dehydrated!==null){if(e===null){if(!i)throw Error(E(318));if(i=t.memoizedState,i=i!==null?i.dehydrated:null,!i)throw Error(E(317));i[nt]=t}else Hn(),!(t.flags&128)&&(t.memoizedState=null),t.flags|=4;pe(t),i=!1}else Ye!==null&&(Da(Ye),Ye=null),i=!0;if(!i)return t.flags&65536?t:null}return t.flags&128?(t.lanes=n,t):(r=r!==null,r!==(e!==null&&e.memoizedState!==null)&&r&&(t.child.flags|=8192,t.mode&1&&(e===null||K.current&1?se===0&&(se=3):Ol())),t.updateQueue!==null&&(t.flags|=4),pe(t),null);case 4:return Kn(),Pa(e,t),e===null&&br(t.stateNode.containerInfo),pe(t),null;case 10:return Sl(t.type._context),pe(t),null;case 17:return je(t.type)&&ai(),pe(t),null;case 19:if(b(K),i=t.memoizedState,i===null)return pe(t),null;if(r=(t.flags&128)!==0,o=i.rendering,o===null)if(r)fr(i,!1);else{if(se!==0||e!==null&&e.flags&128)for(e=t.child;e!==null;){if(o=pi(e),o!==null){for(t.flags|=128,fr(i,!1),r=o.updateQueue,r!==null&&(t.updateQueue=r,t.flags|=4),t.subtreeFlags=0,r=n,n=t.child;n!==null;)i=n,e=r,i.flags&=14680066,o=i.alternate,o===null?(i.childLanes=0,i.lanes=e,i.child=null,i.subtreeFlags=0,i.memoizedProps=null,i.memoizedState=null,i.updateQueue=null,i.dependencies=null,i.stateNode=null):(i.childLanes=o.childLanes,i.lanes=o.lanes,i.child=o.child,i.subtreeFlags=0,i.deletions=null,i.memoizedProps=o.memoizedProps,i.memoizedState=o.memoizedState,i.updateQueue=o.updateQueue,i.type=o.type,e=o.dependencies,i.dependencies=e===null?null:{lanes:e.lanes,firstContext:e.firstContext}),n=n.sibling;return B(K,K.current&1|2),t.child}e=e.sibling}i.tail!==null&&ee()>Qn&&(t.flags|=128,r=!0,fr(i,!1),t.lanes=4194304)}else{if(!r)if(e=pi(o),e!==null){if(t.flags|=128,r=!0,n=e.updateQueue,n!==null&&(t.updateQueue=n,t.flags|=4),fr(i,!0),i.tail===null&&i.tailMode==="hidden"&&!o.alternate&&!W)return pe(t),null}else 2*ee()-i.renderingStartTime>Qn&&n!==1073741824&&(t.flags|=128,r=!0,fr(i,!1),t.lanes=4194304);i.isBackwards?(o.sibling=t.child,t.child=o):(n=i.last,n!==null?n.sibling=o:t.child=o,i.last=o)}return i.tail!==null?(t=i.tail,i.rendering=t,i.tail=t.sibling,i.renderingStartTime=ee(),t.sibling=null,n=K.current,B(K,r?n&1|2:n&1),t):(pe(t),null);case 22:case 23:return Il(),r=t.memoizedState!==null,e!==null&&e.memoizedState!==null!==r&&(t.flags|=8192),r&&t.mode&1?Ae&1073741824&&(pe(t),t.subtreeFlags&6&&(t.flags|=8192)):pe(t),null;case 24:return null;case 25:return null}throw Error(E(156,t.tag))}function a0(e,t){switch(gl(t),t.tag){case 1:return je(t.type)&&ai(),e=t.flags,e&65536?(t.flags=e&-65537|128,t):null;case 3:return Kn(),b(Ee),b(ve),El(),e=t.flags,e&65536&&!(e&128)?(t.flags=e&-65537|128,t):null;case 5:return Cl(t),null;case 13:if(b(K),e=t.memoizedState,e!==null&&e.dehydrated!==null){if(t.alternate===null)throw Error(E(340));Hn()}return e=t.flags,e&65536?(t.flags=e&-65537|128,t):null;case 19:return b(K),null;case 4:return Kn(),null;case 10:return Sl(t.type._context),null;case 22:case 23:return Il(),null;case 24:return null;default:return null}}var Ms=!1,ye=!1,l0=typeof WeakSet=="function"?WeakSet:Set,M=null;function An(e,t){var n=e.ref;if(n!==null)if(typeof n=="function")try{n(null)}catch(r){q(e,t,r)}else n.current=null}function Ta(e,t,n){try{n()}catch(r){q(e,t,r)}}var Tc=!1;function u0(e,t){if(aa=ri,e=eh(),ml(e)){if("selectionStart"in e)var n={start:e.selectionStart,end:e.selectionEnd};else e:{n=(n=e.ownerDocument)&&n.defaultView||window;var r=n.getSelection&&n.getSelection();if(r&&r.rangeCount!==0){n=r.anchorNode;var s=r.anchorOffset,i=r.focusNode;r=r.focusOffset;try{n.nodeType,i.nodeType}catch{n=null;break e}var o=0,a=-1,l=-1,u=0,c=0,d=e,h=null;t:for(;;){for(var g;d!==n||s!==0&&d.nodeType!==3||(a=o+s),d!==i||r!==0&&d.nodeType!==3||(l=o+r),d.nodeType===3&&(o+=d.nodeValue.length),(g=d.firstChild)!==null;)h=d,d=g;for(;;){if(d===e)break t;if(h===n&&++u===s&&(a=o),h===i&&++c===r&&(l=o),(g=d.nextSibling)!==null)break;d=h,h=d.parentNode}d=g}n=a===-1||l===-1?null:{start:a,end:l}}else n=null}n=n||{start:0,end:0}}else n=null;for(la={focusedElem:e,selectionRange:n},ri=!1,M=t;M!==null;)if(t=M,e=t.child,(t.subtreeFlags&1028)!==0&&e!==null)e.return=t,M=e;else for(;M!==null;){t=M;try{var v=t.alternate;if(t.flags&1024)switch(t.tag){case 0:case 11:case 15:break;case 1:if(v!==null){var x=v.memoizedProps,P=v.memoizedState,m=t.stateNode,p=m.getSnapshotBeforeUpdate(t.elementType===t.type?x:Qe(t.type,x),P);m.__reactInternalSnapshotBeforeUpdate=p}break;case 3:var y=t.stateNode.containerInfo;y.nodeType===1?y.textContent="":y.nodeType===9&&y.documentElement&&y.removeChild(y.documentElement);break;case 5:case 6:case 4:case 17:break;default:throw Error(E(163))}}catch(w){q(t,t.return,w)}if(e=t.sibling,e!==null){e.return=t.return,M=e;break}M=t.return}return v=Tc,Tc=!1,v}function jr(e,t,n){var r=t.updateQueue;if(r=r!==null?r.lastEffect:null,r!==null){var s=r=r.next;do{if((s.tag&e)===e){var i=s.destroy;s.destroy=void 0,i!==void 0&&Ta(t,n,i)}s=s.next}while(s!==r)}}function Oi(e,t){if(t=t.updateQueue,t=t!==null?t.lastEffect:null,t!==null){var n=t=t.next;do{if((n.tag&e)===e){var r=n.create;n.destroy=r()}n=n.next}while(n!==t)}}function Ca(e){var t=e.ref;if(t!==null){var n=e.stateNode;switch(e.tag){case 5:e=n;break;default:e=n}typeof t=="function"?t(e):t.current=e}}function Yh(e){var t=e.alternate;t!==null&&(e.alternate=null,Yh(t)),e.child=null,e.deletions=null,e.sibling=null,e.tag===5&&(t=e.stateNode,t!==null&&(delete t[nt],delete t[Hr],delete t[fa],delete t[Wg],delete t[Kg])),e.stateNode=null,e.return=null,e.dependencies=null,e.memoizedProps=null,e.memoizedState=null,e.pendingProps=null,e.stateNode=null,e.updateQueue=null}function qh(e){return e.tag===5||e.tag===3||e.tag===4}function Cc(e){e:for(;;){for(;e.sibling===null;){if(e.return===null||qh(e.return))return null;e=e.return}for(e.sibling.return=e.return,e=e.sibling;e.tag!==5&&e.tag!==6&&e.tag!==18;){if(e.flags&2||e.child===null||e.tag===4)continue e;e.child.return=e,e=e.child}if(!(e.flags&2))return e.stateNode}}function Ea(e,t,n){var r=e.tag;if(r===5||r===6)e=e.stateNode,t?n.nodeType===8?n.parentNode.insertBefore(e,t):n.insertBefore(e,t):(n.nodeType===8?(t=n.parentNode,t.insertBefore(e,n)):(t=n,t.appendChild(e)),n=n._reactRootContainer,n!=null||t.onclick!==null||(t.onclick=oi));else if(r!==4&&(e=e.child,e!==null))for(Ea(e,t,n),e=e.sibling;e!==null;)Ea(e,t,n),e=e.sibling}function ja(e,t,n){var r=e.tag;if(r===5||r===6)e=e.stateNode,t?n.insertBefore(e,t):n.appendChild(e);else if(r!==4&&(e=e.child,e!==null))for(ja(e,t,n),e=e.sibling;e!==null;)ja(e,t,n),e=e.sibling}var le=null,Xe=!1;function wt(e,t,n){for(n=n.child;n!==null;)Zh(e,t,n),n=n.sibling}function Zh(e,t,n){if(rt&&typeof rt.onCommitFiberUnmount=="function")try{rt.onCommitFiberUnmount(Mi,n)}catch{}switch(n.tag){case 5:ye||An(n,t);case 6:var r=le,s=Xe;le=null,wt(e,t,n),le=r,Xe=s,le!==null&&(Xe?(e=le,n=n.stateNode,e.nodeType===8?e.parentNode.removeChild(n):e.removeChild(n)):le.removeChild(n.stateNode));break;case 18:le!==null&&(Xe?(e=le,n=n.stateNode,e.nodeType===8?yo(e.parentNode,n):e.nodeType===1&&yo(e,n),zr(e)):yo(le,n.stateNode));break;case 4:r=le,s=Xe,le=n.stateNode.containerInfo,Xe=!0,wt(e,t,n),le=r,Xe=s;break;case 0:case 11:case 14:case 15:if(!ye&&(r=n.updateQueue,r!==null&&(r=r.lastEffect,r!==null))){s=r=r.next;do{var i=s,o=i.destroy;i=i.tag,o!==void 0&&(i&2||i&4)&&Ta(n,t,o),s=s.next}while(s!==r)}wt(e,t,n);break;case 1:if(!ye&&(An(n,t),r=n.stateNode,typeof r.componentWillUnmount=="function"))try{r.props=n.memoizedProps,r.state=n.memoizedState,r.componentWillUnmount()}catch(a){q(n,t,a)}wt(e,t,n);break;case 21:wt(e,t,n);break;case 22:n.mode&1?(ye=(r=ye)||n.memoizedState!==null,wt(e,t,n),ye=r):wt(e,t,n);break;default:wt(e,t,n)}}function Ec(e){var t=e.updateQueue;if(t!==null){e.updateQueue=null;var n=e.stateNode;n===null&&(n=e.stateNode=new l0),t.forEach(function(r){var s=v0.bind(null,e,r);n.has(r)||(n.add(r),r.then(s,s))})}}function Ke(e,t){var n=t.deletions;if(n!==null)for(var r=0;r<n.length;r++){var s=n[r];try{var i=e,o=t,a=o;e:for(;a!==null;){switch(a.tag){case 5:le=a.stateNode,Xe=!1;break e;case 3:le=a.stateNode.containerInfo,Xe=!0;break e;case 4:le=a.stateNode.containerInfo,Xe=!0;break e}a=a.return}if(le===null)throw Error(E(160));Zh(i,o,s),le=null,Xe=!1;var l=s.alternate;l!==null&&(l.return=null),s.return=null}catch(u){q(s,t,u)}}if(t.subtreeFlags&12854)for(t=t.child;t!==null;)Jh(t,e),t=t.sibling}function Jh(e,t){var n=e.alternate,r=e.flags;switch(e.tag){case 0:case 11:case 14:case 15:if(Ke(t,e),et(e),r&4){try{jr(3,e,e.return),Oi(3,e)}catch(x){q(e,e.return,x)}try{jr(5,e,e.return)}catch(x){q(e,e.return,x)}}break;case 1:Ke(t,e),et(e),r&512&&n!==null&&An(n,n.return);break;case 5:if(Ke(t,e),et(e),r&512&&n!==null&&An(n,n.return),e.flags&32){var s=e.stateNode;try{_r(s,"")}catch(x){q(e,e.return,x)}}if(r&4&&(s=e.stateNode,s!=null)){var i=e.memoizedProps,o=n!==null?n.memoizedProps:i,a=e.type,l=e.updateQueue;if(e.updateQueue=null,l!==null)try{a==="input"&&i.type==="radio"&&i.name!=null&&Sd(s,i),qo(a,o);var u=qo(a,i);for(o=0;o<l.length;o+=2){var c=l[o],d=l[o+1];c==="style"?Ed(s,d):c==="dangerouslySetInnerHTML"?Td(s,d):c==="children"?_r(s,d):nl(s,c,d,u)}switch(a){case"input":Ko(s,i);break;case"textarea":kd(s,i);break;case"select":var h=s._wrapperState.wasMultiple;s._wrapperState.wasMultiple=!!i.multiple;var g=i.value;g!=null?Fn(s,!!i.multiple,g,!1):h!==!!i.multiple&&(i.defaultValue!=null?Fn(s,!!i.multiple,i.defaultValue,!0):Fn(s,!!i.multiple,i.multiple?[]:"",!1))}s[Hr]=i}catch(x){q(e,e.return,x)}}break;case 6:if(Ke(t,e),et(e),r&4){if(e.stateNode===null)throw Error(E(162));s=e.stateNode,i=e.memoizedProps;try{s.nodeValue=i}catch(x){q(e,e.return,x)}}break;case 3:if(Ke(t,e),et(e),r&4&&n!==null&&n.memoizedState.isDehydrated)try{zr(t.containerInfo)}catch(x){q(e,e.return,x)}break;case 4:Ke(t,e),et(e);break;case 13:Ke(t,e),et(e),s=e.child,s.flags&8192&&(i=s.memoizedState!==null,s.stateNode.isHidden=i,!i||s.alternate!==null&&s.alternate.memoizedState!==null||(_l=ee())),r&4&&Ec(e);break;case 22:if(c=n!==null&&n.memoizedState!==null,e.mode&1?(ye=(u=ye)||c,Ke(t,e),ye=u):Ke(t,e),et(e),r&8192){if(u=e.memoizedState!==null,(e.stateNode.isHidden=u)&&!c&&e.mode&1)for(M=e,c=e.child;c!==null;){for(d=M=c;M!==null;){switch(h=M,g=h.child,h.tag){case 0:case 11:case 14:case 15:jr(4,h,h.return);break;case 1:An(h,h.return);var v=h.stateNode;if(typeof v.componentWillUnmount=="function"){r=h,n=h.return;try{t=r,v.props=t.memoizedProps,v.state=t.memoizedState,v.componentWillUnmount()}catch(x){q(r,n,x)}}break;case 5:An(h,h.return);break;case 22:if(h.memoizedState!==null){Nc(d);continue}}g!==null?(g.return=h,M=g):Nc(d)}c=c.sibling}e:for(c=null,d=e;;){if(d.tag===5){if(c===null){c=d;try{s=d.stateNode,u?(i=s.style,typeof i.setProperty=="function"?i.setProperty("display","none","important"):i.display="none"):(a=d.stateNode,l=d.memoizedProps.style,o=l!=null&&l.hasOwnProperty("display")?l.display:null,a.style.display=Cd("display",o))}catch(x){q(e,e.return,x)}}}else if(d.tag===6){if(c===null)try{d.stateNode.nodeValue=u?"":d.memoizedProps}catch(x){q(e,e.return,x)}}else if((d.tag!==22&&d.tag!==23||d.memoizedState===null||d===e)&&d.child!==null){d.child.return=d,d=d.child;continue}if(d===e)break e;for(;d.sibling===null;){if(d.return===null||d.return===e)break e;c===d&&(c=null),d=d.return}c===d&&(c=null),d.sibling.return=d.return,d=d.sibling}}break;case 19:Ke(t,e),et(e),r&4&&Ec(e);break;case 21:break;default:Ke(t,e),et(e)}}function et(e){var t=e.flags;if(t&2){try{e:{for(var n=e.return;n!==null;){if(qh(n)){var r=n;break e}n=n.return}throw Error(E(160))}switch(r.tag){case 5:var s=r.stateNode;r.flags&32&&(_r(s,""),r.flags&=-33);var i=Cc(e);ja(e,i,s);break;case 3:case 4:var o=r.stateNode.containerInfo,a=Cc(e);Ea(e,a,o);break;default:throw Error(E(161))}}catch(l){q(e,e.return,l)}e.flags&=-3}t&4096&&(e.flags&=-4097)}function c0(e,t,n){M=e,ep(e)}function ep(e,t,n){for(var r=(e.mode&1)!==0;M!==null;){var s=M,i=s.child;if(s.tag===22&&r){var o=s.memoizedState!==null||Ms;if(!o){var a=s.alternate,l=a!==null&&a.memoizedState!==null||ye;a=Ms;var u=ye;if(Ms=o,(ye=l)&&!u)for(M=s;M!==null;)o=M,l=o.child,o.tag===22&&o.memoizedState!==null?Ac(s):l!==null?(l.return=o,M=l):Ac(s);for(;i!==null;)M=i,ep(i),i=i.sibling;M=s,Ms=a,ye=u}jc(e)}else s.subtreeFlags&8772&&i!==null?(i.return=s,M=i):jc(e)}}function jc(e){for(;M!==null;){var t=M;if(t.flags&8772){var n=t.alternate;try{if(t.flags&8772)switch(t.tag){case 0:case 11:case 15:ye||Oi(5,t);break;case 1:var r=t.stateNode;if(t.flags&4&&!ye)if(n===null)r.componentDidMount();else{var s=t.elementType===t.type?n.memoizedProps:Qe(t.type,n.memoizedProps);r.componentDidUpdate(s,n.memoizedState,r.__reactInternalSnapshotBeforeUpdate)}var i=t.updateQueue;i!==null&&fc(t,i,r);break;case 3:var o=t.updateQueue;if(o!==null){if(n=null,t.child!==null)switch(t.child.tag){case 5:n=t.child.stateNode;break;case 1:n=t.child.stateNode}fc(t,o,n)}break;case 5:var a=t.stateNode;if(n===null&&t.flags&4){n=a;var l=t.memoizedProps;switch(t.type){case"button":case"input":case"select":case"textarea":l.autoFocus&&n.focus();break;case"img":l.src&&(n.src=l.src)}}break;case 6:break;case 4:break;case 12:break;case 13:if(t.memoizedState===null){var u=t.alternate;if(u!==null){var c=u.memoizedState;if(c!==null){var d=c.dehydrated;d!==null&&zr(d)}}}break;case 19:case 17:case 21:case 22:case 23:case 25:break;default:throw Error(E(163))}ye||t.flags&512&&Ca(t)}catch(h){q(t,t.return,h)}}if(t===e){M=null;break}if(n=t.sibling,n!==null){n.return=t.return,M=n;break}M=t.return}}function Nc(e){for(;M!==null;){var t=M;if(t===e){M=null;break}var n=t.sibling;if(n!==null){n.return=t.return,M=n;break}M=t.return}}function Ac(e){for(;M!==null;){var t=M;try{switch(t.tag){case 0:case 11:case 15:var n=t.return;try{Oi(4,t)}catch(l){q(t,n,l)}break;case 1:var r=t.stateNode;if(typeof r.componentDidMount=="function"){var s=t.return;try{r.componentDidMount()}catch(l){q(t,s,l)}}var i=t.return;try{Ca(t)}catch(l){q(t,i,l)}break;case 5:var o=t.return;try{Ca(t)}catch(l){q(t,o,l)}}}catch(l){q(t,t.return,l)}if(t===e){M=null;break}var a=t.sibling;if(a!==null){a.return=t.return,M=a;break}M=t.return}}var f0=Math.ceil,gi=xt.ReactCurrentDispatcher,Ll=xt.ReactCurrentOwner,be=xt.ReactCurrentBatchConfig,I=0,ae=null,te=null,ce=0,Ae=0,Mn=$t(0),se=0,Yr=null,hn=0,zi=0,Vl=0,Nr=null,Te=null,_l=0,Qn=1/0,at=null,vi=!1,Na=null,Vt=null,Ds=!1,Nt=null,xi=0,Ar=0,Aa=null,Ks=-1,Gs=0;function Se(){return I&6?ee():Ks!==-1?Ks:Ks=ee()}function _t(e){return e.mode&1?I&2&&ce!==0?ce&-ce:Qg.transition!==null?(Gs===0&&(Gs=Od()),Gs):(e=z,e!==0||(e=window.event,e=e===void 0?16:Wd(e.type)),e):1}function Ze(e,t,n,r){if(50<Ar)throw Ar=0,Aa=null,Error(E(185));is(e,n,r),(!(I&2)||e!==ae)&&(e===ae&&(!(I&2)&&(zi|=n),se===4&&Et(e,ce)),Ne(e,r),n===1&&I===0&&!(t.mode&1)&&(Qn=ee()+500,_i&&Ht()))}function Ne(e,t){var n=e.callbackNode;Qy(e,t);var r=ni(e,e===ae?ce:0);if(r===0)n!==null&&Ou(n),e.callbackNode=null,e.callbackPriority=0;else if(t=r&-r,e.callbackPriority!==t){if(n!=null&&Ou(n),t===1)e.tag===0?Gg(Mc.bind(null,e)):ch(Mc.bind(null,e)),$g(function(){!(I&6)&&Ht()}),n=null;else{switch(zd(r)){case 1:n=al;break;case 4:n=Fd;break;case 16:n=ti;break;case 536870912:n=Id;break;default:n=ti}n=lp(n,tp.bind(null,e))}e.callbackPriority=t,e.callbackNode=n}}function tp(e,t){if(Ks=-1,Gs=0,I&6)throw Error(E(327));var n=e.callbackNode;if(Un()&&e.callbackNode!==n)return null;var r=ni(e,e===ae?ce:0);if(r===0)return null;if(r&30||r&e.expiredLanes||t)t=wi(e,r);else{t=r;var s=I;I|=2;var i=rp();(ae!==e||ce!==t)&&(at=null,Qn=ee()+500,on(e,t));do try{p0();break}catch(a){np(e,a)}while(!0);wl(),gi.current=i,I=s,te!==null?t=0:(ae=null,ce=0,t=se)}if(t!==0){if(t===2&&(s=na(e),s!==0&&(r=s,t=Ma(e,s))),t===1)throw n=Yr,on(e,0),Et(e,r),Ne(e,ee()),n;if(t===6)Et(e,r);else{if(s=e.current.alternate,!(r&30)&&!d0(s)&&(t=wi(e,r),t===2&&(i=na(e),i!==0&&(r=i,t=Ma(e,i))),t===1))throw n=Yr,on(e,0),Et(e,r),Ne(e,ee()),n;switch(e.finishedWork=s,e.finishedLanes=r,t){case 0:case 1:throw Error(E(345));case 2:qt(e,Te,at);break;case 3:if(Et(e,r),(r&130023424)===r&&(t=_l+500-ee(),10<t)){if(ni(e,0)!==0)break;if(s=e.suspendedLanes,(s&r)!==r){Se(),e.pingedLanes|=e.suspendedLanes&s;break}e.timeoutHandle=ca(qt.bind(null,e,Te,at),t);break}qt(e,Te,at);break;case 4:if(Et(e,r),(r&4194240)===r)break;for(t=e.eventTimes,s=-1;0<r;){var o=31-qe(r);i=1<<o,o=t[o],o>s&&(s=o),r&=~i}if(r=s,r=ee()-r,r=(120>r?120:480>r?480:1080>r?1080:1920>r?1920:3e3>r?3e3:4320>r?4320:1960*f0(r/1960))-r,10<r){e.timeoutHandle=ca(qt.bind(null,e,Te,at),r);break}qt(e,Te,at);break;case 5:qt(e,Te,at);break;default:throw Error(E(329))}}}return Ne(e,ee()),e.callbackNode===n?tp.bind(null,e):null}function Ma(e,t){var n=Nr;return e.current.memoizedState.isDehydrated&&(on(e,t).flags|=256),e=wi(e,t),e!==2&&(t=Te,Te=n,t!==null&&Da(t)),e}function Da(e){Te===null?Te=e:Te.push.apply(Te,e)}function d0(e){for(var t=e;;){if(t.flags&16384){var n=t.updateQueue;if(n!==null&&(n=n.stores,n!==null))for(var r=0;r<n.length;r++){var s=n[r],i=s.getSnapshot;s=s.value;try{if(!Je(i(),s))return!1}catch{return!1}}}if(n=t.child,t.subtreeFlags&16384&&n!==null)n.return=t,t=n;else{if(t===e)break;for(;t.sibling===null;){if(t.return===null||t.return===e)return!0;t=t.return}t.sibling.return=t.return,t=t.sibling}}return!0}function Et(e,t){for(t&=~Vl,t&=~zi,e.suspendedLanes|=t,e.pingedLanes&=~t,e=e.expirationTimes;0<t;){var n=31-qe(t),r=1<<n;e[n]=-1,t&=~r}}function Mc(e){if(I&6)throw Error(E(327));Un();var t=ni(e,0);if(!(t&1))return Ne(e,ee()),null;var n=wi(e,t);if(e.tag!==0&&n===2){var r=na(e);r!==0&&(t=r,n=Ma(e,r))}if(n===1)throw n=Yr,on(e,0),Et(e,t),Ne(e,ee()),n;if(n===6)throw Error(E(345));return e.finishedWork=e.current.alternate,e.finishedLanes=t,qt(e,Te,at),Ne(e,ee()),null}function Fl(e,t){var n=I;I|=1;try{return e(t)}finally{I=n,I===0&&(Qn=ee()+500,_i&&Ht())}}function pn(e){Nt!==null&&Nt.tag===0&&!(I&6)&&Un();var t=I;I|=1;var n=be.transition,r=z;try{if(be.transition=null,z=1,e)return e()}finally{z=r,be.transition=n,I=t,!(I&6)&&Ht()}}function Il(){Ae=Mn.current,b(Mn)}function on(e,t){e.finishedWork=null,e.finishedLanes=0;var n=e.timeoutHandle;if(n!==-1&&(e.timeoutHandle=-1,bg(n)),te!==null)for(n=te.return;n!==null;){var r=n;switch(gl(r),r.tag){case 1:r=r.type.childContextTypes,r!=null&&ai();break;case 3:Kn(),b(Ee),b(ve),El();break;case 5:Cl(r);break;case 4:Kn();break;case 13:b(K);break;case 19:b(K);break;case 10:Sl(r.type._context);break;case 22:case 23:Il()}n=n.return}if(ae=e,te=e=Ft(e.current,null),ce=Ae=t,se=0,Yr=null,Vl=zi=hn=0,Te=Nr=null,nn!==null){for(t=0;t<nn.length;t++)if(n=nn[t],r=n.interleaved,r!==null){n.interleaved=null;var s=r.next,i=n.pending;if(i!==null){var o=i.next;i.next=s,r.next=o}n.pending=r}nn=null}return e}function np(e,t){do{var n=te;try{if(wl(),$s.current=yi,mi){for(var r=Q.memoizedState;r!==null;){var s=r.queue;s!==null&&(s.pending=null),r=r.next}mi=!1}if(dn=0,oe=re=Q=null,Er=!1,Gr=0,Ll.current=null,n===null||n.return===null){se=1,Yr=t,te=null;break}e:{var i=e,o=n.return,a=n,l=t;if(t=ce,a.flags|=32768,l!==null&&typeof l=="object"&&typeof l.then=="function"){var u=l,c=a,d=c.tag;if(!(c.mode&1)&&(d===0||d===11||d===15)){var h=c.alternate;h?(c.updateQueue=h.updateQueue,c.memoizedState=h.memoizedState,c.lanes=h.lanes):(c.updateQueue=null,c.memoizedState=null)}var g=gc(o);if(g!==null){g.flags&=-257,vc(g,o,a,i,t),g.mode&1&&yc(i,u,t),t=g,l=u;var v=t.updateQueue;if(v===null){var x=new Set;x.add(l),t.updateQueue=x}else v.add(l);break e}else{if(!(t&1)){yc(i,u,t),Ol();break e}l=Error(E(426))}}else if(W&&a.mode&1){var P=gc(o);if(P!==null){!(P.flags&65536)&&(P.flags|=256),vc(P,o,a,i,t),vl(Gn(l,a));break e}}i=l=Gn(l,a),se!==4&&(se=2),Nr===null?Nr=[i]:Nr.push(i),i=o;do{switch(i.tag){case 3:i.flags|=65536,t&=-t,i.lanes|=t;var m=zh(i,l,t);cc(i,m);break e;case 1:a=l;var p=i.type,y=i.stateNode;if(!(i.flags&128)&&(typeof p.getDerivedStateFromError=="function"||y!==null&&typeof y.componentDidCatch=="function"&&(Vt===null||!Vt.has(y)))){i.flags|=65536,t&=-t,i.lanes|=t;var w=Bh(i,a,t);cc(i,w);break e}}i=i.return}while(i!==null)}ip(n)}catch(S){t=S,te===n&&n!==null&&(te=n=n.return);continue}break}while(!0)}function rp(){var e=gi.current;return gi.current=yi,e===null?yi:e}function Ol(){(se===0||se===3||se===2)&&(se=4),ae===null||!(hn&268435455)&&!(zi&268435455)||Et(ae,ce)}function wi(e,t){var n=I;I|=2;var r=rp();(ae!==e||ce!==t)&&(at=null,on(e,t));do try{h0();break}catch(s){np(e,s)}while(!0);if(wl(),I=n,gi.current=r,te!==null)throw Error(E(261));return ae=null,ce=0,se}function h0(){for(;te!==null;)sp(te)}function p0(){for(;te!==null&&!zy();)sp(te)}function sp(e){var t=ap(e.alternate,e,Ae);e.memoizedProps=e.pendingProps,t===null?ip(e):te=t,Ll.current=null}function ip(e){var t=e;do{var n=t.alternate;if(e=t.return,t.flags&32768){if(n=a0(n,t),n!==null){n.flags&=32767,te=n;return}if(e!==null)e.flags|=32768,e.subtreeFlags=0,e.deletions=null;else{se=6,te=null;return}}else if(n=o0(n,t,Ae),n!==null){te=n;return}if(t=t.sibling,t!==null){te=t;return}te=t=e}while(t!==null);se===0&&(se=5)}function qt(e,t,n){var r=z,s=be.transition;try{be.transition=null,z=1,m0(e,t,n,r)}finally{be.transition=s,z=r}return null}function m0(e,t,n,r){do Un();while(Nt!==null);if(I&6)throw Error(E(327));n=e.finishedWork;var s=e.finishedLanes;if(n===null)return null;if(e.finishedWork=null,e.finishedLanes=0,n===e.current)throw Error(E(177));e.callbackNode=null,e.callbackPriority=0;var i=n.lanes|n.childLanes;if(Xy(e,i),e===ae&&(te=ae=null,ce=0),!(n.subtreeFlags&2064)&&!(n.flags&2064)||Ds||(Ds=!0,lp(ti,function(){return Un(),null})),i=(n.flags&15990)!==0,n.subtreeFlags&15990||i){i=be.transition,be.transition=null;var o=z;z=1;var a=I;I|=4,Ll.current=null,u0(e,n),Jh(n,e),_g(la),ri=!!aa,la=aa=null,e.current=n,c0(n),By(),I=a,z=o,be.transition=i}else e.current=n;if(Ds&&(Ds=!1,Nt=e,xi=s),i=e.pendingLanes,i===0&&(Vt=null),$y(n.stateNode),Ne(e,ee()),t!==null)for(r=e.onRecoverableError,n=0;n<t.length;n++)s=t[n],r(s.value,{componentStack:s.stack,digest:s.digest});if(vi)throw vi=!1,e=Na,Na=null,e;return xi&1&&e.tag!==0&&Un(),i=e.pendingLanes,i&1?e===Aa?Ar++:(Ar=0,Aa=e):Ar=0,Ht(),null}function Un(){if(Nt!==null){var e=zd(xi),t=be.transition,n=z;try{if(be.transition=null,z=16>e?16:e,Nt===null)var r=!1;else{if(e=Nt,Nt=null,xi=0,I&6)throw Error(E(331));var s=I;for(I|=4,M=e.current;M!==null;){var i=M,o=i.child;if(M.flags&16){var a=i.deletions;if(a!==null){for(var l=0;l<a.length;l++){var u=a[l];for(M=u;M!==null;){var c=M;switch(c.tag){case 0:case 11:case 15:jr(8,c,i)}var d=c.child;if(d!==null)d.return=c,M=d;else for(;M!==null;){c=M;var h=c.sibling,g=c.return;if(Yh(c),c===u){M=null;break}if(h!==null){h.return=g,M=h;break}M=g}}}var v=i.alternate;if(v!==null){var x=v.child;if(x!==null){v.child=null;do{var P=x.sibling;x.sibling=null,x=P}while(x!==null)}}M=i}}if(i.subtreeFlags&2064&&o!==null)o.return=i,M=o;else e:for(;M!==null;){if(i=M,i.flags&2048)switch(i.tag){case 0:case 11:case 15:jr(9,i,i.return)}var m=i.sibling;if(m!==null){m.return=i.return,M=m;break e}M=i.return}}var p=e.current;for(M=p;M!==null;){o=M;var y=o.child;if(o.subtreeFlags&2064&&y!==null)y.return=o,M=y;else e:for(o=p;M!==null;){if(a=M,a.flags&2048)try{switch(a.tag){case 0:case 11:case 15:Oi(9,a)}}catch(S){q(a,a.return,S)}if(a===o){M=null;break e}var w=a.sibling;if(w!==null){w.return=a.return,M=w;break e}M=a.return}}if(I=s,Ht(),rt&&typeof rt.onPostCommitFiberRoot=="function")try{rt.onPostCommitFiberRoot(Mi,e)}catch{}r=!0}return r}finally{z=n,be.transition=t}}return!1}function Dc(e,t,n){t=Gn(n,t),t=zh(e,t,1),e=Lt(e,t,1),t=Se(),e!==null&&(is(e,1,t),Ne(e,t))}function q(e,t,n){if(e.tag===3)Dc(e,e,n);else for(;t!==null;){if(t.tag===3){Dc(t,e,n);break}else if(t.tag===1){var r=t.stateNode;if(typeof t.type.getDerivedStateFromError=="function"||typeof r.componentDidCatch=="function"&&(Vt===null||!Vt.has(r))){e=Gn(n,e),e=Bh(t,e,1),t=Lt(t,e,1),e=Se(),t!==null&&(is(t,1,e),Ne(t,e));break}}t=t.return}}function y0(e,t,n){var r=e.pingCache;r!==null&&r.delete(t),t=Se(),e.pingedLanes|=e.suspendedLanes&n,ae===e&&(ce&n)===n&&(se===4||se===3&&(ce&130023424)===ce&&500>ee()-_l?on(e,0):Vl|=n),Ne(e,t)}function op(e,t){t===0&&(e.mode&1?(t=Ss,Ss<<=1,!(Ss&130023424)&&(Ss=4194304)):t=1);var n=Se();e=yt(e,t),e!==null&&(is(e,t,n),Ne(e,n))}function g0(e){var t=e.memoizedState,n=0;t!==null&&(n=t.retryLane),op(e,n)}function v0(e,t){var n=0;switch(e.tag){case 13:var r=e.stateNode,s=e.memoizedState;s!==null&&(n=s.retryLane);break;case 19:r=e.stateNode;break;default:throw Error(E(314))}r!==null&&r.delete(t),op(e,n)}var ap;ap=function(e,t,n){if(e!==null)if(e.memoizedProps!==t.pendingProps||Ee.current)Ce=!0;else{if(!(e.lanes&n)&&!(t.flags&128))return Ce=!1,i0(e,t,n);Ce=!!(e.flags&131072)}else Ce=!1,W&&t.flags&1048576&&fh(t,ci,t.index);switch(t.lanes=0,t.tag){case 2:var r=t.type;Ws(e,t),e=t.pendingProps;var s=$n(t,ve.current);Bn(t,n),s=Nl(null,t,r,e,s,n);var i=Al();return t.flags|=1,typeof s=="object"&&s!==null&&typeof s.render=="function"&&s.$$typeof===void 0?(t.tag=1,t.memoizedState=null,t.updateQueue=null,je(r)?(i=!0,li(t)):i=!1,t.memoizedState=s.state!==null&&s.state!==void 0?s.state:null,Pl(t),s.updater=Ii,t.stateNode=s,s._reactInternals=t,ga(t,r,e,n),t=wa(null,t,r,!0,i,n)):(t.tag=0,W&&i&&yl(t),we(null,t,s,n),t=t.child),t;case 16:r=t.elementType;e:{switch(Ws(e,t),e=t.pendingProps,s=r._init,r=s(r._payload),t.type=r,s=t.tag=w0(r),e=Qe(r,e),s){case 0:t=xa(null,t,r,e,n);break e;case 1:t=Sc(null,t,r,e,n);break e;case 11:t=xc(null,t,r,e,n);break e;case 14:t=wc(null,t,r,Qe(r.type,e),n);break e}throw Error(E(306,r,""))}return t;case 0:return r=t.type,s=t.pendingProps,s=t.elementType===r?s:Qe(r,s),xa(e,t,r,s,n);case 1:return r=t.type,s=t.pendingProps,s=t.elementType===r?s:Qe(r,s),Sc(e,t,r,s,n);case 3:e:{if(Hh(t),e===null)throw Error(E(387));r=t.pendingProps,i=t.memoizedState,s=i.element,gh(e,t),hi(t,r,null,n);var o=t.memoizedState;if(r=o.element,i.isDehydrated)if(i={element:r,isDehydrated:!1,cache:o.cache,pendingSuspenseBoundaries:o.pendingSuspenseBoundaries,transitions:o.transitions},t.updateQueue.baseState=i,t.memoizedState=i,t.flags&256){s=Gn(Error(E(423)),t),t=kc(e,t,r,n,s);break e}else if(r!==s){s=Gn(Error(E(424)),t),t=kc(e,t,r,n,s);break e}else for(Me=Rt(t.stateNode.containerInfo.firstChild),De=t,W=!0,Ye=null,n=mh(t,null,r,n),t.child=n;n;)n.flags=n.flags&-3|4096,n=n.sibling;else{if(Hn(),r===s){t=gt(e,t,n);break e}we(e,t,r,n)}t=t.child}return t;case 5:return vh(t),e===null&&pa(t),r=t.type,s=t.pendingProps,i=e!==null?e.memoizedProps:null,o=s.children,ua(r,s)?o=null:i!==null&&ua(r,i)&&(t.flags|=32),$h(e,t),we(e,t,o,n),t.child;case 6:return e===null&&pa(t),null;case 13:return Wh(e,t,n);case 4:return Tl(t,t.stateNode.containerInfo),r=t.pendingProps,e===null?t.child=Wn(t,null,r,n):we(e,t,r,n),t.child;case 11:return r=t.type,s=t.pendingProps,s=t.elementType===r?s:Qe(r,s),xc(e,t,r,s,n);case 7:return we(e,t,t.pendingProps,n),t.child;case 8:return we(e,t,t.pendingProps.children,n),t.child;case 12:return we(e,t,t.pendingProps.children,n),t.child;case 10:e:{if(r=t.type._context,s=t.pendingProps,i=t.memoizedProps,o=s.value,B(fi,r._currentValue),r._currentValue=o,i!==null)if(Je(i.value,o)){if(i.children===s.children&&!Ee.current){t=gt(e,t,n);break e}}else for(i=t.child,i!==null&&(i.return=t);i!==null;){var a=i.dependencies;if(a!==null){o=i.child;for(var l=a.firstContext;l!==null;){if(l.context===r){if(i.tag===1){l=ft(-1,n&-n),l.tag=2;var u=i.updateQueue;if(u!==null){u=u.shared;var c=u.pending;c===null?l.next=l:(l.next=c.next,c.next=l),u.pending=l}}i.lanes|=n,l=i.alternate,l!==null&&(l.lanes|=n),ma(i.return,n,t),a.lanes|=n;break}l=l.next}}else if(i.tag===10)o=i.type===t.type?null:i.child;else if(i.tag===18){if(o=i.return,o===null)throw Error(E(341));o.lanes|=n,a=o.alternate,a!==null&&(a.lanes|=n),ma(o,n,t),o=i.sibling}else o=i.child;if(o!==null)o.return=i;else for(o=i;o!==null;){if(o===t){o=null;break}if(i=o.sibling,i!==null){i.return=o.return,o=i;break}o=o.return}i=o}we(e,t,s.children,n),t=t.child}return t;case 9:return s=t.type,r=t.pendingProps.children,Bn(t,n),s=$e(s),r=r(s),t.flags|=1,we(e,t,r,n),t.child;case 14:return r=t.type,s=Qe(r,t.pendingProps),s=Qe(r.type,s),wc(e,t,r,s,n);case 15:return Uh(e,t,t.type,t.pendingProps,n);case 17:return r=t.type,s=t.pendingProps,s=t.elementType===r?s:Qe(r,s),Ws(e,t),t.tag=1,je(r)?(e=!0,li(t)):e=!1,Bn(t,n),Oh(t,r,s),ga(t,r,s,n),wa(null,t,r,!0,e,n);case 19:return Kh(e,t,n);case 22:return bh(e,t,n)}throw Error(E(156,t.tag))};function lp(e,t){return _d(e,t)}function x0(e,t,n,r){this.tag=e,this.key=n,this.sibling=this.child=this.return=this.stateNode=this.type=this.elementType=null,this.index=0,this.ref=null,this.pendingProps=t,this.dependencies=this.memoizedState=this.updateQueue=this.memoizedProps=null,this.mode=r,this.subtreeFlags=this.flags=0,this.deletions=null,this.childLanes=this.lanes=0,this.alternate=null}function Ue(e,t,n,r){return new x0(e,t,n,r)}function zl(e){return e=e.prototype,!(!e||!e.isReactComponent)}function w0(e){if(typeof e=="function")return zl(e)?1:0;if(e!=null){if(e=e.$$typeof,e===sl)return 11;if(e===il)return 14}return 2}function Ft(e,t){var n=e.alternate;return n===null?(n=Ue(e.tag,t,e.key,e.mode),n.elementType=e.elementType,n.type=e.type,n.stateNode=e.stateNode,n.alternate=e,e.alternate=n):(n.pendingProps=t,n.type=e.type,n.flags=0,n.subtreeFlags=0,n.deletions=null),n.flags=e.flags&14680064,n.childLanes=e.childLanes,n.lanes=e.lanes,n.child=e.child,n.memoizedProps=e.memoizedProps,n.memoizedState=e.memoizedState,n.updateQueue=e.updateQueue,t=e.dependencies,n.dependencies=t===null?null:{lanes:t.lanes,firstContext:t.firstContext},n.sibling=e.sibling,n.index=e.index,n.ref=e.ref,n}function Qs(e,t,n,r,s,i){var o=2;if(r=e,typeof e=="function")zl(e)&&(o=1);else if(typeof e=="string")o=5;else e:switch(e){case wn:return an(n.children,s,i,t);case rl:o=8,s|=8;break;case Uo:return e=Ue(12,n,t,s|2),e.elementType=Uo,e.lanes=i,e;case bo:return e=Ue(13,n,t,s),e.elementType=bo,e.lanes=i,e;case $o:return e=Ue(19,n,t,s),e.elementType=$o,e.lanes=i,e;case vd:return Bi(n,s,i,t);default:if(typeof e=="object"&&e!==null)switch(e.$$typeof){case yd:o=10;break e;case gd:o=9;break e;case sl:o=11;break e;case il:o=14;break e;case Pt:o=16,r=null;break e}throw Error(E(130,e==null?e:typeof e,""))}return t=Ue(o,n,t,s),t.elementType=e,t.type=r,t.lanes=i,t}function an(e,t,n,r){return e=Ue(7,e,r,t),e.lanes=n,e}function Bi(e,t,n,r){return e=Ue(22,e,r,t),e.elementType=vd,e.lanes=n,e.stateNode={isHidden:!1},e}function To(e,t,n){return e=Ue(6,e,null,t),e.lanes=n,e}function Co(e,t,n){return t=Ue(4,e.children!==null?e.children:[],e.key,t),t.lanes=n,t.stateNode={containerInfo:e.containerInfo,pendingChildren:null,implementation:e.implementation},t}function S0(e,t,n,r,s){this.tag=t,this.containerInfo=e,this.finishedWork=this.pingCache=this.current=this.pendingChildren=null,this.timeoutHandle=-1,this.callbackNode=this.pendingContext=this.context=null,this.callbackPriority=0,this.eventTimes=so(0),this.expirationTimes=so(-1),this.entangledLanes=this.finishedLanes=this.mutableReadLanes=this.expiredLanes=this.pingedLanes=this.suspendedLanes=this.pendingLanes=0,this.entanglements=so(0),this.identifierPrefix=r,this.onRecoverableError=s,this.mutableSourceEagerHydrationData=null}function Bl(e,t,n,r,s,i,o,a,l){return e=new S0(e,t,n,a,l),t===1?(t=1,i===!0&&(t|=8)):t=0,i=Ue(3,null,null,t),e.current=i,i.stateNode=e,i.memoizedState={element:r,isDehydrated:n,cache:null,transitions:null,pendingSuspenseBoundaries:null},Pl(i),e}function k0(e,t,n){var r=3<arguments.length&&arguments[3]!==void 0?arguments[3]:null;return{$$typeof:xn,key:r==null?null:""+r,children:e,containerInfo:t,implementation:n}}function up(e){if(!e)return Ot;e=e._reactInternals;e:{if(yn(e)!==e||e.tag!==1)throw Error(E(170));var t=e;do{switch(t.tag){case 3:t=t.stateNode.context;break e;case 1:if(je(t.type)){t=t.stateNode.__reactInternalMemoizedMergedChildContext;break e}}t=t.return}while(t!==null);throw Error(E(171))}if(e.tag===1){var n=e.type;if(je(n))return uh(e,n,t)}return t}function cp(e,t,n,r,s,i,o,a,l){return e=Bl(n,r,!0,e,s,i,o,a,l),e.context=up(null),n=e.current,r=Se(),s=_t(n),i=ft(r,s),i.callback=t??null,Lt(n,i,s),e.current.lanes=s,is(e,s,r),Ne(e,r),e}function Ui(e,t,n,r){var s=t.current,i=Se(),o=_t(s);return n=up(n),t.context===null?t.context=n:t.pendingContext=n,t=ft(i,o),t.payload={element:e},r=r===void 0?null:r,r!==null&&(t.callback=r),e=Lt(s,t,o),e!==null&&(Ze(e,s,o,i),bs(e,s,o)),o}function Si(e){if(e=e.current,!e.child)return null;switch(e.child.tag){case 5:return e.child.stateNode;default:return e.child.stateNode}}function Rc(e,t){if(e=e.memoizedState,e!==null&&e.dehydrated!==null){var n=e.retryLane;e.retryLane=n!==0&&n<t?n:t}}function Ul(e,t){Rc(e,t),(e=e.alternate)&&Rc(e,t)}function P0(){return null}var fp=typeof reportError=="function"?reportError:function(e){console.error(e)};function bl(e){this._internalRoot=e}bi.prototype.render=bl.prototype.render=function(e){var t=this._internalRoot;if(t===null)throw Error(E(409));Ui(e,t,null,null)};bi.prototype.unmount=bl.prototype.unmount=function(){var e=this._internalRoot;if(e!==null){this._internalRoot=null;var t=e.containerInfo;pn(function(){Ui(null,e,null,null)}),t[mt]=null}};function bi(e){this._internalRoot=e}bi.prototype.unstable_scheduleHydration=function(e){if(e){var t=bd();e={blockedOn:null,target:e,priority:t};for(var n=0;n<Ct.length&&t!==0&&t<Ct[n].priority;n++);Ct.splice(n,0,e),n===0&&Hd(e)}};function $l(e){return!(!e||e.nodeType!==1&&e.nodeType!==9&&e.nodeType!==11)}function $i(e){return!(!e||e.nodeType!==1&&e.nodeType!==9&&e.nodeType!==11&&(e.nodeType!==8||e.nodeValue!==" react-mount-point-unstable "))}function Lc(){}function T0(e,t,n,r,s){if(s){if(typeof r=="function"){var i=r;r=function(){var u=Si(o);i.call(u)}}var o=cp(t,r,e,0,null,!1,!1,"",Lc);return e._reactRootContainer=o,e[mt]=o.current,br(e.nodeType===8?e.parentNode:e),pn(),o}for(;s=e.lastChild;)e.removeChild(s);if(typeof r=="function"){var a=r;r=function(){var u=Si(l);a.call(u)}}var l=Bl(e,0,!1,null,null,!1,!1,"",Lc);return e._reactRootContainer=l,e[mt]=l.current,br(e.nodeType===8?e.parentNode:e),pn(function(){Ui(t,l,n,r)}),l}function Hi(e,t,n,r,s){var i=n._reactRootContainer;if(i){var o=i;if(typeof s=="function"){var a=s;s=function(){var l=Si(o);a.call(l)}}Ui(t,o,e,s)}else o=T0(n,t,e,s,r);return Si(o)}Bd=function(e){switch(e.tag){case 3:var t=e.stateNode;if(t.current.memoizedState.isDehydrated){var n=yr(t.pendingLanes);n!==0&&(ll(t,n|1),Ne(t,ee()),!(I&6)&&(Qn=ee()+500,Ht()))}break;case 13:pn(function(){var r=yt(e,1);if(r!==null){var s=Se();Ze(r,e,1,s)}}),Ul(e,1)}};ul=function(e){if(e.tag===13){var t=yt(e,134217728);if(t!==null){var n=Se();Ze(t,e,134217728,n)}Ul(e,134217728)}};Ud=function(e){if(e.tag===13){var t=_t(e),n=yt(e,t);if(n!==null){var r=Se();Ze(n,e,t,r)}Ul(e,t)}};bd=function(){return z};$d=function(e,t){var n=z;try{return z=e,t()}finally{z=n}};Jo=function(e,t,n){switch(t){case"input":if(Ko(e,n),t=n.name,n.type==="radio"&&t!=null){for(n=e;n.parentNode;)n=n.parentNode;for(n=n.querySelectorAll("input[name="+JSON.stringify(""+t)+'][type="radio"]'),t=0;t<n.length;t++){var r=n[t];if(r!==e&&r.form===e.form){var s=Vi(r);if(!s)throw Error(E(90));wd(r),Ko(r,s)}}}break;case"textarea":kd(e,n);break;case"select":t=n.value,t!=null&&Fn(e,!!n.multiple,t,!1)}};Ad=Fl;Md=pn;var C0={usingClientEntryPoint:!1,Events:[as,Tn,Vi,jd,Nd,Fl]},dr={findFiberByHostInstance:tn,bundleType:0,version:"18.3.1",rendererPackageName:"react-dom"},E0={bundleType:dr.bundleType,version:dr.version,rendererPackageName:dr.rendererPackageName,rendererConfig:dr.rendererConfig,overrideHookState:null,overrideHookStateDeletePath:null,overrideHookStateRenamePath:null,overrideProps:null,overridePropsDeletePath:null,overridePropsRenamePath:null,setErrorHandler:null,setSuspenseHandler:null,scheduleUpdate:null,currentDispatcherRef:xt.ReactCurrentDispatcher,findHostInstanceByFiber:function(e){return e=Ld(e),e===null?null:e.stateNode},findFiberByHostInstance:dr.findFiberByHostInstance||P0,findHostInstancesForRefresh:null,scheduleRefresh:null,scheduleRoot:null,setRefreshHandler:null,getCurrentFiber:null,reconcilerVersion:"18.3.1-next-f1338f8080-20240426"};if(typeof __REACT_DEVTOOLS_GLOBAL_HOOK__<"u"){var Rs=__REACT_DEVTOOLS_GLOBAL_HOOK__;if(!Rs.isDisabled&&Rs.supportsFiber)try{Mi=Rs.inject(E0),rt=Rs}catch{}}_e.__SECRET_INTERNALS_DO_NOT_USE_OR_YOU_WILL_BE_FIRED=C0;_e.createPortal=function(e,t){var n=2<arguments.length&&arguments[2]!==void 0?arguments[2]:null;if(!$l(t))throw Error(E(200));return k0(e,t,null,n)};_e.createRoot=function(e,t){if(!$l(e))throw Error(E(299));var n=!1,r="",s=fp;return t!=null&&(t.unstable_strictMode===!0&&(n=!0),t.identifierPrefix!==void 0&&(r=t.identifierPrefix),t.onRecoverableError!==void 0&&(s=t.onRecoverableError)),t=Bl(e,1,!1,null,null,n,!1,r,s),e[mt]=t.current,br(e.nodeType===8?e.parentNode:e),new bl(t)};_e.findDOMNode=function(e){if(e==null)return null;if(e.nodeType===1)return e;var t=e._reactInternals;if(t===void 0)throw typeof e.render=="function"?Error(E(188)):(e=Object.keys(e).join(","),Error(E(268,e)));return e=Ld(t),e=e===null?null:e.stateNode,e};_e.flushSync=function(e){return pn(e)};_e.hydrate=function(e,t,n){if(!$i(t))throw Error(E(200));return Hi(null,e,t,!0,n)};_e.hydrateRoot=function(e,t,n){if(!$l(e))throw Error(E(405));var r=n!=null&&n.hydratedSources||null,s=!1,i="",o=fp;if(n!=null&&(n.unstable_strictMode===!0&&(s=!0),n.identifierPrefix!==void 0&&(i=n.identifierPrefix),n.onRecoverableError!==void 0&&(o=n.onRecoverableError)),t=cp(t,null,e,1,n??null,s,!1,i,o),e[mt]=t.current,br(e),r)for(e=0;e<r.length;e++)n=r[e],s=n._getVersion,s=s(n._source),t.mutableSourceEagerHydrationData==null?t.mutableSourceEagerHydrationData=[n,s]:t.mutableSourceEagerHydrationData.push(n,s);return new bi(t)};_e.render=function(e,t,n){if(!$i(t))throw Error(E(200));return Hi(null,e,t,!1,n)};_e.unmountComponentAtNode=function(e){if(!$i(e))throw Error(E(40));return e._reactRootContainer?(pn(function(){Hi(null,null,e,!1,function(){e._reactRootContainer=null,e[mt]=null})}),!0):!1};_e.unstable_batchedUpdates=Fl;_e.unstable_renderSubtreeIntoContainer=function(e,t,n,r){if(!$i(n))throw Error(E(200));if(e==null||e._reactInternals===void 0)throw Error(E(38));return Hi(e,t,n,!1,r)};_e.version="18.3.1-next-f1338f8080-20240426";function dp(){if(!(typeof __REACT_DEVTOOLS_GLOBAL_HOOK__>"u"||typeof __REACT_DEVTOOLS_GLOBAL_HOOK__.checkDCE!="function"))try{__REACT_DEVTOOLS_GLOBAL_HOOK__.checkDCE(dp)}catch(e){console.error(e)}}dp(),dd.exports=_e;var j0=dd.exports,Vc=j0;zo.createRoot=Vc.createRoot,zo.hydrateRoot=Vc.hydrateRoot;const Hl=k.createContext({});function Wl(e){const t=k.useRef(null);return t.current===null&&(t.current=e()),t.current}const Wi=k.createContext(null),Kl=k.createContext({transformPagePoint:e=>e,isStatic:!1,reducedMotion:"never"});class N0 extends k.Component{getSnapshotBeforeUpdate(t){const n=this.props.childRef.current;if(n&&t.isPresent&&!this.props.isPresent){const r=this.props.sizeRef.current;r.height=n.offsetHeight||0,r.width=n.offsetWidth||0,r.top=n.offsetTop,r.left=n.offsetLeft}return null}componentDidUpdate(){}render(){return this.props.children}}function A0({children:e,isPresent:t}){const n=k.useId(),r=k.useRef(null),s=k.useRef({width:0,height:0,top:0,left:0}),{nonce:i}=k.useContext(Kl);return k.useInsertionEffect(()=>{const{width:o,height:a,top:l,left:u}=s.current;if(t||!r.current||!o||!a)return;r.current.dataset.motionPopId=n;const c=document.createElement("style");return i&&(c.nonce=i),document.head.appendChild(c),c.sheet&&c.sheet.insertRule(`
          [data-motion-pop-id="${n}"] {
            position: absolute !important;
            width: ${o}px !important;
            height: ${a}px !important;
            top: ${l}px !important;
            left: ${u}px !important;
          }
        `),()=>{document.head.removeChild(c)}},[t]),f.jsx(N0,{isPresent:t,childRef:r,sizeRef:s,children:k.cloneElement(e,{ref:r})})}const M0=({children:e,initial:t,isPresent:n,onExitComplete:r,custom:s,presenceAffectsLayout:i,mode:o})=>{const a=Wl(D0),l=k.useId(),u=k.useCallback(d=>{a.set(d,!0);for(const h of a.values())if(!h)return;r&&r()},[a,r]),c=k.useMemo(()=>({id:l,initial:t,isPresent:n,custom:s,onExitComplete:u,register:d=>(a.set(d,!1),()=>a.delete(d))}),i?[Math.random(),u]:[n,u]);return k.useMemo(()=>{a.forEach((d,h)=>a.set(h,!1))},[n]),k.useEffect(()=>{!n&&!a.size&&r&&r()},[n]),o==="popLayout"&&(e=f.jsx(A0,{isPresent:n,children:e})),f.jsx(Wi.Provider,{value:c,children:e})};function D0(){return new Map}function hp(e=!0){const t=k.useContext(Wi);if(t===null)return[!0,null];const{isPresent:n,onExitComplete:r,register:s}=t,i=k.useId();k.useEffect(()=>{e&&s(i)},[e]);const o=k.useCallback(()=>e&&r&&r(i),[i,r,e]);return!n&&r?[!1,o]:[!0]}const Ls=e=>e.key||"";function _c(e){const t=[];return k.Children.forEach(e,n=>{k.isValidElement(n)&&t.push(n)}),t}const Gl=typeof window<"u",pp=Gl?k.useLayoutEffect:k.useEffect,mp=({children:e,custom:t,initial:n=!0,onExitComplete:r,presenceAffectsLayout:s=!0,mode:i="sync",propagate:o=!1})=>{const[a,l]=hp(o),u=k.useMemo(()=>_c(e),[e]),c=o&&!a?[]:u.map(Ls),d=k.useRef(!0),h=k.useRef(u),g=Wl(()=>new Map),[v,x]=k.useState(u),[P,m]=k.useState(u);pp(()=>{d.current=!1,h.current=u;for(let w=0;w<P.length;w++){const S=Ls(P[w]);c.includes(S)?g.delete(S):g.get(S)!==!0&&g.set(S,!1)}},[P,c.length,c.join("-")]);const p=[];if(u!==v){let w=[...u];for(let S=0;S<P.length;S++){const C=P[S],j=Ls(C);c.includes(j)||(w.splice(S,0,C),p.push(C))}i==="wait"&&p.length&&(w=p),m(_c(w)),x(u);return}const{forceRender:y}=k.useContext(Hl);return f.jsx(f.Fragment,{children:P.map(w=>{const S=Ls(w),C=o&&!a?!1:u===P||c.includes(S),j=()=>{if(g.has(S))g.set(S,!0);else return;let T=!0;g.forEach(D=>{D||(T=!1)}),T&&(y==null||y(),m(h.current),o&&(l==null||l()),r&&r())};return f.jsx(M0,{isPresent:C,initial:!d.current||n?void 0:!1,custom:C?void 0:t,presenceAffectsLayout:s,mode:i,onExitComplete:C?void 0:j,children:w},S)})})},Re=e=>e;let yp=Re;function Ql(e){let t;return()=>(t===void 0&&(t=e()),t)}const Xn=(e,t,n)=>{const r=t-e;return r===0?1:(n-e)/r},dt=e=>e*1e3,ht=e=>e/1e3,R0={useManualTiming:!1};function L0(e){let t=new Set,n=new Set,r=!1,s=!1;const i=new WeakSet;let o={delta:0,timestamp:0,isProcessing:!1};function a(u){i.has(u)&&(l.schedule(u),e()),u(o)}const l={schedule:(u,c=!1,d=!1)=>{const g=d&&r?t:n;return c&&i.add(u),g.has(u)||g.add(u),u},cancel:u=>{n.delete(u),i.delete(u)},process:u=>{if(o=u,r){s=!0;return}r=!0,[t,n]=[n,t],t.forEach(a),t.clear(),r=!1,s&&(s=!1,l.process(u))}};return l}const Vs=["read","resolveKeyframes","update","preRender","render","postRender"],V0=40;function gp(e,t){let n=!1,r=!0;const s={delta:0,timestamp:0,isProcessing:!1},i=()=>n=!0,o=Vs.reduce((m,p)=>(m[p]=L0(i),m),{}),{read:a,resolveKeyframes:l,update:u,preRender:c,render:d,postRender:h}=o,g=()=>{const m=performance.now();n=!1,s.delta=r?1e3/60:Math.max(Math.min(m-s.timestamp,V0),1),s.timestamp=m,s.isProcessing=!0,a.process(s),l.process(s),u.process(s),c.process(s),d.process(s),h.process(s),s.isProcessing=!1,n&&t&&(r=!1,e(g))},v=()=>{n=!0,r=!0,s.isProcessing||e(g)};return{schedule:Vs.reduce((m,p)=>{const y=o[p];return m[p]=(w,S=!1,C=!1)=>(n||v(),y.schedule(w,S,C)),m},{}),cancel:m=>{for(let p=0;p<Vs.length;p++)o[Vs[p]].cancel(m)},state:s,steps:o}}const{schedule:$,cancel:zt,state:ue,steps:Eo}=gp(typeof requestAnimationFrame<"u"?requestAnimationFrame:Re,!0),vp=k.createContext({strict:!1}),Fc={animation:["animate","variants","whileHover","whileTap","exit","whileInView","whileFocus","whileDrag"],exit:["exit"],drag:["drag","dragControls"],focus:["whileFocus"],hover:["whileHover","onHoverStart","onHoverEnd"],tap:["whileTap","onTap","onTapStart","onTapCancel"],pan:["onPan","onPanStart","onPanSessionStart","onPanEnd"],inView:["whileInView","onViewportEnter","onViewportLeave"],layout:["layout","layoutId"]},Yn={};for(const e in Fc)Yn[e]={isEnabled:t=>Fc[e].some(n=>!!t[n])};function _0(e){for(const t in e)Yn[t]={...Yn[t],...e[t]}}const F0=new Set(["animate","exit","variants","initial","style","values","variants","transition","transformTemplate","custom","inherit","onBeforeLayoutMeasure","onAnimationStart","onAnimationComplete","onUpdate","onDragStart","onDrag","onDragEnd","onMeasureDragConstraints","onDirectionLock","onDragTransitionEnd","_dragX","_dragY","onHoverStart","onHoverEnd","onViewportEnter","onViewportLeave","globalTapTarget","ignoreStrict","viewport"]);function ki(e){return e.startsWith("while")||e.startsWith("drag")&&e!=="draggable"||e.startsWith("layout")||e.startsWith("onTap")||e.startsWith("onPan")||e.startsWith("onLayout")||F0.has(e)}let xp=e=>!ki(e);function I0(e){e&&(xp=t=>t.startsWith("on")?!ki(t):e(t))}try{I0(require("@emotion/is-prop-valid").default)}catch{}function O0(e,t,n){const r={};for(const s in e)s==="values"&&typeof e.values=="object"||(xp(s)||n===!0&&ki(s)||!t&&!ki(s)||e.draggable&&s.startsWith("onDrag"))&&(r[s]=e[s]);return r}function z0(e){if(typeof Proxy>"u")return e;const t=new Map,n=(...r)=>e(...r);return new Proxy(n,{get:(r,s)=>s==="create"?e:(t.has(s)||t.set(s,e(s)),t.get(s))})}const Ki=k.createContext({});function qr(e){return typeof e=="string"||Array.isArray(e)}function Gi(e){return e!==null&&typeof e=="object"&&typeof e.start=="function"}const Xl=["animate","whileInView","whileFocus","whileHover","whileTap","whileDrag","exit"],Yl=["initial",...Xl];function Qi(e){return Gi(e.animate)||Yl.some(t=>qr(e[t]))}function wp(e){return!!(Qi(e)||e.variants)}function B0(e,t){if(Qi(e)){const{initial:n,animate:r}=e;return{initial:n===!1||qr(n)?n:void 0,animate:qr(r)?r:void 0}}return e.inherit!==!1?t:{}}function U0(e){const{initial:t,animate:n}=B0(e,k.useContext(Ki));return k.useMemo(()=>({initial:t,animate:n}),[Ic(t),Ic(n)])}function Ic(e){return Array.isArray(e)?e.join(" "):e}const b0=Symbol.for("motionComponentSymbol");function Dn(e){return e&&typeof e=="object"&&Object.prototype.hasOwnProperty.call(e,"current")}function $0(e,t,n){return k.useCallback(r=>{r&&e.onMount&&e.onMount(r),t&&(r?t.mount(r):t.unmount()),n&&(typeof n=="function"?n(r):Dn(n)&&(n.current=r))},[t])}const ql=e=>e.replace(/([a-z])([A-Z])/gu,"$1-$2").toLowerCase(),H0="framerAppearId",Sp="data-"+ql(H0),{schedule:Zl}=gp(queueMicrotask,!1),kp=k.createContext({});function W0(e,t,n,r,s){var i,o;const{visualElement:a}=k.useContext(Ki),l=k.useContext(vp),u=k.useContext(Wi),c=k.useContext(Kl).reducedMotion,d=k.useRef(null);r=r||l.renderer,!d.current&&r&&(d.current=r(e,{visualState:t,parent:a,props:n,presenceContext:u,blockInitialAnimation:u?u.initial===!1:!1,reducedMotionConfig:c}));const h=d.current,g=k.useContext(kp);h&&!h.projection&&s&&(h.type==="html"||h.type==="svg")&&K0(d.current,n,s,g);const v=k.useRef(!1);k.useInsertionEffect(()=>{h&&v.current&&h.update(n,u)});const x=n[Sp],P=k.useRef(!!x&&!(!((i=window.MotionHandoffIsComplete)===null||i===void 0)&&i.call(window,x))&&((o=window.MotionHasOptimisedAnimation)===null||o===void 0?void 0:o.call(window,x)));return pp(()=>{h&&(v.current=!0,window.MotionIsMounted=!0,h.updateFeatures(),Zl.render(h.render),P.current&&h.animationState&&h.animationState.animateChanges())}),k.useEffect(()=>{h&&(!P.current&&h.animationState&&h.animationState.animateChanges(),P.current&&(queueMicrotask(()=>{var m;(m=window.MotionHandoffMarkAsComplete)===null||m===void 0||m.call(window,x)}),P.current=!1))}),h}function K0(e,t,n,r){const{layoutId:s,layout:i,drag:o,dragConstraints:a,layoutScroll:l,layoutRoot:u}=t;e.projection=new n(e.latestValues,t["data-framer-portal-id"]?void 0:Pp(e.parent)),e.projection.setOptions({layoutId:s,layout:i,alwaysMeasureLayout:!!o||a&&Dn(a),visualElement:e,animationType:typeof i=="string"?i:"both",initialPromotionConfig:r,layoutScroll:l,layoutRoot:u})}function Pp(e){if(e)return e.options.allowProjection!==!1?e.projection:Pp(e.parent)}function G0({preloadedFeatures:e,createVisualElement:t,useRender:n,useVisualState:r,Component:s}){var i,o;e&&_0(e);function a(u,c){let d;const h={...k.useContext(Kl),...u,layoutId:Q0(u)},{isStatic:g}=h,v=U0(u),x=r(u,g);if(!g&&Gl){X0();const P=Y0(h);d=P.MeasureLayout,v.visualElement=W0(s,x,h,t,P.ProjectionNode)}return f.jsxs(Ki.Provider,{value:v,children:[d&&v.visualElement?f.jsx(d,{visualElement:v.visualElement,...h}):null,n(s,u,$0(x,v.visualElement,c),x,g,v.visualElement)]})}a.displayName=`motion.${typeof s=="string"?s:`create(${(o=(i=s.displayName)!==null&&i!==void 0?i:s.name)!==null&&o!==void 0?o:""})`}`;const l=k.forwardRef(a);return l[b0]=s,l}function Q0({layoutId:e}){const t=k.useContext(Hl).id;return t&&e!==void 0?t+"-"+e:e}function X0(e,t){k.useContext(vp).strict}function Y0(e){const{drag:t,layout:n}=Yn;if(!t&&!n)return{};const r={...t,...n};return{MeasureLayout:t!=null&&t.isEnabled(e)||n!=null&&n.isEnabled(e)?r.MeasureLayout:void 0,ProjectionNode:r.ProjectionNode}}const q0=["animate","circle","defs","desc","ellipse","g","image","line","filter","marker","mask","metadata","path","pattern","polygon","polyline","rect","stop","switch","symbol","svg","text","tspan","use","view"];function Jl(e){return typeof e!="string"||e.includes("-")?!1:!!(q0.indexOf(e)>-1||/[A-Z]/u.test(e))}function Oc(e){const t=[{},{}];return e==null||e.values.forEach((n,r)=>{t[0][r]=n.get(),t[1][r]=n.getVelocity()}),t}function eu(e,t,n,r){if(typeof t=="function"){const[s,i]=Oc(r);t=t(n!==void 0?n:e.custom,s,i)}if(typeof t=="string"&&(t=e.variants&&e.variants[t]),typeof t=="function"){const[s,i]=Oc(r);t=t(n!==void 0?n:e.custom,s,i)}return t}const Ra=e=>Array.isArray(e),Z0=e=>!!(e&&typeof e=="object"&&e.mix&&e.toValue),J0=e=>Ra(e)?e[e.length-1]||0:e,ge=e=>!!(e&&e.getVelocity);function Xs(e){const t=ge(e)?e.get():e;return Z0(t)?t.toValue():t}function ev({scrapeMotionValuesFromProps:e,createRenderState:t,onUpdate:n},r,s,i){const o={latestValues:tv(r,s,i,e),renderState:t()};return n&&(o.onMount=a=>n({props:r,current:a,...o}),o.onUpdate=a=>n(a)),o}const Tp=e=>(t,n)=>{const r=k.useContext(Ki),s=k.useContext(Wi),i=()=>ev(e,t,r,s);return n?i():Wl(i)};function tv(e,t,n,r){const s={},i=r(e,{});for(const h in i)s[h]=Xs(i[h]);let{initial:o,animate:a}=e;const l=Qi(e),u=wp(e);t&&u&&!l&&e.inherit!==!1&&(o===void 0&&(o=t.initial),a===void 0&&(a=t.animate));let c=n?n.initial===!1:!1;c=c||o===!1;const d=c?a:o;if(d&&typeof d!="boolean"&&!Gi(d)){const h=Array.isArray(d)?d:[d];for(let g=0;g<h.length;g++){const v=eu(e,h[g]);if(v){const{transitionEnd:x,transition:P,...m}=v;for(const p in m){let y=m[p];if(Array.isArray(y)){const w=c?y.length-1:0;y=y[w]}y!==null&&(s[p]=y)}for(const p in x)s[p]=x[p]}}}return s}const tr=["transformPerspective","x","y","z","translateX","translateY","translateZ","scale","scaleX","scaleY","rotate","rotateX","rotateY","rotateZ","skew","skewX","skewY"],gn=new Set(tr),Cp=e=>t=>typeof t=="string"&&t.startsWith(e),Ep=Cp("--"),nv=Cp("var(--"),tu=e=>nv(e)?rv.test(e.split("/*")[0].trim()):!1,rv=/var\(--(?:[\w-]+\s*|[\w-]+\s*,(?:\s*[^)(\s]|\s*\((?:[^)(]|\([^)(]*\))*\))+\s*)\)$/iu,jp=(e,t)=>t&&typeof e=="number"?t.transform(e):e,vt=(e,t,n)=>n>t?t:n<e?e:n,nr={test:e=>typeof e=="number",parse:parseFloat,transform:e=>e},Zr={...nr,transform:e=>vt(0,1,e)},_s={...nr,default:1},us=e=>({test:t=>typeof t=="string"&&t.endsWith(e)&&t.split(" ").length===1,parse:parseFloat,transform:t=>`${t}${e}`}),kt=us("deg"),it=us("%"),R=us("px"),sv=us("vh"),iv=us("vw"),zc={...it,parse:e=>it.parse(e)/100,transform:e=>it.transform(e*100)},ov={borderWidth:R,borderTopWidth:R,borderRightWidth:R,borderBottomWidth:R,borderLeftWidth:R,borderRadius:R,radius:R,borderTopLeftRadius:R,borderTopRightRadius:R,borderBottomRightRadius:R,borderBottomLeftRadius:R,width:R,maxWidth:R,height:R,maxHeight:R,top:R,right:R,bottom:R,left:R,padding:R,paddingTop:R,paddingRight:R,paddingBottom:R,paddingLeft:R,margin:R,marginTop:R,marginRight:R,marginBottom:R,marginLeft:R,backgroundPositionX:R,backgroundPositionY:R},av={rotate:kt,rotateX:kt,rotateY:kt,rotateZ:kt,scale:_s,scaleX:_s,scaleY:_s,scaleZ:_s,skew:kt,skewX:kt,skewY:kt,distance:R,translateX:R,translateY:R,translateZ:R,x:R,y:R,z:R,perspective:R,transformPerspective:R,opacity:Zr,originX:zc,originY:zc,originZ:R},Bc={...nr,transform:Math.round},nu={...ov,...av,zIndex:Bc,size:R,fillOpacity:Zr,strokeOpacity:Zr,numOctaves:Bc},lv={x:"translateX",y:"translateY",z:"translateZ",transformPerspective:"perspective"},uv=tr.length;function cv(e,t,n){let r="",s=!0;for(let i=0;i<uv;i++){const o=tr[i],a=e[o];if(a===void 0)continue;let l=!0;if(typeof a=="number"?l=a===(o.startsWith("scale")?1:0):l=parseFloat(a)===0,!l||n){const u=jp(a,nu[o]);if(!l){s=!1;const c=lv[o]||o;r+=`${c}(${u}) `}n&&(t[o]=u)}}return r=r.trim(),n?r=n(t,s?"":r):s&&(r="none"),r}function ru(e,t,n){const{style:r,vars:s,transformOrigin:i}=e;let o=!1,a=!1;for(const l in t){const u=t[l];if(gn.has(l)){o=!0;continue}else if(Ep(l)){s[l]=u;continue}else{const c=jp(u,nu[l]);l.startsWith("origin")?(a=!0,i[l]=c):r[l]=c}}if(t.transform||(o||n?r.transform=cv(t,e.transform,n):r.transform&&(r.transform="none")),a){const{originX:l="50%",originY:u="50%",originZ:c=0}=i;r.transformOrigin=`${l} ${u} ${c}`}}const fv={offset:"stroke-dashoffset",array:"stroke-dasharray"},dv={offset:"strokeDashoffset",array:"strokeDasharray"};function hv(e,t,n=1,r=0,s=!0){e.pathLength=1;const i=s?fv:dv;e[i.offset]=R.transform(-r);const o=R.transform(t),a=R.transform(n);e[i.array]=`${o} ${a}`}function Uc(e,t,n){return typeof e=="string"?e:R.transform(t+n*e)}function pv(e,t,n){const r=Uc(t,e.x,e.width),s=Uc(n,e.y,e.height);return`${r} ${s}`}function su(e,{attrX:t,attrY:n,attrScale:r,originX:s,originY:i,pathLength:o,pathSpacing:a=1,pathOffset:l=0,...u},c,d){if(ru(e,u,d),c){e.style.viewBox&&(e.attrs.viewBox=e.style.viewBox);return}e.attrs=e.style,e.style={};const{attrs:h,style:g,dimensions:v}=e;h.transform&&(v&&(g.transform=h.transform),delete h.transform),v&&(s!==void 0||i!==void 0||g.transform)&&(g.transformOrigin=pv(v,s!==void 0?s:.5,i!==void 0?i:.5)),t!==void 0&&(h.x=t),n!==void 0&&(h.y=n),r!==void 0&&(h.scale=r),o!==void 0&&hv(h,o,a,l,!1)}const iu=()=>({style:{},transform:{},transformOrigin:{},vars:{}}),Np=()=>({...iu(),attrs:{}}),ou=e=>typeof e=="string"&&e.toLowerCase()==="svg";function Ap(e,{style:t,vars:n},r,s){Object.assign(e.style,t,s&&s.getProjectionStyles(r));for(const i in n)e.style.setProperty(i,n[i])}const Mp=new Set(["baseFrequency","diffuseConstant","kernelMatrix","kernelUnitLength","keySplines","keyTimes","limitingConeAngle","markerHeight","markerWidth","numOctaves","targetX","targetY","surfaceScale","specularConstant","specularExponent","stdDeviation","tableValues","viewBox","gradientTransform","pathLength","startOffset","textLength","lengthAdjust"]);function Dp(e,t,n,r){Ap(e,t,void 0,r);for(const s in t.attrs)e.setAttribute(Mp.has(s)?s:ql(s),t.attrs[s])}const Pi={};function mv(e){Object.assign(Pi,e)}function Rp(e,{layout:t,layoutId:n}){return gn.has(e)||e.startsWith("origin")||(t||n!==void 0)&&(!!Pi[e]||e==="opacity")}function au(e,t,n){var r;const{style:s}=e,i={};for(const o in s)(ge(s[o])||t.style&&ge(t.style[o])||Rp(o,e)||((r=n==null?void 0:n.getValue(o))===null||r===void 0?void 0:r.liveStyle)!==void 0)&&(i[o]=s[o]);return i}function Lp(e,t,n){const r=au(e,t,n);for(const s in e)if(ge(e[s])||ge(t[s])){const i=tr.indexOf(s)!==-1?"attr"+s.charAt(0).toUpperCase()+s.substring(1):s;r[i]=e[s]}return r}function yv(e,t){try{t.dimensions=typeof e.getBBox=="function"?e.getBBox():e.getBoundingClientRect()}catch{t.dimensions={x:0,y:0,width:0,height:0}}}const bc=["x","y","width","height","cx","cy","r"],gv={useVisualState:Tp({scrapeMotionValuesFromProps:Lp,createRenderState:Np,onUpdate:({props:e,prevProps:t,current:n,renderState:r,latestValues:s})=>{if(!n)return;let i=!!e.drag;if(!i){for(const a in s)if(gn.has(a)){i=!0;break}}if(!i)return;let o=!t;if(t)for(let a=0;a<bc.length;a++){const l=bc[a];e[l]!==t[l]&&(o=!0)}o&&$.read(()=>{yv(n,r),$.render(()=>{su(r,s,ou(n.tagName),e.transformTemplate),Dp(n,r)})})}})},vv={useVisualState:Tp({scrapeMotionValuesFromProps:au,createRenderState:iu})};function Vp(e,t,n){for(const r in t)!ge(t[r])&&!Rp(r,n)&&(e[r]=t[r])}function xv({transformTemplate:e},t){return k.useMemo(()=>{const n=iu();return ru(n,t,e),Object.assign({},n.vars,n.style)},[t])}function wv(e,t){const n=e.style||{},r={};return Vp(r,n,e),Object.assign(r,xv(e,t)),r}function Sv(e,t){const n={},r=wv(e,t);return e.drag&&e.dragListener!==!1&&(n.draggable=!1,r.userSelect=r.WebkitUserSelect=r.WebkitTouchCallout="none",r.touchAction=e.drag===!0?"none":`pan-${e.drag==="x"?"y":"x"}`),e.tabIndex===void 0&&(e.onTap||e.onTapStart||e.whileTap)&&(n.tabIndex=0),n.style=r,n}function kv(e,t,n,r){const s=k.useMemo(()=>{const i=Np();return su(i,t,ou(r),e.transformTemplate),{...i.attrs,style:{...i.style}}},[t]);if(e.style){const i={};Vp(i,e.style,e),s.style={...i,...s.style}}return s}function Pv(e=!1){return(n,r,s,{latestValues:i},o)=>{const l=(Jl(n)?kv:Sv)(r,i,o,n),u=O0(r,typeof n=="string",e),c=n!==k.Fragment?{...u,...l,ref:s}:{},{children:d}=r,h=k.useMemo(()=>ge(d)?d.get():d,[d]);return k.createElement(n,{...c,children:h})}}function Tv(e,t){return function(r,{forwardMotionProps:s}={forwardMotionProps:!1}){const o={...Jl(r)?gv:vv,preloadedFeatures:e,useRender:Pv(s),createVisualElement:t,Component:r};return G0(o)}}function _p(e,t){if(!Array.isArray(t))return!1;const n=t.length;if(n!==e.length)return!1;for(let r=0;r<n;r++)if(t[r]!==e[r])return!1;return!0}function Xi(e,t,n){const r=e.getProps();return eu(r,t,n!==void 0?n:r.custom,e)}const Cv=Ql(()=>window.ScrollTimeline!==void 0);class Ev{constructor(t){this.stop=()=>this.runAll("stop"),this.animations=t.filter(Boolean)}get finished(){return Promise.all(this.animations.map(t=>"finished"in t?t.finished:t))}getAll(t){return this.animations[0][t]}setAll(t,n){for(let r=0;r<this.animations.length;r++)this.animations[r][t]=n}attachTimeline(t,n){const r=this.animations.map(s=>{if(Cv()&&s.attachTimeline)return s.attachTimeline(t);if(typeof n=="function")return n(s)});return()=>{r.forEach((s,i)=>{s&&s(),this.animations[i].stop()})}}get time(){return this.getAll("time")}set time(t){this.setAll("time",t)}get speed(){return this.getAll("speed")}set speed(t){this.setAll("speed",t)}get startTime(){return this.getAll("startTime")}get duration(){let t=0;for(let n=0;n<this.animations.length;n++)t=Math.max(t,this.animations[n].duration);return t}runAll(t){this.animations.forEach(n=>n[t]())}flatten(){this.runAll("flatten")}play(){this.runAll("play")}pause(){this.runAll("pause")}cancel(){this.runAll("cancel")}complete(){this.runAll("complete")}}class jv extends Ev{then(t,n){return Promise.all(this.animations).then(t).catch(n)}}function lu(e,t){return e?e[t]||e.default||e:void 0}const La=2e4;function Fp(e){let t=0;const n=50;let r=e.next(t);for(;!r.done&&t<La;)t+=n,r=e.next(t);return t>=La?1/0:t}function uu(e){return typeof e=="function"}function $c(e,t){e.timeline=t,e.onfinish=null}const cu=e=>Array.isArray(e)&&typeof e[0]=="number",Nv={linearEasing:void 0};function Av(e,t){const n=Ql(e);return()=>{var r;return(r=Nv[t])!==null&&r!==void 0?r:n()}}const Ti=Av(()=>{try{document.createElement("div").animate({opacity:0},{easing:"linear(0, 1)"})}catch{return!1}return!0},"linearEasing"),Ip=(e,t,n=10)=>{let r="";const s=Math.max(Math.round(t/n),2);for(let i=0;i<s;i++)r+=e(Xn(0,s-1,i))+", ";return`linear(${r.substring(0,r.length-2)})`};function Op(e){return!!(typeof e=="function"&&Ti()||!e||typeof e=="string"&&(e in Va||Ti())||cu(e)||Array.isArray(e)&&e.every(Op))}const vr=([e,t,n,r])=>`cubic-bezier(${e}, ${t}, ${n}, ${r})`,Va={linear:"linear",ease:"ease",easeIn:"ease-in",easeOut:"ease-out",easeInOut:"ease-in-out",circIn:vr([0,.65,.55,1]),circOut:vr([.55,0,1,.45]),backIn:vr([.31,.01,.66,-.59]),backOut:vr([.33,1.53,.69,.99])};function zp(e,t){if(e)return typeof e=="function"&&Ti()?Ip(e,t):cu(e)?vr(e):Array.isArray(e)?e.map(n=>zp(n,t)||Va.easeOut):Va[e]}const Ge={x:!1,y:!1};function Bp(){return Ge.x||Ge.y}function Mv(e,t,n){var r;if(e instanceof Element)return[e];if(typeof e=="string"){let s=document;const i=(r=void 0)!==null&&r!==void 0?r:s.querySelectorAll(e);return i?Array.from(i):[]}return Array.from(e)}function Up(e,t){const n=Mv(e),r=new AbortController,s={passive:!0,...t,signal:r.signal};return[n,s,()=>r.abort()]}function Hc(e){return t=>{t.pointerType==="touch"||Bp()||e(t)}}function Dv(e,t,n={}){const[r,s,i]=Up(e,n),o=Hc(a=>{const{target:l}=a,u=t(a);if(typeof u!="function"||!l)return;const c=Hc(d=>{u(d),l.removeEventListener("pointerleave",c)});l.addEventListener("pointerleave",c,s)});return r.forEach(a=>{a.addEventListener("pointerenter",o,s)}),i}const bp=(e,t)=>t?e===t?!0:bp(e,t.parentElement):!1,fu=e=>e.pointerType==="mouse"?typeof e.button!="number"||e.button<=0:e.isPrimary!==!1,Rv=new Set(["BUTTON","INPUT","SELECT","TEXTAREA","A"]);function Lv(e){return Rv.has(e.tagName)||e.tabIndex!==-1}const xr=new WeakSet;function Wc(e){return t=>{t.key==="Enter"&&e(t)}}function jo(e,t){e.dispatchEvent(new PointerEvent("pointer"+t,{isPrimary:!0,bubbles:!0}))}const Vv=(e,t)=>{const n=e.currentTarget;if(!n)return;const r=Wc(()=>{if(xr.has(n))return;jo(n,"down");const s=Wc(()=>{jo(n,"up")}),i=()=>jo(n,"cancel");n.addEventListener("keyup",s,t),n.addEventListener("blur",i,t)});n.addEventListener("keydown",r,t),n.addEventListener("blur",()=>n.removeEventListener("keydown",r),t)};function Kc(e){return fu(e)&&!Bp()}function _v(e,t,n={}){const[r,s,i]=Up(e,n),o=a=>{const l=a.currentTarget;if(!Kc(a)||xr.has(l))return;xr.add(l);const u=t(a),c=(g,v)=>{window.removeEventListener("pointerup",d),window.removeEventListener("pointercancel",h),!(!Kc(g)||!xr.has(l))&&(xr.delete(l),typeof u=="function"&&u(g,{success:v}))},d=g=>{c(g,n.useGlobalTarget||bp(l,g.target))},h=g=>{c(g,!1)};window.addEventListener("pointerup",d,s),window.addEventListener("pointercancel",h,s)};return r.forEach(a=>{!Lv(a)&&a.getAttribute("tabindex")===null&&(a.tabIndex=0),(n.useGlobalTarget?window:a).addEventListener("pointerdown",o,s),a.addEventListener("focus",u=>Vv(u,s),s)}),i}function Fv(e){return e==="x"||e==="y"?Ge[e]?null:(Ge[e]=!0,()=>{Ge[e]=!1}):Ge.x||Ge.y?null:(Ge.x=Ge.y=!0,()=>{Ge.x=Ge.y=!1})}const $p=new Set(["width","height","top","left","right","bottom",...tr]);let Ys;function Iv(){Ys=void 0}const ot={now:()=>(Ys===void 0&&ot.set(ue.isProcessing||R0.useManualTiming?ue.timestamp:performance.now()),Ys),set:e=>{Ys=e,queueMicrotask(Iv)}};function du(e,t){e.indexOf(t)===-1&&e.push(t)}function hu(e,t){const n=e.indexOf(t);n>-1&&e.splice(n,1)}class pu{constructor(){this.subscriptions=[]}add(t){return du(this.subscriptions,t),()=>hu(this.subscriptions,t)}notify(t,n,r){const s=this.subscriptions.length;if(s)if(s===1)this.subscriptions[0](t,n,r);else for(let i=0;i<s;i++){const o=this.subscriptions[i];o&&o(t,n,r)}}getSize(){return this.subscriptions.length}clear(){this.subscriptions.length=0}}function Hp(e,t){return t?e*(1e3/t):0}const Gc=30,Ov=e=>!isNaN(parseFloat(e));class zv{constructor(t,n={}){this.version="11.18.2",this.canTrackVelocity=null,this.events={},this.updateAndNotify=(r,s=!0)=>{const i=ot.now();this.updatedAt!==i&&this.setPrevFrameValue(),this.prev=this.current,this.setCurrent(r),this.current!==this.prev&&this.events.change&&this.events.change.notify(this.current),s&&this.events.renderRequest&&this.events.renderRequest.notify(this.current)},this.hasAnimated=!1,this.setCurrent(t),this.owner=n.owner}setCurrent(t){this.current=t,this.updatedAt=ot.now(),this.canTrackVelocity===null&&t!==void 0&&(this.canTrackVelocity=Ov(this.current))}setPrevFrameValue(t=this.current){this.prevFrameValue=t,this.prevUpdatedAt=this.updatedAt}onChange(t){return this.on("change",t)}on(t,n){this.events[t]||(this.events[t]=new pu);const r=this.events[t].add(n);return t==="change"?()=>{r(),$.read(()=>{this.events.change.getSize()||this.stop()})}:r}clearListeners(){for(const t in this.events)this.events[t].clear()}attach(t,n){this.passiveEffect=t,this.stopPassiveEffect=n}set(t,n=!0){!n||!this.passiveEffect?this.updateAndNotify(t,n):this.passiveEffect(t,this.updateAndNotify)}setWithVelocity(t,n,r){this.set(n),this.prev=void 0,this.prevFrameValue=t,this.prevUpdatedAt=this.updatedAt-r}jump(t,n=!0){this.updateAndNotify(t),this.prev=t,this.prevUpdatedAt=this.prevFrameValue=void 0,n&&this.stop(),this.stopPassiveEffect&&this.stopPassiveEffect()}get(){return this.current}getPrevious(){return this.prev}getVelocity(){const t=ot.now();if(!this.canTrackVelocity||this.prevFrameValue===void 0||t-this.updatedAt>Gc)return 0;const n=Math.min(this.updatedAt-this.prevUpdatedAt,Gc);return Hp(parseFloat(this.current)-parseFloat(this.prevFrameValue),n)}start(t){return this.stop(),new Promise(n=>{this.hasAnimated=!0,this.animation=t(n),this.events.animationStart&&this.events.animationStart.notify()}).then(()=>{this.events.animationComplete&&this.events.animationComplete.notify(),this.clearAnimation()})}stop(){this.animation&&(this.animation.stop(),this.events.animationCancel&&this.events.animationCancel.notify()),this.clearAnimation()}isAnimating(){return!!this.animation}clearAnimation(){delete this.animation}destroy(){this.clearListeners(),this.stop(),this.stopPassiveEffect&&this.stopPassiveEffect()}}function Jr(e,t){return new zv(e,t)}function Bv(e,t,n){e.hasValue(t)?e.getValue(t).set(n):e.addValue(t,Jr(n))}function Uv(e,t){const n=Xi(e,t);let{transitionEnd:r={},transition:s={},...i}=n||{};i={...i,...r};for(const o in i){const a=J0(i[o]);Bv(e,o,a)}}function bv(e){return!!(ge(e)&&e.add)}function _a(e,t){const n=e.getValue("willChange");if(bv(n))return n.add(t)}function Wp(e){return e.props[Sp]}const Kp=(e,t,n)=>(((1-3*n+3*t)*e+(3*n-6*t))*e+3*t)*e,$v=1e-7,Hv=12;function Wv(e,t,n,r,s){let i,o,a=0;do o=t+(n-t)/2,i=Kp(o,r,s)-e,i>0?n=o:t=o;while(Math.abs(i)>$v&&++a<Hv);return o}function cs(e,t,n,r){if(e===t&&n===r)return Re;const s=i=>Wv(i,0,1,e,n);return i=>i===0||i===1?i:Kp(s(i),t,r)}const Gp=e=>t=>t<=.5?e(2*t)/2:(2-e(2*(1-t)))/2,Qp=e=>t=>1-e(1-t),Xp=cs(.33,1.53,.69,.99),mu=Qp(Xp),Yp=Gp(mu),qp=e=>(e*=2)<1?.5*mu(e):.5*(2-Math.pow(2,-10*(e-1))),yu=e=>1-Math.sin(Math.acos(e)),Zp=Qp(yu),Jp=Gp(yu),em=e=>/^0[^.\s]+$/u.test(e);function Kv(e){return typeof e=="number"?e===0:e!==null?e==="none"||e==="0"||em(e):!0}const Mr=e=>Math.round(e*1e5)/1e5,gu=/-?(?:\d+(?:\.\d+)?|\.\d+)/gu;function Gv(e){return e==null}const Qv=/^(?:#[\da-f]{3,8}|(?:rgb|hsl)a?\((?:-?[\d.]+%?[,\s]+){2}-?[\d.]+%?\s*(?:[,/]\s*)?(?:\b\d+(?:\.\d+)?|\.\d+)?%?\))$/iu,vu=(e,t)=>n=>!!(typeof n=="string"&&Qv.test(n)&&n.startsWith(e)||t&&!Gv(n)&&Object.prototype.hasOwnProperty.call(n,t)),tm=(e,t,n)=>r=>{if(typeof r!="string")return r;const[s,i,o,a]=r.match(gu);return{[e]:parseFloat(s),[t]:parseFloat(i),[n]:parseFloat(o),alpha:a!==void 0?parseFloat(a):1}},Xv=e=>vt(0,255,e),No={...nr,transform:e=>Math.round(Xv(e))},sn={test:vu("rgb","red"),parse:tm("red","green","blue"),transform:({red:e,green:t,blue:n,alpha:r=1})=>"rgba("+No.transform(e)+", "+No.transform(t)+", "+No.transform(n)+", "+Mr(Zr.transform(r))+")"};function Yv(e){let t="",n="",r="",s="";return e.length>5?(t=e.substring(1,3),n=e.substring(3,5),r=e.substring(5,7),s=e.substring(7,9)):(t=e.substring(1,2),n=e.substring(2,3),r=e.substring(3,4),s=e.substring(4,5),t+=t,n+=n,r+=r,s+=s),{red:parseInt(t,16),green:parseInt(n,16),blue:parseInt(r,16),alpha:s?parseInt(s,16)/255:1}}const Fa={test:vu("#"),parse:Yv,transform:sn.transform},Rn={test:vu("hsl","hue"),parse:tm("hue","saturation","lightness"),transform:({hue:e,saturation:t,lightness:n,alpha:r=1})=>"hsla("+Math.round(e)+", "+it.transform(Mr(t))+", "+it.transform(Mr(n))+", "+Mr(Zr.transform(r))+")"},me={test:e=>sn.test(e)||Fa.test(e)||Rn.test(e),parse:e=>sn.test(e)?sn.parse(e):Rn.test(e)?Rn.parse(e):Fa.parse(e),transform:e=>typeof e=="string"?e:e.hasOwnProperty("red")?sn.transform(e):Rn.transform(e)},qv=/(?:#[\da-f]{3,8}|(?:rgb|hsl)a?\((?:-?[\d.]+%?[,\s]+){2}-?[\d.]+%?\s*(?:[,/]\s*)?(?:\b\d+(?:\.\d+)?|\.\d+)?%?\))/giu;function Zv(e){var t,n;return isNaN(e)&&typeof e=="string"&&(((t=e.match(gu))===null||t===void 0?void 0:t.length)||0)+(((n=e.match(qv))===null||n===void 0?void 0:n.length)||0)>0}const nm="number",rm="color",Jv="var",e1="var(",Qc="${}",t1=/var\s*\(\s*--(?:[\w-]+\s*|[\w-]+\s*,(?:\s*[^)(\s]|\s*\((?:[^)(]|\([^)(]*\))*\))+\s*)\)|#[\da-f]{3,8}|(?:rgb|hsl)a?\((?:-?[\d.]+%?[,\s]+){2}-?[\d.]+%?\s*(?:[,/]\s*)?(?:\b\d+(?:\.\d+)?|\.\d+)?%?\)|-?(?:\d+(?:\.\d+)?|\.\d+)/giu;function es(e){const t=e.toString(),n=[],r={color:[],number:[],var:[]},s=[];let i=0;const a=t.replace(t1,l=>(me.test(l)?(r.color.push(i),s.push(rm),n.push(me.parse(l))):l.startsWith(e1)?(r.var.push(i),s.push(Jv),n.push(l)):(r.number.push(i),s.push(nm),n.push(parseFloat(l))),++i,Qc)).split(Qc);return{values:n,split:a,indexes:r,types:s}}function sm(e){return es(e).values}function im(e){const{split:t,types:n}=es(e),r=t.length;return s=>{let i="";for(let o=0;o<r;o++)if(i+=t[o],s[o]!==void 0){const a=n[o];a===nm?i+=Mr(s[o]):a===rm?i+=me.transform(s[o]):i+=s[o]}return i}}const n1=e=>typeof e=="number"?0:e;function r1(e){const t=sm(e);return im(e)(t.map(n1))}const Bt={test:Zv,parse:sm,createTransformer:im,getAnimatableNone:r1},s1=new Set(["brightness","contrast","saturate","opacity"]);function i1(e){const[t,n]=e.slice(0,-1).split("(");if(t==="drop-shadow")return e;const[r]=n.match(gu)||[];if(!r)return e;const s=n.replace(r,"");let i=s1.has(t)?1:0;return r!==n&&(i*=100),t+"("+i+s+")"}const o1=/\b([a-z-]*)\(.*?\)/gu,Ia={...Bt,getAnimatableNone:e=>{const t=e.match(o1);return t?t.map(i1).join(" "):e}},a1={...nu,color:me,backgroundColor:me,outlineColor:me,fill:me,stroke:me,borderColor:me,borderTopColor:me,borderRightColor:me,borderBottomColor:me,borderLeftColor:me,filter:Ia,WebkitFilter:Ia},xu=e=>a1[e];function om(e,t){let n=xu(e);return n!==Ia&&(n=Bt),n.getAnimatableNone?n.getAnimatableNone(t):void 0}const l1=new Set(["auto","none","0"]);function u1(e,t,n){let r=0,s;for(;r<e.length&&!s;){const i=e[r];typeof i=="string"&&!l1.has(i)&&es(i).values.length&&(s=e[r]),r++}if(s&&n)for(const i of t)e[i]=om(n,s)}const Xc=e=>e===nr||e===R,Yc=(e,t)=>parseFloat(e.split(", ")[t]),qc=(e,t)=>(n,{transform:r})=>{if(r==="none"||!r)return 0;const s=r.match(/^matrix3d\((.+)\)$/u);if(s)return Yc(s[1],t);{const i=r.match(/^matrix\((.+)\)$/u);return i?Yc(i[1],e):0}},c1=new Set(["x","y","z"]),f1=tr.filter(e=>!c1.has(e));function d1(e){const t=[];return f1.forEach(n=>{const r=e.getValue(n);r!==void 0&&(t.push([n,r.get()]),r.set(n.startsWith("scale")?1:0))}),t}const qn={width:({x:e},{paddingLeft:t="0",paddingRight:n="0"})=>e.max-e.min-parseFloat(t)-parseFloat(n),height:({y:e},{paddingTop:t="0",paddingBottom:n="0"})=>e.max-e.min-parseFloat(t)-parseFloat(n),top:(e,{top:t})=>parseFloat(t),left:(e,{left:t})=>parseFloat(t),bottom:({y:e},{top:t})=>parseFloat(t)+(e.max-e.min),right:({x:e},{left:t})=>parseFloat(t)+(e.max-e.min),x:qc(4,13),y:qc(5,14)};qn.translateX=qn.x;qn.translateY=qn.y;const ln=new Set;let Oa=!1,za=!1;function am(){if(za){const e=Array.from(ln).filter(r=>r.needsMeasurement),t=new Set(e.map(r=>r.element)),n=new Map;t.forEach(r=>{const s=d1(r);s.length&&(n.set(r,s),r.render())}),e.forEach(r=>r.measureInitialState()),t.forEach(r=>{r.render();const s=n.get(r);s&&s.forEach(([i,o])=>{var a;(a=r.getValue(i))===null||a===void 0||a.set(o)})}),e.forEach(r=>r.measureEndState()),e.forEach(r=>{r.suspendedScrollY!==void 0&&window.scrollTo(0,r.suspendedScrollY)})}za=!1,Oa=!1,ln.forEach(e=>e.complete()),ln.clear()}function lm(){ln.forEach(e=>{e.readKeyframes(),e.needsMeasurement&&(za=!0)})}function h1(){lm(),am()}class wu{constructor(t,n,r,s,i,o=!1){this.isComplete=!1,this.isAsync=!1,this.needsMeasurement=!1,this.isScheduled=!1,this.unresolvedKeyframes=[...t],this.onComplete=n,this.name=r,this.motionValue=s,this.element=i,this.isAsync=o}scheduleResolve(){this.isScheduled=!0,this.isAsync?(ln.add(this),Oa||(Oa=!0,$.read(lm),$.resolveKeyframes(am))):(this.readKeyframes(),this.complete())}readKeyframes(){const{unresolvedKeyframes:t,name:n,element:r,motionValue:s}=this;for(let i=0;i<t.length;i++)if(t[i]===null)if(i===0){const o=s==null?void 0:s.get(),a=t[t.length-1];if(o!==void 0)t[0]=o;else if(r&&n){const l=r.readValue(n,a);l!=null&&(t[0]=l)}t[0]===void 0&&(t[0]=a),s&&o===void 0&&s.set(t[0])}else t[i]=t[i-1]}setFinalKeyframe(){}measureInitialState(){}renderEndStyles(){}measureEndState(){}complete(){this.isComplete=!0,this.onComplete(this.unresolvedKeyframes,this.finalKeyframe),ln.delete(this)}cancel(){this.isComplete||(this.isScheduled=!1,ln.delete(this))}resume(){this.isComplete||this.scheduleResolve()}}const um=e=>/^-?(?:\d+(?:\.\d+)?|\.\d+)$/u.test(e),p1=/^var\(--(?:([\w-]+)|([\w-]+), ?([a-zA-Z\d ()%#.,-]+))\)/u;function m1(e){const t=p1.exec(e);if(!t)return[,];const[,n,r,s]=t;return[`--${n??r}`,s]}function cm(e,t,n=1){const[r,s]=m1(e);if(!r)return;const i=window.getComputedStyle(t).getPropertyValue(r);if(i){const o=i.trim();return um(o)?parseFloat(o):o}return tu(s)?cm(s,t,n+1):s}const fm=e=>t=>t.test(e),y1={test:e=>e==="auto",parse:e=>e},dm=[nr,R,it,kt,iv,sv,y1],Zc=e=>dm.find(fm(e));class hm extends wu{constructor(t,n,r,s,i){super(t,n,r,s,i,!0)}readKeyframes(){const{unresolvedKeyframes:t,element:n,name:r}=this;if(!n||!n.current)return;super.readKeyframes();for(let l=0;l<t.length;l++){let u=t[l];if(typeof u=="string"&&(u=u.trim(),tu(u))){const c=cm(u,n.current);c!==void 0&&(t[l]=c),l===t.length-1&&(this.finalKeyframe=u)}}if(this.resolveNoneKeyframes(),!$p.has(r)||t.length!==2)return;const[s,i]=t,o=Zc(s),a=Zc(i);if(o!==a)if(Xc(o)&&Xc(a))for(let l=0;l<t.length;l++){const u=t[l];typeof u=="string"&&(t[l]=parseFloat(u))}else this.needsMeasurement=!0}resolveNoneKeyframes(){const{unresolvedKeyframes:t,name:n}=this,r=[];for(let s=0;s<t.length;s++)Kv(t[s])&&r.push(s);r.length&&u1(t,r,n)}measureInitialState(){const{element:t,unresolvedKeyframes:n,name:r}=this;if(!t||!t.current)return;r==="height"&&(this.suspendedScrollY=window.pageYOffset),this.measuredOrigin=qn[r](t.measureViewportBox(),window.getComputedStyle(t.current)),n[0]=this.measuredOrigin;const s=n[n.length-1];s!==void 0&&t.getValue(r,s).jump(s,!1)}measureEndState(){var t;const{element:n,name:r,unresolvedKeyframes:s}=this;if(!n||!n.current)return;const i=n.getValue(r);i&&i.jump(this.measuredOrigin,!1);const o=s.length-1,a=s[o];s[o]=qn[r](n.measureViewportBox(),window.getComputedStyle(n.current)),a!==null&&this.finalKeyframe===void 0&&(this.finalKeyframe=a),!((t=this.removedTransforms)===null||t===void 0)&&t.length&&this.removedTransforms.forEach(([l,u])=>{n.getValue(l).set(u)}),this.resolveNoneKeyframes()}}const Jc=(e,t)=>t==="zIndex"?!1:!!(typeof e=="number"||Array.isArray(e)||typeof e=="string"&&(Bt.test(e)||e==="0")&&!e.startsWith("url("));function g1(e){const t=e[0];if(e.length===1)return!0;for(let n=0;n<e.length;n++)if(e[n]!==t)return!0}function v1(e,t,n,r){const s=e[0];if(s===null)return!1;if(t==="display"||t==="visibility")return!0;const i=e[e.length-1],o=Jc(s,t),a=Jc(i,t);return!o||!a?!1:g1(e)||(n==="spring"||uu(n))&&r}const x1=e=>e!==null;function Yi(e,{repeat:t,repeatType:n="loop"},r){const s=e.filter(x1),i=t&&n!=="loop"&&t%2===1?0:s.length-1;return!i||r===void 0?s[i]:r}const w1=40;class pm{constructor({autoplay:t=!0,delay:n=0,type:r="keyframes",repeat:s=0,repeatDelay:i=0,repeatType:o="loop",...a}){this.isStopped=!1,this.hasAttemptedResolve=!1,this.createdAt=ot.now(),this.options={autoplay:t,delay:n,type:r,repeat:s,repeatDelay:i,repeatType:o,...a},this.updateFinishedPromise()}calcStartTime(){return this.resolvedAt?this.resolvedAt-this.createdAt>w1?this.resolvedAt:this.createdAt:this.createdAt}get resolved(){return!this._resolved&&!this.hasAttemptedResolve&&h1(),this._resolved}onKeyframesResolved(t,n){this.resolvedAt=ot.now(),this.hasAttemptedResolve=!0;const{name:r,type:s,velocity:i,delay:o,onComplete:a,onUpdate:l,isGenerator:u}=this.options;if(!u&&!v1(t,r,s,i))if(o)this.options.duration=0;else{l&&l(Yi(t,this.options,n)),a&&a(),this.resolveFinishedPromise();return}const c=this.initPlayback(t,n);c!==!1&&(this._resolved={keyframes:t,finalKeyframe:n,...c},this.onPostResolved())}onPostResolved(){}then(t,n){return this.currentFinishedPromise.then(t,n)}flatten(){this.options.type="keyframes",this.options.ease="linear"}updateFinishedPromise(){this.currentFinishedPromise=new Promise(t=>{this.resolveFinishedPromise=t})}}const G=(e,t,n)=>e+(t-e)*n;function Ao(e,t,n){return n<0&&(n+=1),n>1&&(n-=1),n<1/6?e+(t-e)*6*n:n<1/2?t:n<2/3?e+(t-e)*(2/3-n)*6:e}function S1({hue:e,saturation:t,lightness:n,alpha:r}){e/=360,t/=100,n/=100;let s=0,i=0,o=0;if(!t)s=i=o=n;else{const a=n<.5?n*(1+t):n+t-n*t,l=2*n-a;s=Ao(l,a,e+1/3),i=Ao(l,a,e),o=Ao(l,a,e-1/3)}return{red:Math.round(s*255),green:Math.round(i*255),blue:Math.round(o*255),alpha:r}}function Ci(e,t){return n=>n>0?t:e}const Mo=(e,t,n)=>{const r=e*e,s=n*(t*t-r)+r;return s<0?0:Math.sqrt(s)},k1=[Fa,sn,Rn],P1=e=>k1.find(t=>t.test(e));function ef(e){const t=P1(e);if(!t)return!1;let n=t.parse(e);return t===Rn&&(n=S1(n)),n}const tf=(e,t)=>{const n=ef(e),r=ef(t);if(!n||!r)return Ci(e,t);const s={...n};return i=>(s.red=Mo(n.red,r.red,i),s.green=Mo(n.green,r.green,i),s.blue=Mo(n.blue,r.blue,i),s.alpha=G(n.alpha,r.alpha,i),sn.transform(s))},T1=(e,t)=>n=>t(e(n)),fs=(...e)=>e.reduce(T1),Ba=new Set(["none","hidden"]);function C1(e,t){return Ba.has(e)?n=>n<=0?e:t:n=>n>=1?t:e}function E1(e,t){return n=>G(e,t,n)}function Su(e){return typeof e=="number"?E1:typeof e=="string"?tu(e)?Ci:me.test(e)?tf:A1:Array.isArray(e)?mm:typeof e=="object"?me.test(e)?tf:j1:Ci}function mm(e,t){const n=[...e],r=n.length,s=e.map((i,o)=>Su(i)(i,t[o]));return i=>{for(let o=0;o<r;o++)n[o]=s[o](i);return n}}function j1(e,t){const n={...e,...t},r={};for(const s in n)e[s]!==void 0&&t[s]!==void 0&&(r[s]=Su(e[s])(e[s],t[s]));return s=>{for(const i in r)n[i]=r[i](s);return n}}function N1(e,t){var n;const r=[],s={color:0,var:0,number:0};for(let i=0;i<t.values.length;i++){const o=t.types[i],a=e.indexes[o][s[o]],l=(n=e.values[a])!==null&&n!==void 0?n:0;r[i]=l,s[o]++}return r}const A1=(e,t)=>{const n=Bt.createTransformer(t),r=es(e),s=es(t);return r.indexes.var.length===s.indexes.var.length&&r.indexes.color.length===s.indexes.color.length&&r.indexes.number.length>=s.indexes.number.length?Ba.has(e)&&!s.values.length||Ba.has(t)&&!r.values.length?C1(e,t):fs(mm(N1(r,s),s.values),n):Ci(e,t)};function ym(e,t,n){return typeof e=="number"&&typeof t=="number"&&typeof n=="number"?G(e,t,n):Su(e)(e,t)}const M1=5;function gm(e,t,n){const r=Math.max(t-M1,0);return Hp(n-e(r),t-r)}const Y={stiffness:100,damping:10,mass:1,velocity:0,duration:800,bounce:.3,visualDuration:.3,restSpeed:{granular:.01,default:2},restDelta:{granular:.005,default:.5},minDuration:.01,maxDuration:10,minDamping:.05,maxDamping:1},Do=.001;function D1({duration:e=Y.duration,bounce:t=Y.bounce,velocity:n=Y.velocity,mass:r=Y.mass}){let s,i,o=1-t;o=vt(Y.minDamping,Y.maxDamping,o),e=vt(Y.minDuration,Y.maxDuration,ht(e)),o<1?(s=u=>{const c=u*o,d=c*e,h=c-n,g=Ua(u,o),v=Math.exp(-d);return Do-h/g*v},i=u=>{const d=u*o*e,h=d*n+n,g=Math.pow(o,2)*Math.pow(u,2)*e,v=Math.exp(-d),x=Ua(Math.pow(u,2),o);return(-s(u)+Do>0?-1:1)*((h-g)*v)/x}):(s=u=>{const c=Math.exp(-u*e),d=(u-n)*e+1;return-Do+c*d},i=u=>{const c=Math.exp(-u*e),d=(n-u)*(e*e);return c*d});const a=5/e,l=L1(s,i,a);if(e=dt(e),isNaN(l))return{stiffness:Y.stiffness,damping:Y.damping,duration:e};{const u=Math.pow(l,2)*r;return{stiffness:u,damping:o*2*Math.sqrt(r*u),duration:e}}}const R1=12;function L1(e,t,n){let r=n;for(let s=1;s<R1;s++)r=r-e(r)/t(r);return r}function Ua(e,t){return e*Math.sqrt(1-t*t)}const V1=["duration","bounce"],_1=["stiffness","damping","mass"];function nf(e,t){return t.some(n=>e[n]!==void 0)}function F1(e){let t={velocity:Y.velocity,stiffness:Y.stiffness,damping:Y.damping,mass:Y.mass,isResolvedFromDuration:!1,...e};if(!nf(e,_1)&&nf(e,V1))if(e.visualDuration){const n=e.visualDuration,r=2*Math.PI/(n*1.2),s=r*r,i=2*vt(.05,1,1-(e.bounce||0))*Math.sqrt(s);t={...t,mass:Y.mass,stiffness:s,damping:i}}else{const n=D1(e);t={...t,...n,mass:Y.mass},t.isResolvedFromDuration=!0}return t}function vm(e=Y.visualDuration,t=Y.bounce){const n=typeof e!="object"?{visualDuration:e,keyframes:[0,1],bounce:t}:e;let{restSpeed:r,restDelta:s}=n;const i=n.keyframes[0],o=n.keyframes[n.keyframes.length-1],a={done:!1,value:i},{stiffness:l,damping:u,mass:c,duration:d,velocity:h,isResolvedFromDuration:g}=F1({...n,velocity:-ht(n.velocity||0)}),v=h||0,x=u/(2*Math.sqrt(l*c)),P=o-i,m=ht(Math.sqrt(l/c)),p=Math.abs(P)<5;r||(r=p?Y.restSpeed.granular:Y.restSpeed.default),s||(s=p?Y.restDelta.granular:Y.restDelta.default);let y;if(x<1){const S=Ua(m,x);y=C=>{const j=Math.exp(-x*m*C);return o-j*((v+x*m*P)/S*Math.sin(S*C)+P*Math.cos(S*C))}}else if(x===1)y=S=>o-Math.exp(-m*S)*(P+(v+m*P)*S);else{const S=m*Math.sqrt(x*x-1);y=C=>{const j=Math.exp(-x*m*C),T=Math.min(S*C,300);return o-j*((v+x*m*P)*Math.sinh(T)+S*P*Math.cosh(T))/S}}const w={calculatedDuration:g&&d||null,next:S=>{const C=y(S);if(g)a.done=S>=d;else{let j=0;x<1&&(j=S===0?dt(v):gm(y,S,C));const T=Math.abs(j)<=r,D=Math.abs(o-C)<=s;a.done=T&&D}return a.value=a.done?o:C,a},toString:()=>{const S=Math.min(Fp(w),La),C=Ip(j=>w.next(S*j).value,S,30);return S+"ms "+C}};return w}function rf({keyframes:e,velocity:t=0,power:n=.8,timeConstant:r=325,bounceDamping:s=10,bounceStiffness:i=500,modifyTarget:o,min:a,max:l,restDelta:u=.5,restSpeed:c}){const d=e[0],h={done:!1,value:d},g=T=>a!==void 0&&T<a||l!==void 0&&T>l,v=T=>a===void 0?l:l===void 0||Math.abs(a-T)<Math.abs(l-T)?a:l;let x=n*t;const P=d+x,m=o===void 0?P:o(P);m!==P&&(x=m-d);const p=T=>-x*Math.exp(-T/r),y=T=>m+p(T),w=T=>{const D=p(T),A=y(T);h.done=Math.abs(D)<=u,h.value=h.done?m:A};let S,C;const j=T=>{g(h.value)&&(S=T,C=vm({keyframes:[h.value,v(h.value)],velocity:gm(y,T,h.value),damping:s,stiffness:i,restDelta:u,restSpeed:c}))};return j(0),{calculatedDuration:null,next:T=>{let D=!1;return!C&&S===void 0&&(D=!0,w(T),j(T)),S!==void 0&&T>=S?C.next(T-S):(!D&&w(T),h)}}}const I1=cs(.42,0,1,1),O1=cs(0,0,.58,1),xm=cs(.42,0,.58,1),z1=e=>Array.isArray(e)&&typeof e[0]!="number",B1={linear:Re,easeIn:I1,easeInOut:xm,easeOut:O1,circIn:yu,circInOut:Jp,circOut:Zp,backIn:mu,backInOut:Yp,backOut:Xp,anticipate:qp},sf=e=>{if(cu(e)){yp(e.length===4);const[t,n,r,s]=e;return cs(t,n,r,s)}else if(typeof e=="string")return B1[e];return e};function U1(e,t,n){const r=[],s=n||ym,i=e.length-1;for(let o=0;o<i;o++){let a=s(e[o],e[o+1]);if(t){const l=Array.isArray(t)?t[o]||Re:t;a=fs(l,a)}r.push(a)}return r}function b1(e,t,{clamp:n=!0,ease:r,mixer:s}={}){const i=e.length;if(yp(i===t.length),i===1)return()=>t[0];if(i===2&&t[0]===t[1])return()=>t[1];const o=e[0]===e[1];e[0]>e[i-1]&&(e=[...e].reverse(),t=[...t].reverse());const a=U1(t,r,s),l=a.length,u=c=>{if(o&&c<e[0])return t[0];let d=0;if(l>1)for(;d<e.length-2&&!(c<e[d+1]);d++);const h=Xn(e[d],e[d+1],c);return a[d](h)};return n?c=>u(vt(e[0],e[i-1],c)):u}function $1(e,t){const n=e[e.length-1];for(let r=1;r<=t;r++){const s=Xn(0,t,r);e.push(G(n,1,s))}}function H1(e){const t=[0];return $1(t,e.length-1),t}function W1(e,t){return e.map(n=>n*t)}function K1(e,t){return e.map(()=>t||xm).splice(0,e.length-1)}function Ei({duration:e=300,keyframes:t,times:n,ease:r="easeInOut"}){const s=z1(r)?r.map(sf):sf(r),i={done:!1,value:t[0]},o=W1(n&&n.length===t.length?n:H1(t),e),a=b1(o,t,{ease:Array.isArray(s)?s:K1(t,s)});return{calculatedDuration:e,next:l=>(i.value=a(l),i.done=l>=e,i)}}const G1=e=>{const t=({timestamp:n})=>e(n);return{start:()=>$.update(t,!0),stop:()=>zt(t),now:()=>ue.isProcessing?ue.timestamp:ot.now()}},Q1={decay:rf,inertia:rf,tween:Ei,keyframes:Ei,spring:vm},X1=e=>e/100;class ku extends pm{constructor(t){super(t),this.holdTime=null,this.cancelTime=null,this.currentTime=0,this.playbackSpeed=1,this.pendingPlayState="running",this.startTime=null,this.state="idle",this.stop=()=>{if(this.resolver.cancel(),this.isStopped=!0,this.state==="idle")return;this.teardown();const{onStop:l}=this.options;l&&l()};const{name:n,motionValue:r,element:s,keyframes:i}=this.options,o=(s==null?void 0:s.KeyframeResolver)||wu,a=(l,u)=>this.onKeyframesResolved(l,u);this.resolver=new o(i,a,n,r,s),this.resolver.scheduleResolve()}flatten(){super.flatten(),this._resolved&&Object.assign(this._resolved,this.initPlayback(this._resolved.keyframes))}initPlayback(t){const{type:n="keyframes",repeat:r=0,repeatDelay:s=0,repeatType:i,velocity:o=0}=this.options,a=uu(n)?n:Q1[n]||Ei;let l,u;a!==Ei&&typeof t[0]!="number"&&(l=fs(X1,ym(t[0],t[1])),t=[0,100]);const c=a({...this.options,keyframes:t});i==="mirror"&&(u=a({...this.options,keyframes:[...t].reverse(),velocity:-o})),c.calculatedDuration===null&&(c.calculatedDuration=Fp(c));const{calculatedDuration:d}=c,h=d+s,g=h*(r+1)-s;return{generator:c,mirroredGenerator:u,mapPercentToKeyframes:l,calculatedDuration:d,resolvedDuration:h,totalDuration:g}}onPostResolved(){const{autoplay:t=!0}=this.options;this.play(),this.pendingPlayState==="paused"||!t?this.pause():this.state=this.pendingPlayState}tick(t,n=!1){const{resolved:r}=this;if(!r){const{keyframes:T}=this.options;return{done:!0,value:T[T.length-1]}}const{finalKeyframe:s,generator:i,mirroredGenerator:o,mapPercentToKeyframes:a,keyframes:l,calculatedDuration:u,totalDuration:c,resolvedDuration:d}=r;if(this.startTime===null)return i.next(0);const{delay:h,repeat:g,repeatType:v,repeatDelay:x,onUpdate:P}=this.options;this.speed>0?this.startTime=Math.min(this.startTime,t):this.speed<0&&(this.startTime=Math.min(t-c/this.speed,this.startTime)),n?this.currentTime=t:this.holdTime!==null?this.currentTime=this.holdTime:this.currentTime=Math.round(t-this.startTime)*this.speed;const m=this.currentTime-h*(this.speed>=0?1:-1),p=this.speed>=0?m<0:m>c;this.currentTime=Math.max(m,0),this.state==="finished"&&this.holdTime===null&&(this.currentTime=c);let y=this.currentTime,w=i;if(g){const T=Math.min(this.currentTime,c)/d;let D=Math.floor(T),A=T%1;!A&&T>=1&&(A=1),A===1&&D--,D=Math.min(D,g+1),!!(D%2)&&(v==="reverse"?(A=1-A,x&&(A-=x/d)):v==="mirror"&&(w=o)),y=vt(0,1,A)*d}const S=p?{done:!1,value:l[0]}:w.next(y);a&&(S.value=a(S.value));let{done:C}=S;!p&&u!==null&&(C=this.speed>=0?this.currentTime>=c:this.currentTime<=0);const j=this.holdTime===null&&(this.state==="finished"||this.state==="running"&&C);return j&&s!==void 0&&(S.value=Yi(l,this.options,s)),P&&P(S.value),j&&this.finish(),S}get duration(){const{resolved:t}=this;return t?ht(t.calculatedDuration):0}get time(){return ht(this.currentTime)}set time(t){t=dt(t),this.currentTime=t,this.holdTime!==null||this.speed===0?this.holdTime=t:this.driver&&(this.startTime=this.driver.now()-t/this.speed)}get speed(){return this.playbackSpeed}set speed(t){const n=this.playbackSpeed!==t;this.playbackSpeed=t,n&&(this.time=ht(this.currentTime))}play(){if(this.resolver.isScheduled||this.resolver.resume(),!this._resolved){this.pendingPlayState="running";return}if(this.isStopped)return;const{driver:t=G1,onPlay:n,startTime:r}=this.options;this.driver||(this.driver=t(i=>this.tick(i))),n&&n();const s=this.driver.now();this.holdTime!==null?this.startTime=s-this.holdTime:this.startTime?this.state==="finished"&&(this.startTime=s):this.startTime=r??this.calcStartTime(),this.state==="finished"&&this.updateFinishedPromise(),this.cancelTime=this.startTime,this.holdTime=null,this.state="running",this.driver.start()}pause(){var t;if(!this._resolved){this.pendingPlayState="paused";return}this.state="paused",this.holdTime=(t=this.currentTime)!==null&&t!==void 0?t:0}complete(){this.state!=="running"&&this.play(),this.pendingPlayState=this.state="finished",this.holdTime=null}finish(){this.teardown(),this.state="finished";const{onComplete:t}=this.options;t&&t()}cancel(){this.cancelTime!==null&&this.tick(this.cancelTime),this.teardown(),this.updateFinishedPromise()}teardown(){this.state="idle",this.stopDriver(),this.resolveFinishedPromise(),this.updateFinishedPromise(),this.startTime=this.cancelTime=null,this.resolver.cancel()}stopDriver(){this.driver&&(this.driver.stop(),this.driver=void 0)}sample(t){return this.startTime=0,this.tick(t,!0)}}const Y1=new Set(["opacity","clipPath","filter","transform"]);function q1(e,t,n,{delay:r=0,duration:s=300,repeat:i=0,repeatType:o="loop",ease:a="easeInOut",times:l}={}){const u={[t]:n};l&&(u.offset=l);const c=zp(a,s);return Array.isArray(c)&&(u.easing=c),e.animate(u,{delay:r,duration:s,easing:Array.isArray(c)?"linear":c,fill:"both",iterations:i+1,direction:o==="reverse"?"alternate":"normal"})}const Z1=Ql(()=>Object.hasOwnProperty.call(Element.prototype,"animate")),ji=10,J1=2e4;function ex(e){return uu(e.type)||e.type==="spring"||!Op(e.ease)}function tx(e,t){const n=new ku({...t,keyframes:e,repeat:0,delay:0,isGenerator:!0});let r={done:!1,value:e[0]};const s=[];let i=0;for(;!r.done&&i<J1;)r=n.sample(i),s.push(r.value),i+=ji;return{times:void 0,keyframes:s,duration:i-ji,ease:"linear"}}const wm={anticipate:qp,backInOut:Yp,circInOut:Jp};function nx(e){return e in wm}class of extends pm{constructor(t){super(t);const{name:n,motionValue:r,element:s,keyframes:i}=this.options;this.resolver=new hm(i,(o,a)=>this.onKeyframesResolved(o,a),n,r,s),this.resolver.scheduleResolve()}initPlayback(t,n){let{duration:r=300,times:s,ease:i,type:o,motionValue:a,name:l,startTime:u}=this.options;if(!a.owner||!a.owner.current)return!1;if(typeof i=="string"&&Ti()&&nx(i)&&(i=wm[i]),ex(this.options)){const{onComplete:d,onUpdate:h,motionValue:g,element:v,...x}=this.options,P=tx(t,x);t=P.keyframes,t.length===1&&(t[1]=t[0]),r=P.duration,s=P.times,i=P.ease,o="keyframes"}const c=q1(a.owner.current,l,t,{...this.options,duration:r,times:s,ease:i});return c.startTime=u??this.calcStartTime(),this.pendingTimeline?($c(c,this.pendingTimeline),this.pendingTimeline=void 0):c.onfinish=()=>{const{onComplete:d}=this.options;a.set(Yi(t,this.options,n)),d&&d(),this.cancel(),this.resolveFinishedPromise()},{animation:c,duration:r,times:s,type:o,ease:i,keyframes:t}}get duration(){const{resolved:t}=this;if(!t)return 0;const{duration:n}=t;return ht(n)}get time(){const{resolved:t}=this;if(!t)return 0;const{animation:n}=t;return ht(n.currentTime||0)}set time(t){const{resolved:n}=this;if(!n)return;const{animation:r}=n;r.currentTime=dt(t)}get speed(){const{resolved:t}=this;if(!t)return 1;const{animation:n}=t;return n.playbackRate}set speed(t){const{resolved:n}=this;if(!n)return;const{animation:r}=n;r.playbackRate=t}get state(){const{resolved:t}=this;if(!t)return"idle";const{animation:n}=t;return n.playState}get startTime(){const{resolved:t}=this;if(!t)return null;const{animation:n}=t;return n.startTime}attachTimeline(t){if(!this._resolved)this.pendingTimeline=t;else{const{resolved:n}=this;if(!n)return Re;const{animation:r}=n;$c(r,t)}return Re}play(){if(this.isStopped)return;const{resolved:t}=this;if(!t)return;const{animation:n}=t;n.playState==="finished"&&this.updateFinishedPromise(),n.play()}pause(){const{resolved:t}=this;if(!t)return;const{animation:n}=t;n.pause()}stop(){if(this.resolver.cancel(),this.isStopped=!0,this.state==="idle")return;this.resolveFinishedPromise(),this.updateFinishedPromise();const{resolved:t}=this;if(!t)return;const{animation:n,keyframes:r,duration:s,type:i,ease:o,times:a}=t;if(n.playState==="idle"||n.playState==="finished")return;if(this.time){const{motionValue:u,onUpdate:c,onComplete:d,element:h,...g}=this.options,v=new ku({...g,keyframes:r,duration:s,type:i,ease:o,times:a,isGenerator:!0}),x=dt(this.time);u.setWithVelocity(v.sample(x-ji).value,v.sample(x).value,ji)}const{onStop:l}=this.options;l&&l(),this.cancel()}complete(){const{resolved:t}=this;t&&t.animation.finish()}cancel(){const{resolved:t}=this;t&&t.animation.cancel()}static supports(t){const{motionValue:n,name:r,repeatDelay:s,repeatType:i,damping:o,type:a}=t;if(!n||!n.owner||!(n.owner.current instanceof HTMLElement))return!1;const{onUpdate:l,transformTemplate:u}=n.owner.getProps();return Z1()&&r&&Y1.has(r)&&!l&&!u&&!s&&i!=="mirror"&&o!==0&&a!=="inertia"}}const rx={type:"spring",stiffness:500,damping:25,restSpeed:10},sx=e=>({type:"spring",stiffness:550,damping:e===0?2*Math.sqrt(550):30,restSpeed:10}),ix={type:"keyframes",duration:.8},ox={type:"keyframes",ease:[.25,.1,.35,1],duration:.3},ax=(e,{keyframes:t})=>t.length>2?ix:gn.has(e)?e.startsWith("scale")?sx(t[1]):rx:ox;function lx({when:e,delay:t,delayChildren:n,staggerChildren:r,staggerDirection:s,repeat:i,repeatType:o,repeatDelay:a,from:l,elapsed:u,...c}){return!!Object.keys(c).length}const Pu=(e,t,n,r={},s,i)=>o=>{const a=lu(r,e)||{},l=a.delay||r.delay||0;let{elapsed:u=0}=r;u=u-dt(l);let c={keyframes:Array.isArray(n)?n:[null,n],ease:"easeOut",velocity:t.getVelocity(),...a,delay:-u,onUpdate:h=>{t.set(h),a.onUpdate&&a.onUpdate(h)},onComplete:()=>{o(),a.onComplete&&a.onComplete()},name:e,motionValue:t,element:i?void 0:s};lx(a)||(c={...c,...ax(e,c)}),c.duration&&(c.duration=dt(c.duration)),c.repeatDelay&&(c.repeatDelay=dt(c.repeatDelay)),c.from!==void 0&&(c.keyframes[0]=c.from);let d=!1;if((c.type===!1||c.duration===0&&!c.repeatDelay)&&(c.duration=0,c.delay===0&&(d=!0)),d&&!i&&t.get()!==void 0){const h=Yi(c.keyframes,a);if(h!==void 0)return $.update(()=>{c.onUpdate(h),c.onComplete()}),new jv([])}return!i&&of.supports(c)?new of(c):new ku(c)};function ux({protectedKeys:e,needsAnimating:t},n){const r=e.hasOwnProperty(n)&&t[n]!==!0;return t[n]=!1,r}function Sm(e,t,{delay:n=0,transitionOverride:r,type:s}={}){var i;let{transition:o=e.getDefaultTransition(),transitionEnd:a,...l}=t;r&&(o=r);const u=[],c=s&&e.animationState&&e.animationState.getState()[s];for(const d in l){const h=e.getValue(d,(i=e.latestValues[d])!==null&&i!==void 0?i:null),g=l[d];if(g===void 0||c&&ux(c,d))continue;const v={delay:n,...lu(o||{},d)};let x=!1;if(window.MotionHandoffAnimation){const m=Wp(e);if(m){const p=window.MotionHandoffAnimation(m,d,$);p!==null&&(v.startTime=p,x=!0)}}_a(e,d),h.start(Pu(d,h,g,e.shouldReduceMotion&&$p.has(d)?{type:!1}:v,e,x));const P=h.animation;P&&u.push(P)}return a&&Promise.all(u).then(()=>{$.update(()=>{a&&Uv(e,a)})}),u}function ba(e,t,n={}){var r;const s=Xi(e,t,n.type==="exit"?(r=e.presenceContext)===null||r===void 0?void 0:r.custom:void 0);let{transition:i=e.getDefaultTransition()||{}}=s||{};n.transitionOverride&&(i=n.transitionOverride);const o=s?()=>Promise.all(Sm(e,s,n)):()=>Promise.resolve(),a=e.variantChildren&&e.variantChildren.size?(u=0)=>{const{delayChildren:c=0,staggerChildren:d,staggerDirection:h}=i;return cx(e,t,c+u,d,h,n)}:()=>Promise.resolve(),{when:l}=i;if(l){const[u,c]=l==="beforeChildren"?[o,a]:[a,o];return u().then(()=>c())}else return Promise.all([o(),a(n.delay)])}function cx(e,t,n=0,r=0,s=1,i){const o=[],a=(e.variantChildren.size-1)*r,l=s===1?(u=0)=>u*r:(u=0)=>a-u*r;return Array.from(e.variantChildren).sort(fx).forEach((u,c)=>{u.notify("AnimationStart",t),o.push(ba(u,t,{...i,delay:n+l(c)}).then(()=>u.notify("AnimationComplete",t)))}),Promise.all(o)}function fx(e,t){return e.sortNodePosition(t)}function dx(e,t,n={}){e.notify("AnimationStart",t);let r;if(Array.isArray(t)){const s=t.map(i=>ba(e,i,n));r=Promise.all(s)}else if(typeof t=="string")r=ba(e,t,n);else{const s=typeof t=="function"?Xi(e,t,n.custom):t;r=Promise.all(Sm(e,s,n))}return r.then(()=>{e.notify("AnimationComplete",t)})}const hx=Yl.length;function km(e){if(!e)return;if(!e.isControllingVariants){const n=e.parent?km(e.parent)||{}:{};return e.props.initial!==void 0&&(n.initial=e.props.initial),n}const t={};for(let n=0;n<hx;n++){const r=Yl[n],s=e.props[r];(qr(s)||s===!1)&&(t[r]=s)}return t}const px=[...Xl].reverse(),mx=Xl.length;function yx(e){return t=>Promise.all(t.map(({animation:n,options:r})=>dx(e,n,r)))}function gx(e){let t=yx(e),n=af(),r=!0;const s=l=>(u,c)=>{var d;const h=Xi(e,c,l==="exit"?(d=e.presenceContext)===null||d===void 0?void 0:d.custom:void 0);if(h){const{transition:g,transitionEnd:v,...x}=h;u={...u,...x,...v}}return u};function i(l){t=l(e)}function o(l){const{props:u}=e,c=km(e.parent)||{},d=[],h=new Set;let g={},v=1/0;for(let P=0;P<mx;P++){const m=px[P],p=n[m],y=u[m]!==void 0?u[m]:c[m],w=qr(y),S=m===l?p.isActive:null;S===!1&&(v=P);let C=y===c[m]&&y!==u[m]&&w;if(C&&r&&e.manuallyAnimateOnMount&&(C=!1),p.protectedKeys={...g},!p.isActive&&S===null||!y&&!p.prevProp||Gi(y)||typeof y=="boolean")continue;const j=vx(p.prevProp,y);let T=j||m===l&&p.isActive&&!C&&w||P>v&&w,D=!1;const A=Array.isArray(y)?y:[y];let O=A.reduce(s(m),{});S===!1&&(O={});const{prevResolvedValues:de={}}=p,We={...de,...O},sr=ne=>{T=!0,h.has(ne)&&(D=!0,h.delete(ne)),p.needsAnimating[ne]=!0;const N=e.getValue(ne);N&&(N.liveStyle=!1)};for(const ne in We){const N=O[ne],V=de[ne];if(g.hasOwnProperty(ne))continue;let _=!1;Ra(N)&&Ra(V)?_=!_p(N,V):_=N!==V,_?N!=null?sr(ne):h.add(ne):N!==void 0&&h.has(ne)?sr(ne):p.protectedKeys[ne]=!0}p.prevProp=y,p.prevResolvedValues=O,p.isActive&&(g={...g,...O}),r&&e.blockInitialAnimation&&(T=!1),T&&(!(C&&j)||D)&&d.push(...A.map(ne=>({animation:ne,options:{type:m}})))}if(h.size){const P={};h.forEach(m=>{const p=e.getBaseTarget(m),y=e.getValue(m);y&&(y.liveStyle=!0),P[m]=p??null}),d.push({animation:P})}let x=!!d.length;return r&&(u.initial===!1||u.initial===u.animate)&&!e.manuallyAnimateOnMount&&(x=!1),r=!1,x?t(d):Promise.resolve()}function a(l,u){var c;if(n[l].isActive===u)return Promise.resolve();(c=e.variantChildren)===null||c===void 0||c.forEach(h=>{var g;return(g=h.animationState)===null||g===void 0?void 0:g.setActive(l,u)}),n[l].isActive=u;const d=o(l);for(const h in n)n[h].protectedKeys={};return d}return{animateChanges:o,setActive:a,setAnimateFunction:i,getState:()=>n,reset:()=>{n=af(),r=!0}}}function vx(e,t){return typeof t=="string"?t!==e:Array.isArray(t)?!_p(t,e):!1}function Xt(e=!1){return{isActive:e,protectedKeys:{},needsAnimating:{},prevResolvedValues:{}}}function af(){return{animate:Xt(!0),whileInView:Xt(),whileHover:Xt(),whileTap:Xt(),whileDrag:Xt(),whileFocus:Xt(),exit:Xt()}}class Wt{constructor(t){this.isMounted=!1,this.node=t}update(){}}class xx extends Wt{constructor(t){super(t),t.animationState||(t.animationState=gx(t))}updateAnimationControlsSubscription(){const{animate:t}=this.node.getProps();Gi(t)&&(this.unmountControls=t.subscribe(this.node))}mount(){this.updateAnimationControlsSubscription()}update(){const{animate:t}=this.node.getProps(),{animate:n}=this.node.prevProps||{};t!==n&&this.updateAnimationControlsSubscription()}unmount(){var t;this.node.animationState.reset(),(t=this.unmountControls)===null||t===void 0||t.call(this)}}let wx=0;class Sx extends Wt{constructor(){super(...arguments),this.id=wx++}update(){if(!this.node.presenceContext)return;const{isPresent:t,onExitComplete:n}=this.node.presenceContext,{isPresent:r}=this.node.prevPresenceContext||{};if(!this.node.animationState||t===r)return;const s=this.node.animationState.setActive("exit",!t);n&&!t&&s.then(()=>n(this.id))}mount(){const{register:t}=this.node.presenceContext||{};t&&(this.unmount=t(this.id))}unmount(){}}const kx={animation:{Feature:xx},exit:{Feature:Sx}};function ts(e,t,n,r={passive:!0}){return e.addEventListener(t,n,r),()=>e.removeEventListener(t,n)}function ds(e){return{point:{x:e.pageX,y:e.pageY}}}const Px=e=>t=>fu(t)&&e(t,ds(t));function Dr(e,t,n,r){return ts(e,t,Px(n),r)}const lf=(e,t)=>Math.abs(e-t);function Tx(e,t){const n=lf(e.x,t.x),r=lf(e.y,t.y);return Math.sqrt(n**2+r**2)}class Pm{constructor(t,n,{transformPagePoint:r,contextWindow:s,dragSnapToOrigin:i=!1}={}){if(this.startEvent=null,this.lastMoveEvent=null,this.lastMoveEventInfo=null,this.handlers={},this.contextWindow=window,this.updatePoint=()=>{if(!(this.lastMoveEvent&&this.lastMoveEventInfo))return;const d=Lo(this.lastMoveEventInfo,this.history),h=this.startEvent!==null,g=Tx(d.offset,{x:0,y:0})>=3;if(!h&&!g)return;const{point:v}=d,{timestamp:x}=ue;this.history.push({...v,timestamp:x});const{onStart:P,onMove:m}=this.handlers;h||(P&&P(this.lastMoveEvent,d),this.startEvent=this.lastMoveEvent),m&&m(this.lastMoveEvent,d)},this.handlePointerMove=(d,h)=>{this.lastMoveEvent=d,this.lastMoveEventInfo=Ro(h,this.transformPagePoint),$.update(this.updatePoint,!0)},this.handlePointerUp=(d,h)=>{this.end();const{onEnd:g,onSessionEnd:v,resumeAnimation:x}=this.handlers;if(this.dragSnapToOrigin&&x&&x(),!(this.lastMoveEvent&&this.lastMoveEventInfo))return;const P=Lo(d.type==="pointercancel"?this.lastMoveEventInfo:Ro(h,this.transformPagePoint),this.history);this.startEvent&&g&&g(d,P),v&&v(d,P)},!fu(t))return;this.dragSnapToOrigin=i,this.handlers=n,this.transformPagePoint=r,this.contextWindow=s||window;const o=ds(t),a=Ro(o,this.transformPagePoint),{point:l}=a,{timestamp:u}=ue;this.history=[{...l,timestamp:u}];const{onSessionStart:c}=n;c&&c(t,Lo(a,this.history)),this.removeListeners=fs(Dr(this.contextWindow,"pointermove",this.handlePointerMove),Dr(this.contextWindow,"pointerup",this.handlePointerUp),Dr(this.contextWindow,"pointercancel",this.handlePointerUp))}updateHandlers(t){this.handlers=t}end(){this.removeListeners&&this.removeListeners(),zt(this.updatePoint)}}function Ro(e,t){return t?{point:t(e.point)}:e}function uf(e,t){return{x:e.x-t.x,y:e.y-t.y}}function Lo({point:e},t){return{point:e,delta:uf(e,Tm(t)),offset:uf(e,Cx(t)),velocity:Ex(t,.1)}}function Cx(e){return e[0]}function Tm(e){return e[e.length-1]}function Ex(e,t){if(e.length<2)return{x:0,y:0};let n=e.length-1,r=null;const s=Tm(e);for(;n>=0&&(r=e[n],!(s.timestamp-r.timestamp>dt(t)));)n--;if(!r)return{x:0,y:0};const i=ht(s.timestamp-r.timestamp);if(i===0)return{x:0,y:0};const o={x:(s.x-r.x)/i,y:(s.y-r.y)/i};return o.x===1/0&&(o.x=0),o.y===1/0&&(o.y=0),o}const Cm=1e-4,jx=1-Cm,Nx=1+Cm,Em=.01,Ax=0-Em,Mx=0+Em;function Ve(e){return e.max-e.min}function Dx(e,t,n){return Math.abs(e-t)<=n}function cf(e,t,n,r=.5){e.origin=r,e.originPoint=G(t.min,t.max,e.origin),e.scale=Ve(n)/Ve(t),e.translate=G(n.min,n.max,e.origin)-e.originPoint,(e.scale>=jx&&e.scale<=Nx||isNaN(e.scale))&&(e.scale=1),(e.translate>=Ax&&e.translate<=Mx||isNaN(e.translate))&&(e.translate=0)}function Rr(e,t,n,r){cf(e.x,t.x,n.x,r?r.originX:void 0),cf(e.y,t.y,n.y,r?r.originY:void 0)}function ff(e,t,n){e.min=n.min+t.min,e.max=e.min+Ve(t)}function Rx(e,t,n){ff(e.x,t.x,n.x),ff(e.y,t.y,n.y)}function df(e,t,n){e.min=t.min-n.min,e.max=e.min+Ve(t)}function Lr(e,t,n){df(e.x,t.x,n.x),df(e.y,t.y,n.y)}function Lx(e,{min:t,max:n},r){return t!==void 0&&e<t?e=r?G(t,e,r.min):Math.max(e,t):n!==void 0&&e>n&&(e=r?G(n,e,r.max):Math.min(e,n)),e}function hf(e,t,n){return{min:t!==void 0?e.min+t:void 0,max:n!==void 0?e.max+n-(e.max-e.min):void 0}}function Vx(e,{top:t,left:n,bottom:r,right:s}){return{x:hf(e.x,n,s),y:hf(e.y,t,r)}}function pf(e,t){let n=t.min-e.min,r=t.max-e.max;return t.max-t.min<e.max-e.min&&([n,r]=[r,n]),{min:n,max:r}}function _x(e,t){return{x:pf(e.x,t.x),y:pf(e.y,t.y)}}function Fx(e,t){let n=.5;const r=Ve(e),s=Ve(t);return s>r?n=Xn(t.min,t.max-r,e.min):r>s&&(n=Xn(e.min,e.max-s,t.min)),vt(0,1,n)}function Ix(e,t){const n={};return t.min!==void 0&&(n.min=t.min-e.min),t.max!==void 0&&(n.max=t.max-e.min),n}const $a=.35;function Ox(e=$a){return e===!1?e=0:e===!0&&(e=$a),{x:mf(e,"left","right"),y:mf(e,"top","bottom")}}function mf(e,t,n){return{min:yf(e,t),max:yf(e,n)}}function yf(e,t){return typeof e=="number"?e:e[t]||0}const gf=()=>({translate:0,scale:1,origin:0,originPoint:0}),Ln=()=>({x:gf(),y:gf()}),vf=()=>({min:0,max:0}),J=()=>({x:vf(),y:vf()});function Oe(e){return[e("x"),e("y")]}function jm({top:e,left:t,right:n,bottom:r}){return{x:{min:t,max:n},y:{min:e,max:r}}}function zx({x:e,y:t}){return{top:t.min,right:e.max,bottom:t.max,left:e.min}}function Bx(e,t){if(!t)return e;const n=t({x:e.left,y:e.top}),r=t({x:e.right,y:e.bottom});return{top:n.y,left:n.x,bottom:r.y,right:r.x}}function Vo(e){return e===void 0||e===1}function Ha({scale:e,scaleX:t,scaleY:n}){return!Vo(e)||!Vo(t)||!Vo(n)}function Zt(e){return Ha(e)||Nm(e)||e.z||e.rotate||e.rotateX||e.rotateY||e.skewX||e.skewY}function Nm(e){return xf(e.x)||xf(e.y)}function xf(e){return e&&e!=="0%"}function Ni(e,t,n){const r=e-n,s=t*r;return n+s}function wf(e,t,n,r,s){return s!==void 0&&(e=Ni(e,s,r)),Ni(e,n,r)+t}function Wa(e,t=0,n=1,r,s){e.min=wf(e.min,t,n,r,s),e.max=wf(e.max,t,n,r,s)}function Am(e,{x:t,y:n}){Wa(e.x,t.translate,t.scale,t.originPoint),Wa(e.y,n.translate,n.scale,n.originPoint)}const Sf=.999999999999,kf=1.0000000000001;function Ux(e,t,n,r=!1){const s=n.length;if(!s)return;t.x=t.y=1;let i,o;for(let a=0;a<s;a++){i=n[a],o=i.projectionDelta;const{visualElement:l}=i.options;l&&l.props.style&&l.props.style.display==="contents"||(r&&i.options.layoutScroll&&i.scroll&&i!==i.root&&_n(e,{x:-i.scroll.offset.x,y:-i.scroll.offset.y}),o&&(t.x*=o.x.scale,t.y*=o.y.scale,Am(e,o)),r&&Zt(i.latestValues)&&_n(e,i.latestValues))}t.x<kf&&t.x>Sf&&(t.x=1),t.y<kf&&t.y>Sf&&(t.y=1)}function Vn(e,t){e.min=e.min+t,e.max=e.max+t}function Pf(e,t,n,r,s=.5){const i=G(e.min,e.max,s);Wa(e,t,n,i,r)}function _n(e,t){Pf(e.x,t.x,t.scaleX,t.scale,t.originX),Pf(e.y,t.y,t.scaleY,t.scale,t.originY)}function Mm(e,t){return jm(Bx(e.getBoundingClientRect(),t))}function bx(e,t,n){const r=Mm(e,n),{scroll:s}=t;return s&&(Vn(r.x,s.offset.x),Vn(r.y,s.offset.y)),r}const Dm=({current:e})=>e?e.ownerDocument.defaultView:null,$x=new WeakMap;class Hx{constructor(t){this.openDragLock=null,this.isDragging=!1,this.currentDirection=null,this.originPoint={x:0,y:0},this.constraints=!1,this.hasMutatedConstraints=!1,this.elastic=J(),this.visualElement=t}start(t,{snapToCursor:n=!1}={}){const{presenceContext:r}=this.visualElement;if(r&&r.isPresent===!1)return;const s=c=>{const{dragSnapToOrigin:d}=this.getProps();d?this.pauseAnimation():this.stopAnimation(),n&&this.snapToCursor(ds(c).point)},i=(c,d)=>{const{drag:h,dragPropagation:g,onDragStart:v}=this.getProps();if(h&&!g&&(this.openDragLock&&this.openDragLock(),this.openDragLock=Fv(h),!this.openDragLock))return;this.isDragging=!0,this.currentDirection=null,this.resolveConstraints(),this.visualElement.projection&&(this.visualElement.projection.isAnimationBlocked=!0,this.visualElement.projection.target=void 0),Oe(P=>{let m=this.getAxisMotionValue(P).get()||0;if(it.test(m)){const{projection:p}=this.visualElement;if(p&&p.layout){const y=p.layout.layoutBox[P];y&&(m=Ve(y)*(parseFloat(m)/100))}}this.originPoint[P]=m}),v&&$.postRender(()=>v(c,d)),_a(this.visualElement,"transform");const{animationState:x}=this.visualElement;x&&x.setActive("whileDrag",!0)},o=(c,d)=>{const{dragPropagation:h,dragDirectionLock:g,onDirectionLock:v,onDrag:x}=this.getProps();if(!h&&!this.openDragLock)return;const{offset:P}=d;if(g&&this.currentDirection===null){this.currentDirection=Wx(P),this.currentDirection!==null&&v&&v(this.currentDirection);return}this.updateAxis("x",d.point,P),this.updateAxis("y",d.point,P),this.visualElement.render(),x&&x(c,d)},a=(c,d)=>this.stop(c,d),l=()=>Oe(c=>{var d;return this.getAnimationState(c)==="paused"&&((d=this.getAxisMotionValue(c).animation)===null||d===void 0?void 0:d.play())}),{dragSnapToOrigin:u}=this.getProps();this.panSession=new Pm(t,{onSessionStart:s,onStart:i,onMove:o,onSessionEnd:a,resumeAnimation:l},{transformPagePoint:this.visualElement.getTransformPagePoint(),dragSnapToOrigin:u,contextWindow:Dm(this.visualElement)})}stop(t,n){const r=this.isDragging;if(this.cancel(),!r)return;const{velocity:s}=n;this.startAnimation(s);const{onDragEnd:i}=this.getProps();i&&$.postRender(()=>i(t,n))}cancel(){this.isDragging=!1;const{projection:t,animationState:n}=this.visualElement;t&&(t.isAnimationBlocked=!1),this.panSession&&this.panSession.end(),this.panSession=void 0;const{dragPropagation:r}=this.getProps();!r&&this.openDragLock&&(this.openDragLock(),this.openDragLock=null),n&&n.setActive("whileDrag",!1)}updateAxis(t,n,r){const{drag:s}=this.getProps();if(!r||!Fs(t,s,this.currentDirection))return;const i=this.getAxisMotionValue(t);let o=this.originPoint[t]+r[t];this.constraints&&this.constraints[t]&&(o=Lx(o,this.constraints[t],this.elastic[t])),i.set(o)}resolveConstraints(){var t;const{dragConstraints:n,dragElastic:r}=this.getProps(),s=this.visualElement.projection&&!this.visualElement.projection.layout?this.visualElement.projection.measure(!1):(t=this.visualElement.projection)===null||t===void 0?void 0:t.layout,i=this.constraints;n&&Dn(n)?this.constraints||(this.constraints=this.resolveRefConstraints()):n&&s?this.constraints=Vx(s.layoutBox,n):this.constraints=!1,this.elastic=Ox(r),i!==this.constraints&&s&&this.constraints&&!this.hasMutatedConstraints&&Oe(o=>{this.constraints!==!1&&this.getAxisMotionValue(o)&&(this.constraints[o]=Ix(s.layoutBox[o],this.constraints[o]))})}resolveRefConstraints(){const{dragConstraints:t,onMeasureDragConstraints:n}=this.getProps();if(!t||!Dn(t))return!1;const r=t.current,{projection:s}=this.visualElement;if(!s||!s.layout)return!1;const i=bx(r,s.root,this.visualElement.getTransformPagePoint());let o=_x(s.layout.layoutBox,i);if(n){const a=n(zx(o));this.hasMutatedConstraints=!!a,a&&(o=jm(a))}return o}startAnimation(t){const{drag:n,dragMomentum:r,dragElastic:s,dragTransition:i,dragSnapToOrigin:o,onDragTransitionEnd:a}=this.getProps(),l=this.constraints||{},u=Oe(c=>{if(!Fs(c,n,this.currentDirection))return;let d=l&&l[c]||{};o&&(d={min:0,max:0});const h=s?200:1e6,g=s?40:1e7,v={type:"inertia",velocity:r?t[c]:0,bounceStiffness:h,bounceDamping:g,timeConstant:750,restDelta:1,restSpeed:10,...i,...d};return this.startAxisValueAnimation(c,v)});return Promise.all(u).then(a)}startAxisValueAnimation(t,n){const r=this.getAxisMotionValue(t);return _a(this.visualElement,t),r.start(Pu(t,r,0,n,this.visualElement,!1))}stopAnimation(){Oe(t=>this.getAxisMotionValue(t).stop())}pauseAnimation(){Oe(t=>{var n;return(n=this.getAxisMotionValue(t).animation)===null||n===void 0?void 0:n.pause()})}getAnimationState(t){var n;return(n=this.getAxisMotionValue(t).animation)===null||n===void 0?void 0:n.state}getAxisMotionValue(t){const n=`_drag${t.toUpperCase()}`,r=this.visualElement.getProps(),s=r[n];return s||this.visualElement.getValue(t,(r.initial?r.initial[t]:void 0)||0)}snapToCursor(t){Oe(n=>{const{drag:r}=this.getProps();if(!Fs(n,r,this.currentDirection))return;const{projection:s}=this.visualElement,i=this.getAxisMotionValue(n);if(s&&s.layout){const{min:o,max:a}=s.layout.layoutBox[n];i.set(t[n]-G(o,a,.5))}})}scalePositionWithinConstraints(){if(!this.visualElement.current)return;const{drag:t,dragConstraints:n}=this.getProps(),{projection:r}=this.visualElement;if(!Dn(n)||!r||!this.constraints)return;this.stopAnimation();const s={x:0,y:0};Oe(o=>{const a=this.getAxisMotionValue(o);if(a&&this.constraints!==!1){const l=a.get();s[o]=Fx({min:l,max:l},this.constraints[o])}});const{transformTemplate:i}=this.visualElement.getProps();this.visualElement.current.style.transform=i?i({},""):"none",r.root&&r.root.updateScroll(),r.updateLayout(),this.resolveConstraints(),Oe(o=>{if(!Fs(o,t,null))return;const a=this.getAxisMotionValue(o),{min:l,max:u}=this.constraints[o];a.set(G(l,u,s[o]))})}addListeners(){if(!this.visualElement.current)return;$x.set(this.visualElement,this);const t=this.visualElement.current,n=Dr(t,"pointerdown",l=>{const{drag:u,dragListener:c=!0}=this.getProps();u&&c&&this.start(l)}),r=()=>{const{dragConstraints:l}=this.getProps();Dn(l)&&l.current&&(this.constraints=this.resolveRefConstraints())},{projection:s}=this.visualElement,i=s.addEventListener("measure",r);s&&!s.layout&&(s.root&&s.root.updateScroll(),s.updateLayout()),$.read(r);const o=ts(window,"resize",()=>this.scalePositionWithinConstraints()),a=s.addEventListener("didUpdate",({delta:l,hasLayoutChanged:u})=>{this.isDragging&&u&&(Oe(c=>{const d=this.getAxisMotionValue(c);d&&(this.originPoint[c]+=l[c].translate,d.set(d.get()+l[c].translate))}),this.visualElement.render())});return()=>{o(),n(),i(),a&&a()}}getProps(){const t=this.visualElement.getProps(),{drag:n=!1,dragDirectionLock:r=!1,dragPropagation:s=!1,dragConstraints:i=!1,dragElastic:o=$a,dragMomentum:a=!0}=t;return{...t,drag:n,dragDirectionLock:r,dragPropagation:s,dragConstraints:i,dragElastic:o,dragMomentum:a}}}function Fs(e,t,n){return(t===!0||t===e)&&(n===null||n===e)}function Wx(e,t=10){let n=null;return Math.abs(e.y)>t?n="y":Math.abs(e.x)>t&&(n="x"),n}class Kx extends Wt{constructor(t){super(t),this.removeGroupControls=Re,this.removeListeners=Re,this.controls=new Hx(t)}mount(){const{dragControls:t}=this.node.getProps();t&&(this.removeGroupControls=t.subscribe(this.controls)),this.removeListeners=this.controls.addListeners()||Re}unmount(){this.removeGroupControls(),this.removeListeners()}}const Tf=e=>(t,n)=>{e&&$.postRender(()=>e(t,n))};class Gx extends Wt{constructor(){super(...arguments),this.removePointerDownListener=Re}onPointerDown(t){this.session=new Pm(t,this.createPanHandlers(),{transformPagePoint:this.node.getTransformPagePoint(),contextWindow:Dm(this.node)})}createPanHandlers(){const{onPanSessionStart:t,onPanStart:n,onPan:r,onPanEnd:s}=this.node.getProps();return{onSessionStart:Tf(t),onStart:Tf(n),onMove:r,onEnd:(i,o)=>{delete this.session,s&&$.postRender(()=>s(i,o))}}}mount(){this.removePointerDownListener=Dr(this.node.current,"pointerdown",t=>this.onPointerDown(t))}update(){this.session&&this.session.updateHandlers(this.createPanHandlers())}unmount(){this.removePointerDownListener(),this.session&&this.session.end()}}const qs={hasAnimatedSinceResize:!0,hasEverUpdated:!1};function Cf(e,t){return t.max===t.min?0:e/(t.max-t.min)*100}const hr={correct:(e,t)=>{if(!t.target)return e;if(typeof e=="string")if(R.test(e))e=parseFloat(e);else return e;const n=Cf(e,t.target.x),r=Cf(e,t.target.y);return`${n}% ${r}%`}},Qx={correct:(e,{treeScale:t,projectionDelta:n})=>{const r=e,s=Bt.parse(e);if(s.length>5)return r;const i=Bt.createTransformer(e),o=typeof s[0]!="number"?1:0,a=n.x.scale*t.x,l=n.y.scale*t.y;s[0+o]/=a,s[1+o]/=l;const u=G(a,l,.5);return typeof s[2+o]=="number"&&(s[2+o]/=u),typeof s[3+o]=="number"&&(s[3+o]/=u),i(s)}};class Xx extends k.Component{componentDidMount(){const{visualElement:t,layoutGroup:n,switchLayoutGroup:r,layoutId:s}=this.props,{projection:i}=t;mv(Yx),i&&(n.group&&n.group.add(i),r&&r.register&&s&&r.register(i),i.root.didUpdate(),i.addEventListener("animationComplete",()=>{this.safeToRemove()}),i.setOptions({...i.options,onExitComplete:()=>this.safeToRemove()})),qs.hasEverUpdated=!0}getSnapshotBeforeUpdate(t){const{layoutDependency:n,visualElement:r,drag:s,isPresent:i}=this.props,o=r.projection;return o&&(o.isPresent=i,s||t.layoutDependency!==n||n===void 0?o.willUpdate():this.safeToRemove(),t.isPresent!==i&&(i?o.promote():o.relegate()||$.postRender(()=>{const a=o.getStack();(!a||!a.members.length)&&this.safeToRemove()}))),null}componentDidUpdate(){const{projection:t}=this.props.visualElement;t&&(t.root.didUpdate(),Zl.postRender(()=>{!t.currentAnimation&&t.isLead()&&this.safeToRemove()}))}componentWillUnmount(){const{visualElement:t,layoutGroup:n,switchLayoutGroup:r}=this.props,{projection:s}=t;s&&(s.scheduleCheckAfterUnmount(),n&&n.group&&n.group.remove(s),r&&r.deregister&&r.deregister(s))}safeToRemove(){const{safeToRemove:t}=this.props;t&&t()}render(){return null}}function Rm(e){const[t,n]=hp(),r=k.useContext(Hl);return f.jsx(Xx,{...e,layoutGroup:r,switchLayoutGroup:k.useContext(kp),isPresent:t,safeToRemove:n})}const Yx={borderRadius:{...hr,applyTo:["borderTopLeftRadius","borderTopRightRadius","borderBottomLeftRadius","borderBottomRightRadius"]},borderTopLeftRadius:hr,borderTopRightRadius:hr,borderBottomLeftRadius:hr,borderBottomRightRadius:hr,boxShadow:Qx};function qx(e,t,n){const r=ge(e)?e:Jr(e);return r.start(Pu("",r,t,n)),r.animation}function Zx(e){return e instanceof SVGElement&&e.tagName!=="svg"}const Jx=(e,t)=>e.depth-t.depth;class ew{constructor(){this.children=[],this.isDirty=!1}add(t){du(this.children,t),this.isDirty=!0}remove(t){hu(this.children,t),this.isDirty=!0}forEach(t){this.isDirty&&this.children.sort(Jx),this.isDirty=!1,this.children.forEach(t)}}function tw(e,t){const n=ot.now(),r=({timestamp:s})=>{const i=s-n;i>=t&&(zt(r),e(i-t))};return $.read(r,!0),()=>zt(r)}const Lm=["TopLeft","TopRight","BottomLeft","BottomRight"],nw=Lm.length,Ef=e=>typeof e=="string"?parseFloat(e):e,jf=e=>typeof e=="number"||R.test(e);function rw(e,t,n,r,s,i){s?(e.opacity=G(0,n.opacity!==void 0?n.opacity:1,sw(r)),e.opacityExit=G(t.opacity!==void 0?t.opacity:1,0,iw(r))):i&&(e.opacity=G(t.opacity!==void 0?t.opacity:1,n.opacity!==void 0?n.opacity:1,r));for(let o=0;o<nw;o++){const a=`border${Lm[o]}Radius`;let l=Nf(t,a),u=Nf(n,a);if(l===void 0&&u===void 0)continue;l||(l=0),u||(u=0),l===0||u===0||jf(l)===jf(u)?(e[a]=Math.max(G(Ef(l),Ef(u),r),0),(it.test(u)||it.test(l))&&(e[a]+="%")):e[a]=u}(t.rotate||n.rotate)&&(e.rotate=G(t.rotate||0,n.rotate||0,r))}function Nf(e,t){return e[t]!==void 0?e[t]:e.borderRadius}const sw=Vm(0,.5,Zp),iw=Vm(.5,.95,Re);function Vm(e,t,n){return r=>r<e?0:r>t?1:n(Xn(e,t,r))}function Af(e,t){e.min=t.min,e.max=t.max}function Ie(e,t){Af(e.x,t.x),Af(e.y,t.y)}function Mf(e,t){e.translate=t.translate,e.scale=t.scale,e.originPoint=t.originPoint,e.origin=t.origin}function Df(e,t,n,r,s){return e-=t,e=Ni(e,1/n,r),s!==void 0&&(e=Ni(e,1/s,r)),e}function ow(e,t=0,n=1,r=.5,s,i=e,o=e){if(it.test(t)&&(t=parseFloat(t),t=G(o.min,o.max,t/100)-o.min),typeof t!="number")return;let a=G(i.min,i.max,r);e===i&&(a-=t),e.min=Df(e.min,t,n,a,s),e.max=Df(e.max,t,n,a,s)}function Rf(e,t,[n,r,s],i,o){ow(e,t[n],t[r],t[s],t.scale,i,o)}const aw=["x","scaleX","originX"],lw=["y","scaleY","originY"];function Lf(e,t,n,r){Rf(e.x,t,aw,n?n.x:void 0,r?r.x:void 0),Rf(e.y,t,lw,n?n.y:void 0,r?r.y:void 0)}function Vf(e){return e.translate===0&&e.scale===1}function _m(e){return Vf(e.x)&&Vf(e.y)}function _f(e,t){return e.min===t.min&&e.max===t.max}function uw(e,t){return _f(e.x,t.x)&&_f(e.y,t.y)}function Ff(e,t){return Math.round(e.min)===Math.round(t.min)&&Math.round(e.max)===Math.round(t.max)}function Fm(e,t){return Ff(e.x,t.x)&&Ff(e.y,t.y)}function If(e){return Ve(e.x)/Ve(e.y)}function Of(e,t){return e.translate===t.translate&&e.scale===t.scale&&e.originPoint===t.originPoint}class cw{constructor(){this.members=[]}add(t){du(this.members,t),t.scheduleRender()}remove(t){if(hu(this.members,t),t===this.prevLead&&(this.prevLead=void 0),t===this.lead){const n=this.members[this.members.length-1];n&&this.promote(n)}}relegate(t){const n=this.members.findIndex(s=>t===s);if(n===0)return!1;let r;for(let s=n;s>=0;s--){const i=this.members[s];if(i.isPresent!==!1){r=i;break}}return r?(this.promote(r),!0):!1}promote(t,n){const r=this.lead;if(t!==r&&(this.prevLead=r,this.lead=t,t.show(),r)){r.instance&&r.scheduleRender(),t.scheduleRender(),t.resumeFrom=r,n&&(t.resumeFrom.preserveOpacity=!0),r.snapshot&&(t.snapshot=r.snapshot,t.snapshot.latestValues=r.animationValues||r.latestValues),t.root&&t.root.isUpdating&&(t.isLayoutDirty=!0);const{crossfade:s}=t.options;s===!1&&r.hide()}}exitAnimationComplete(){this.members.forEach(t=>{const{options:n,resumingFrom:r}=t;n.onExitComplete&&n.onExitComplete(),r&&r.options.onExitComplete&&r.options.onExitComplete()})}scheduleRender(){this.members.forEach(t=>{t.instance&&t.scheduleRender(!1)})}removeLeadSnapshot(){this.lead&&this.lead.snapshot&&(this.lead.snapshot=void 0)}}function fw(e,t,n){let r="";const s=e.x.translate/t.x,i=e.y.translate/t.y,o=(n==null?void 0:n.z)||0;if((s||i||o)&&(r=`translate3d(${s}px, ${i}px, ${o}px) `),(t.x!==1||t.y!==1)&&(r+=`scale(${1/t.x}, ${1/t.y}) `),n){const{transformPerspective:u,rotate:c,rotateX:d,rotateY:h,skewX:g,skewY:v}=n;u&&(r=`perspective(${u}px) ${r}`),c&&(r+=`rotate(${c}deg) `),d&&(r+=`rotateX(${d}deg) `),h&&(r+=`rotateY(${h}deg) `),g&&(r+=`skewX(${g}deg) `),v&&(r+=`skewY(${v}deg) `)}const a=e.x.scale*t.x,l=e.y.scale*t.y;return(a!==1||l!==1)&&(r+=`scale(${a}, ${l})`),r||"none"}const Jt={type:"projectionFrame",totalNodes:0,resolvedTargetDeltas:0,recalculatedProjection:0},wr=typeof window<"u"&&window.MotionDebug!==void 0,_o=["","X","Y","Z"],dw={visibility:"hidden"},zf=1e3;let hw=0;function Fo(e,t,n,r){const{latestValues:s}=t;s[e]&&(n[e]=s[e],t.setStaticValue(e,0),r&&(r[e]=0))}function Im(e){if(e.hasCheckedOptimisedAppear=!0,e.root===e)return;const{visualElement:t}=e.options;if(!t)return;const n=Wp(t);if(window.MotionHasOptimisedAnimation(n,"transform")){const{layout:s,layoutId:i}=e.options;window.MotionCancelOptimisedAnimation(n,"transform",$,!(s||i))}const{parent:r}=e;r&&!r.hasCheckedOptimisedAppear&&Im(r)}function Om({attachResizeListener:e,defaultParent:t,measureScroll:n,checkIsScrollRoot:r,resetTransform:s}){return class{constructor(o={},a=t==null?void 0:t()){this.id=hw++,this.animationId=0,this.children=new Set,this.options={},this.isTreeAnimating=!1,this.isAnimationBlocked=!1,this.isLayoutDirty=!1,this.isProjectionDirty=!1,this.isSharedProjectionDirty=!1,this.isTransformDirty=!1,this.updateManuallyBlocked=!1,this.updateBlockedByResize=!1,this.isUpdating=!1,this.isSVG=!1,this.needsReset=!1,this.shouldResetTransform=!1,this.hasCheckedOptimisedAppear=!1,this.treeScale={x:1,y:1},this.eventHandlers=new Map,this.hasTreeAnimated=!1,this.updateScheduled=!1,this.scheduleUpdate=()=>this.update(),this.projectionUpdateScheduled=!1,this.checkUpdateFailed=()=>{this.isUpdating&&(this.isUpdating=!1,this.clearAllSnapshots())},this.updateProjection=()=>{this.projectionUpdateScheduled=!1,wr&&(Jt.totalNodes=Jt.resolvedTargetDeltas=Jt.recalculatedProjection=0),this.nodes.forEach(yw),this.nodes.forEach(Sw),this.nodes.forEach(kw),this.nodes.forEach(gw),wr&&window.MotionDebug.record(Jt)},this.resolvedRelativeTargetAt=0,this.hasProjected=!1,this.isVisible=!0,this.animationProgress=0,this.sharedNodes=new Map,this.latestValues=o,this.root=a?a.root||a:this,this.path=a?[...a.path,a]:[],this.parent=a,this.depth=a?a.depth+1:0;for(let l=0;l<this.path.length;l++)this.path[l].shouldResetTransform=!0;this.root===this&&(this.nodes=new ew)}addEventListener(o,a){return this.eventHandlers.has(o)||this.eventHandlers.set(o,new pu),this.eventHandlers.get(o).add(a)}notifyListeners(o,...a){const l=this.eventHandlers.get(o);l&&l.notify(...a)}hasListeners(o){return this.eventHandlers.has(o)}mount(o,a=this.root.hasTreeAnimated){if(this.instance)return;this.isSVG=Zx(o),this.instance=o;const{layoutId:l,layout:u,visualElement:c}=this.options;if(c&&!c.current&&c.mount(o),this.root.nodes.add(this),this.parent&&this.parent.children.add(this),a&&(u||l)&&(this.isLayoutDirty=!0),e){let d;const h=()=>this.root.updateBlockedByResize=!1;e(o,()=>{this.root.updateBlockedByResize=!0,d&&d(),d=tw(h,250),qs.hasAnimatedSinceResize&&(qs.hasAnimatedSinceResize=!1,this.nodes.forEach(Uf))})}l&&this.root.registerSharedNode(l,this),this.options.animate!==!1&&c&&(l||u)&&this.addEventListener("didUpdate",({delta:d,hasLayoutChanged:h,hasRelativeTargetChanged:g,layout:v})=>{if(this.isTreeAnimationBlocked()){this.target=void 0,this.relativeTarget=void 0;return}const x=this.options.transition||c.getDefaultTransition()||jw,{onLayoutAnimationStart:P,onLayoutAnimationComplete:m}=c.getProps(),p=!this.targetLayout||!Fm(this.targetLayout,v)||g,y=!h&&g;if(this.options.layoutRoot||this.resumeFrom&&this.resumeFrom.instance||y||h&&(p||!this.currentAnimation)){this.resumeFrom&&(this.resumingFrom=this.resumeFrom,this.resumingFrom.resumingFrom=void 0),this.setAnimationOrigin(d,y);const w={...lu(x,"layout"),onPlay:P,onComplete:m};(c.shouldReduceMotion||this.options.layoutRoot)&&(w.delay=0,w.type=!1),this.startAnimation(w)}else h||Uf(this),this.isLead()&&this.options.onExitComplete&&this.options.onExitComplete();this.targetLayout=v})}unmount(){this.options.layoutId&&this.willUpdate(),this.root.nodes.remove(this);const o=this.getStack();o&&o.remove(this),this.parent&&this.parent.children.delete(this),this.instance=void 0,zt(this.updateProjection)}blockUpdate(){this.updateManuallyBlocked=!0}unblockUpdate(){this.updateManuallyBlocked=!1}isUpdateBlocked(){return this.updateManuallyBlocked||this.updateBlockedByResize}isTreeAnimationBlocked(){return this.isAnimationBlocked||this.parent&&this.parent.isTreeAnimationBlocked()||!1}startUpdate(){this.isUpdateBlocked()||(this.isUpdating=!0,this.nodes&&this.nodes.forEach(Pw),this.animationId++)}getTransformTemplate(){const{visualElement:o}=this.options;return o&&o.getProps().transformTemplate}willUpdate(o=!0){if(this.root.hasTreeAnimated=!0,this.root.isUpdateBlocked()){this.options.onExitComplete&&this.options.onExitComplete();return}if(window.MotionCancelOptimisedAnimation&&!this.hasCheckedOptimisedAppear&&Im(this),!this.root.isUpdating&&this.root.startUpdate(),this.isLayoutDirty)return;this.isLayoutDirty=!0;for(let c=0;c<this.path.length;c++){const d=this.path[c];d.shouldResetTransform=!0,d.updateScroll("snapshot"),d.options.layoutRoot&&d.willUpdate(!1)}const{layoutId:a,layout:l}=this.options;if(a===void 0&&!l)return;const u=this.getTransformTemplate();this.prevTransformTemplateValue=u?u(this.latestValues,""):void 0,this.updateSnapshot(),o&&this.notifyListeners("willUpdate")}update(){if(this.updateScheduled=!1,this.isUpdateBlocked()){this.unblockUpdate(),this.clearAllSnapshots(),this.nodes.forEach(Bf);return}this.isUpdating||this.nodes.forEach(xw),this.isUpdating=!1,this.nodes.forEach(ww),this.nodes.forEach(pw),this.nodes.forEach(mw),this.clearAllSnapshots();const a=ot.now();ue.delta=vt(0,1e3/60,a-ue.timestamp),ue.timestamp=a,ue.isProcessing=!0,Eo.update.process(ue),Eo.preRender.process(ue),Eo.render.process(ue),ue.isProcessing=!1}didUpdate(){this.updateScheduled||(this.updateScheduled=!0,Zl.read(this.scheduleUpdate))}clearAllSnapshots(){this.nodes.forEach(vw),this.sharedNodes.forEach(Tw)}scheduleUpdateProjection(){this.projectionUpdateScheduled||(this.projectionUpdateScheduled=!0,$.preRender(this.updateProjection,!1,!0))}scheduleCheckAfterUnmount(){$.postRender(()=>{this.isLayoutDirty?this.root.didUpdate():this.root.checkUpdateFailed()})}updateSnapshot(){this.snapshot||!this.instance||(this.snapshot=this.measure())}updateLayout(){if(!this.instance||(this.updateScroll(),!(this.options.alwaysMeasureLayout&&this.isLead())&&!this.isLayoutDirty))return;if(this.resumeFrom&&!this.resumeFrom.instance)for(let l=0;l<this.path.length;l++)this.path[l].updateScroll();const o=this.layout;this.layout=this.measure(!1),this.layoutCorrected=J(),this.isLayoutDirty=!1,this.projectionDelta=void 0,this.notifyListeners("measure",this.layout.layoutBox);const{visualElement:a}=this.options;a&&a.notify("LayoutMeasure",this.layout.layoutBox,o?o.layoutBox:void 0)}updateScroll(o="measure"){let a=!!(this.options.layoutScroll&&this.instance);if(this.scroll&&this.scroll.animationId===this.root.animationId&&this.scroll.phase===o&&(a=!1),a){const l=r(this.instance);this.scroll={animationId:this.root.animationId,phase:o,isRoot:l,offset:n(this.instance),wasRoot:this.scroll?this.scroll.isRoot:l}}}resetTransform(){if(!s)return;const o=this.isLayoutDirty||this.shouldResetTransform||this.options.alwaysMeasureLayout,a=this.projectionDelta&&!_m(this.projectionDelta),l=this.getTransformTemplate(),u=l?l(this.latestValues,""):void 0,c=u!==this.prevTransformTemplateValue;o&&(a||Zt(this.latestValues)||c)&&(s(this.instance,u),this.shouldResetTransform=!1,this.scheduleRender())}measure(o=!0){const a=this.measurePageBox();let l=this.removeElementScroll(a);return o&&(l=this.removeTransform(l)),Nw(l),{animationId:this.root.animationId,measuredBox:a,layoutBox:l,latestValues:{},source:this.id}}measurePageBox(){var o;const{visualElement:a}=this.options;if(!a)return J();const l=a.measureViewportBox();if(!(((o=this.scroll)===null||o===void 0?void 0:o.wasRoot)||this.path.some(Aw))){const{scroll:c}=this.root;c&&(Vn(l.x,c.offset.x),Vn(l.y,c.offset.y))}return l}removeElementScroll(o){var a;const l=J();if(Ie(l,o),!((a=this.scroll)===null||a===void 0)&&a.wasRoot)return l;for(let u=0;u<this.path.length;u++){const c=this.path[u],{scroll:d,options:h}=c;c!==this.root&&d&&h.layoutScroll&&(d.wasRoot&&Ie(l,o),Vn(l.x,d.offset.x),Vn(l.y,d.offset.y))}return l}applyTransform(o,a=!1){const l=J();Ie(l,o);for(let u=0;u<this.path.length;u++){const c=this.path[u];!a&&c.options.layoutScroll&&c.scroll&&c!==c.root&&_n(l,{x:-c.scroll.offset.x,y:-c.scroll.offset.y}),Zt(c.latestValues)&&_n(l,c.latestValues)}return Zt(this.latestValues)&&_n(l,this.latestValues),l}removeTransform(o){const a=J();Ie(a,o);for(let l=0;l<this.path.length;l++){const u=this.path[l];if(!u.instance||!Zt(u.latestValues))continue;Ha(u.latestValues)&&u.updateSnapshot();const c=J(),d=u.measurePageBox();Ie(c,d),Lf(a,u.latestValues,u.snapshot?u.snapshot.layoutBox:void 0,c)}return Zt(this.latestValues)&&Lf(a,this.latestValues),a}setTargetDelta(o){this.targetDelta=o,this.root.scheduleUpdateProjection(),this.isProjectionDirty=!0}setOptions(o){this.options={...this.options,...o,crossfade:o.crossfade!==void 0?o.crossfade:!0}}clearMeasurements(){this.scroll=void 0,this.layout=void 0,this.snapshot=void 0,this.prevTransformTemplateValue=void 0,this.targetDelta=void 0,this.target=void 0,this.isLayoutDirty=!1}forceRelativeParentToResolveTarget(){this.relativeParent&&this.relativeParent.resolvedRelativeTargetAt!==ue.timestamp&&this.relativeParent.resolveTargetDelta(!0)}resolveTargetDelta(o=!1){var a;const l=this.getLead();this.isProjectionDirty||(this.isProjectionDirty=l.isProjectionDirty),this.isTransformDirty||(this.isTransformDirty=l.isTransformDirty),this.isSharedProjectionDirty||(this.isSharedProjectionDirty=l.isSharedProjectionDirty);const u=!!this.resumingFrom||this!==l;if(!(o||u&&this.isSharedProjectionDirty||this.isProjectionDirty||!((a=this.parent)===null||a===void 0)&&a.isProjectionDirty||this.attemptToResolveRelativeTarget||this.root.updateBlockedByResize))return;const{layout:d,layoutId:h}=this.options;if(!(!this.layout||!(d||h))){if(this.resolvedRelativeTargetAt=ue.timestamp,!this.targetDelta&&!this.relativeTarget){const g=this.getClosestProjectingParent();g&&g.layout&&this.animationProgress!==1?(this.relativeParent=g,this.forceRelativeParentToResolveTarget(),this.relativeTarget=J(),this.relativeTargetOrigin=J(),Lr(this.relativeTargetOrigin,this.layout.layoutBox,g.layout.layoutBox),Ie(this.relativeTarget,this.relativeTargetOrigin)):this.relativeParent=this.relativeTarget=void 0}if(!(!this.relativeTarget&&!this.targetDelta)){if(this.target||(this.target=J(),this.targetWithTransforms=J()),this.relativeTarget&&this.relativeTargetOrigin&&this.relativeParent&&this.relativeParent.target?(this.forceRelativeParentToResolveTarget(),Rx(this.target,this.relativeTarget,this.relativeParent.target)):this.targetDelta?(this.resumingFrom?this.target=this.applyTransform(this.layout.layoutBox):Ie(this.target,this.layout.layoutBox),Am(this.target,this.targetDelta)):Ie(this.target,this.layout.layoutBox),this.attemptToResolveRelativeTarget){this.attemptToResolveRelativeTarget=!1;const g=this.getClosestProjectingParent();g&&!!g.resumingFrom==!!this.resumingFrom&&!g.options.layoutScroll&&g.target&&this.animationProgress!==1?(this.relativeParent=g,this.forceRelativeParentToResolveTarget(),this.relativeTarget=J(),this.relativeTargetOrigin=J(),Lr(this.relativeTargetOrigin,this.target,g.target),Ie(this.relativeTarget,this.relativeTargetOrigin)):this.relativeParent=this.relativeTarget=void 0}wr&&Jt.resolvedTargetDeltas++}}}getClosestProjectingParent(){if(!(!this.parent||Ha(this.parent.latestValues)||Nm(this.parent.latestValues)))return this.parent.isProjecting()?this.parent:this.parent.getClosestProjectingParent()}isProjecting(){return!!((this.relativeTarget||this.targetDelta||this.options.layoutRoot)&&this.layout)}calcProjection(){var o;const a=this.getLead(),l=!!this.resumingFrom||this!==a;let u=!0;if((this.isProjectionDirty||!((o=this.parent)===null||o===void 0)&&o.isProjectionDirty)&&(u=!1),l&&(this.isSharedProjectionDirty||this.isTransformDirty)&&(u=!1),this.resolvedRelativeTargetAt===ue.timestamp&&(u=!1),u)return;const{layout:c,layoutId:d}=this.options;if(this.isTreeAnimating=!!(this.parent&&this.parent.isTreeAnimating||this.currentAnimation||this.pendingAnimation),this.isTreeAnimating||(this.targetDelta=this.relativeTarget=void 0),!this.layout||!(c||d))return;Ie(this.layoutCorrected,this.layout.layoutBox);const h=this.treeScale.x,g=this.treeScale.y;Ux(this.layoutCorrected,this.treeScale,this.path,l),a.layout&&!a.target&&(this.treeScale.x!==1||this.treeScale.y!==1)&&(a.target=a.layout.layoutBox,a.targetWithTransforms=J());const{target:v}=a;if(!v){this.prevProjectionDelta&&(this.createProjectionDeltas(),this.scheduleRender());return}!this.projectionDelta||!this.prevProjectionDelta?this.createProjectionDeltas():(Mf(this.prevProjectionDelta.x,this.projectionDelta.x),Mf(this.prevProjectionDelta.y,this.projectionDelta.y)),Rr(this.projectionDelta,this.layoutCorrected,v,this.latestValues),(this.treeScale.x!==h||this.treeScale.y!==g||!Of(this.projectionDelta.x,this.prevProjectionDelta.x)||!Of(this.projectionDelta.y,this.prevProjectionDelta.y))&&(this.hasProjected=!0,this.scheduleRender(),this.notifyListeners("projectionUpdate",v)),wr&&Jt.recalculatedProjection++}hide(){this.isVisible=!1}show(){this.isVisible=!0}scheduleRender(o=!0){var a;if((a=this.options.visualElement)===null||a===void 0||a.scheduleRender(),o){const l=this.getStack();l&&l.scheduleRender()}this.resumingFrom&&!this.resumingFrom.instance&&(this.resumingFrom=void 0)}createProjectionDeltas(){this.prevProjectionDelta=Ln(),this.projectionDelta=Ln(),this.projectionDeltaWithTransform=Ln()}setAnimationOrigin(o,a=!1){const l=this.snapshot,u=l?l.latestValues:{},c={...this.latestValues},d=Ln();(!this.relativeParent||!this.relativeParent.options.layoutRoot)&&(this.relativeTarget=this.relativeTargetOrigin=void 0),this.attemptToResolveRelativeTarget=!a;const h=J(),g=l?l.source:void 0,v=this.layout?this.layout.source:void 0,x=g!==v,P=this.getStack(),m=!P||P.members.length<=1,p=!!(x&&!m&&this.options.crossfade===!0&&!this.path.some(Ew));this.animationProgress=0;let y;this.mixTargetDelta=w=>{const S=w/1e3;bf(d.x,o.x,S),bf(d.y,o.y,S),this.setTargetDelta(d),this.relativeTarget&&this.relativeTargetOrigin&&this.layout&&this.relativeParent&&this.relativeParent.layout&&(Lr(h,this.layout.layoutBox,this.relativeParent.layout.layoutBox),Cw(this.relativeTarget,this.relativeTargetOrigin,h,S),y&&uw(this.relativeTarget,y)&&(this.isProjectionDirty=!1),y||(y=J()),Ie(y,this.relativeTarget)),x&&(this.animationValues=c,rw(c,u,this.latestValues,S,p,m)),this.root.scheduleUpdateProjection(),this.scheduleRender(),this.animationProgress=S},this.mixTargetDelta(this.options.layoutRoot?1e3:0)}startAnimation(o){this.notifyListeners("animationStart"),this.currentAnimation&&this.currentAnimation.stop(),this.resumingFrom&&this.resumingFrom.currentAnimation&&this.resumingFrom.currentAnimation.stop(),this.pendingAnimation&&(zt(this.pendingAnimation),this.pendingAnimation=void 0),this.pendingAnimation=$.update(()=>{qs.hasAnimatedSinceResize=!0,this.currentAnimation=qx(0,zf,{...o,onUpdate:a=>{this.mixTargetDelta(a),o.onUpdate&&o.onUpdate(a)},onComplete:()=>{o.onComplete&&o.onComplete(),this.completeAnimation()}}),this.resumingFrom&&(this.resumingFrom.currentAnimation=this.currentAnimation),this.pendingAnimation=void 0})}completeAnimation(){this.resumingFrom&&(this.resumingFrom.currentAnimation=void 0,this.resumingFrom.preserveOpacity=void 0);const o=this.getStack();o&&o.exitAnimationComplete(),this.resumingFrom=this.currentAnimation=this.animationValues=void 0,this.notifyListeners("animationComplete")}finishAnimation(){this.currentAnimation&&(this.mixTargetDelta&&this.mixTargetDelta(zf),this.currentAnimation.stop()),this.completeAnimation()}applyTransformsToTarget(){const o=this.getLead();let{targetWithTransforms:a,target:l,layout:u,latestValues:c}=o;if(!(!a||!l||!u)){if(this!==o&&this.layout&&u&&zm(this.options.animationType,this.layout.layoutBox,u.layoutBox)){l=this.target||J();const d=Ve(this.layout.layoutBox.x);l.x.min=o.target.x.min,l.x.max=l.x.min+d;const h=Ve(this.layout.layoutBox.y);l.y.min=o.target.y.min,l.y.max=l.y.min+h}Ie(a,l),_n(a,c),Rr(this.projectionDeltaWithTransform,this.layoutCorrected,a,c)}}registerSharedNode(o,a){this.sharedNodes.has(o)||this.sharedNodes.set(o,new cw),this.sharedNodes.get(o).add(a);const u=a.options.initialPromotionConfig;a.promote({transition:u?u.transition:void 0,preserveFollowOpacity:u&&u.shouldPreserveFollowOpacity?u.shouldPreserveFollowOpacity(a):void 0})}isLead(){const o=this.getStack();return o?o.lead===this:!0}getLead(){var o;const{layoutId:a}=this.options;return a?((o=this.getStack())===null||o===void 0?void 0:o.lead)||this:this}getPrevLead(){var o;const{layoutId:a}=this.options;return a?(o=this.getStack())===null||o===void 0?void 0:o.prevLead:void 0}getStack(){const{layoutId:o}=this.options;if(o)return this.root.sharedNodes.get(o)}promote({needsReset:o,transition:a,preserveFollowOpacity:l}={}){const u=this.getStack();u&&u.promote(this,l),o&&(this.projectionDelta=void 0,this.needsReset=!0),a&&this.setOptions({transition:a})}relegate(){const o=this.getStack();return o?o.relegate(this):!1}resetSkewAndRotation(){const{visualElement:o}=this.options;if(!o)return;let a=!1;const{latestValues:l}=o;if((l.z||l.rotate||l.rotateX||l.rotateY||l.rotateZ||l.skewX||l.skewY)&&(a=!0),!a)return;const u={};l.z&&Fo("z",o,u,this.animationValues);for(let c=0;c<_o.length;c++)Fo(`rotate${_o[c]}`,o,u,this.animationValues),Fo(`skew${_o[c]}`,o,u,this.animationValues);o.render();for(const c in u)o.setStaticValue(c,u[c]),this.animationValues&&(this.animationValues[c]=u[c]);o.scheduleRender()}getProjectionStyles(o){var a,l;if(!this.instance||this.isSVG)return;if(!this.isVisible)return dw;const u={visibility:""},c=this.getTransformTemplate();if(this.needsReset)return this.needsReset=!1,u.opacity="",u.pointerEvents=Xs(o==null?void 0:o.pointerEvents)||"",u.transform=c?c(this.latestValues,""):"none",u;const d=this.getLead();if(!this.projectionDelta||!this.layout||!d.target){const x={};return this.options.layoutId&&(x.opacity=this.latestValues.opacity!==void 0?this.latestValues.opacity:1,x.pointerEvents=Xs(o==null?void 0:o.pointerEvents)||""),this.hasProjected&&!Zt(this.latestValues)&&(x.transform=c?c({},""):"none",this.hasProjected=!1),x}const h=d.animationValues||d.latestValues;this.applyTransformsToTarget(),u.transform=fw(this.projectionDeltaWithTransform,this.treeScale,h),c&&(u.transform=c(h,u.transform));const{x:g,y:v}=this.projectionDelta;u.transformOrigin=`${g.origin*100}% ${v.origin*100}% 0`,d.animationValues?u.opacity=d===this?(l=(a=h.opacity)!==null&&a!==void 0?a:this.latestValues.opacity)!==null&&l!==void 0?l:1:this.preserveOpacity?this.latestValues.opacity:h.opacityExit:u.opacity=d===this?h.opacity!==void 0?h.opacity:"":h.opacityExit!==void 0?h.opacityExit:0;for(const x in Pi){if(h[x]===void 0)continue;const{correct:P,applyTo:m}=Pi[x],p=u.transform==="none"?h[x]:P(h[x],d);if(m){const y=m.length;for(let w=0;w<y;w++)u[m[w]]=p}else u[x]=p}return this.options.layoutId&&(u.pointerEvents=d===this?Xs(o==null?void 0:o.pointerEvents)||"":"none"),u}clearSnapshot(){this.resumeFrom=this.snapshot=void 0}resetTree(){this.root.nodes.forEach(o=>{var a;return(a=o.currentAnimation)===null||a===void 0?void 0:a.stop()}),this.root.nodes.forEach(Bf),this.root.sharedNodes.clear()}}}function pw(e){e.updateLayout()}function mw(e){var t;const n=((t=e.resumeFrom)===null||t===void 0?void 0:t.snapshot)||e.snapshot;if(e.isLead()&&e.layout&&n&&e.hasListeners("didUpdate")){const{layoutBox:r,measuredBox:s}=e.layout,{animationType:i}=e.options,o=n.source!==e.layout.source;i==="size"?Oe(d=>{const h=o?n.measuredBox[d]:n.layoutBox[d],g=Ve(h);h.min=r[d].min,h.max=h.min+g}):zm(i,n.layoutBox,r)&&Oe(d=>{const h=o?n.measuredBox[d]:n.layoutBox[d],g=Ve(r[d]);h.max=h.min+g,e.relativeTarget&&!e.currentAnimation&&(e.isProjectionDirty=!0,e.relativeTarget[d].max=e.relativeTarget[d].min+g)});const a=Ln();Rr(a,r,n.layoutBox);const l=Ln();o?Rr(l,e.applyTransform(s,!0),n.measuredBox):Rr(l,r,n.layoutBox);const u=!_m(a);let c=!1;if(!e.resumeFrom){const d=e.getClosestProjectingParent();if(d&&!d.resumeFrom){const{snapshot:h,layout:g}=d;if(h&&g){const v=J();Lr(v,n.layoutBox,h.layoutBox);const x=J();Lr(x,r,g.layoutBox),Fm(v,x)||(c=!0),d.options.layoutRoot&&(e.relativeTarget=x,e.relativeTargetOrigin=v,e.relativeParent=d)}}}e.notifyListeners("didUpdate",{layout:r,snapshot:n,delta:l,layoutDelta:a,hasLayoutChanged:u,hasRelativeTargetChanged:c})}else if(e.isLead()){const{onExitComplete:r}=e.options;r&&r()}e.options.transition=void 0}function yw(e){wr&&Jt.totalNodes++,e.parent&&(e.isProjecting()||(e.isProjectionDirty=e.parent.isProjectionDirty),e.isSharedProjectionDirty||(e.isSharedProjectionDirty=!!(e.isProjectionDirty||e.parent.isProjectionDirty||e.parent.isSharedProjectionDirty)),e.isTransformDirty||(e.isTransformDirty=e.parent.isTransformDirty))}function gw(e){e.isProjectionDirty=e.isSharedProjectionDirty=e.isTransformDirty=!1}function vw(e){e.clearSnapshot()}function Bf(e){e.clearMeasurements()}function xw(e){e.isLayoutDirty=!1}function ww(e){const{visualElement:t}=e.options;t&&t.getProps().onBeforeLayoutMeasure&&t.notify("BeforeLayoutMeasure"),e.resetTransform()}function Uf(e){e.finishAnimation(),e.targetDelta=e.relativeTarget=e.target=void 0,e.isProjectionDirty=!0}function Sw(e){e.resolveTargetDelta()}function kw(e){e.calcProjection()}function Pw(e){e.resetSkewAndRotation()}function Tw(e){e.removeLeadSnapshot()}function bf(e,t,n){e.translate=G(t.translate,0,n),e.scale=G(t.scale,1,n),e.origin=t.origin,e.originPoint=t.originPoint}function $f(e,t,n,r){e.min=G(t.min,n.min,r),e.max=G(t.max,n.max,r)}function Cw(e,t,n,r){$f(e.x,t.x,n.x,r),$f(e.y,t.y,n.y,r)}function Ew(e){return e.animationValues&&e.animationValues.opacityExit!==void 0}const jw={duration:.45,ease:[.4,0,.1,1]},Hf=e=>typeof navigator<"u"&&navigator.userAgent&&navigator.userAgent.toLowerCase().includes(e),Wf=Hf("applewebkit/")&&!Hf("chrome/")?Math.round:Re;function Kf(e){e.min=Wf(e.min),e.max=Wf(e.max)}function Nw(e){Kf(e.x),Kf(e.y)}function zm(e,t,n){return e==="position"||e==="preserve-aspect"&&!Dx(If(t),If(n),.2)}function Aw(e){var t;return e!==e.root&&((t=e.scroll)===null||t===void 0?void 0:t.wasRoot)}const Mw=Om({attachResizeListener:(e,t)=>ts(e,"resize",t),measureScroll:()=>({x:document.documentElement.scrollLeft||document.body.scrollLeft,y:document.documentElement.scrollTop||document.body.scrollTop}),checkIsScrollRoot:()=>!0}),Io={current:void 0},Bm=Om({measureScroll:e=>({x:e.scrollLeft,y:e.scrollTop}),defaultParent:()=>{if(!Io.current){const e=new Mw({});e.mount(window),e.setOptions({layoutScroll:!0}),Io.current=e}return Io.current},resetTransform:(e,t)=>{e.style.transform=t!==void 0?t:"none"},checkIsScrollRoot:e=>window.getComputedStyle(e).position==="fixed"}),Dw={pan:{Feature:Gx},drag:{Feature:Kx,ProjectionNode:Bm,MeasureLayout:Rm}};function Gf(e,t,n){const{props:r}=e;e.animationState&&r.whileHover&&e.animationState.setActive("whileHover",n==="Start");const s="onHover"+n,i=r[s];i&&$.postRender(()=>i(t,ds(t)))}class Rw extends Wt{mount(){const{current:t}=this.node;t&&(this.unmount=Dv(t,n=>(Gf(this.node,n,"Start"),r=>Gf(this.node,r,"End"))))}unmount(){}}class Lw extends Wt{constructor(){super(...arguments),this.isActive=!1}onFocus(){let t=!1;try{t=this.node.current.matches(":focus-visible")}catch{t=!0}!t||!this.node.animationState||(this.node.animationState.setActive("whileFocus",!0),this.isActive=!0)}onBlur(){!this.isActive||!this.node.animationState||(this.node.animationState.setActive("whileFocus",!1),this.isActive=!1)}mount(){this.unmount=fs(ts(this.node.current,"focus",()=>this.onFocus()),ts(this.node.current,"blur",()=>this.onBlur()))}unmount(){}}function Qf(e,t,n){const{props:r}=e;e.animationState&&r.whileTap&&e.animationState.setActive("whileTap",n==="Start");const s="onTap"+(n==="End"?"":n),i=r[s];i&&$.postRender(()=>i(t,ds(t)))}class Vw extends Wt{mount(){const{current:t}=this.node;t&&(this.unmount=_v(t,n=>(Qf(this.node,n,"Start"),(r,{success:s})=>Qf(this.node,r,s?"End":"Cancel")),{useGlobalTarget:this.node.props.globalTapTarget}))}unmount(){}}const Ka=new WeakMap,Oo=new WeakMap,_w=e=>{const t=Ka.get(e.target);t&&t(e)},Fw=e=>{e.forEach(_w)};function Iw({root:e,...t}){const n=e||document;Oo.has(n)||Oo.set(n,{});const r=Oo.get(n),s=JSON.stringify(t);return r[s]||(r[s]=new IntersectionObserver(Fw,{root:e,...t})),r[s]}function Ow(e,t,n){const r=Iw(t);return Ka.set(e,n),r.observe(e),()=>{Ka.delete(e),r.unobserve(e)}}const zw={some:0,all:1};class Bw extends Wt{constructor(){super(...arguments),this.hasEnteredView=!1,this.isInView=!1}startObserver(){this.unmount();const{viewport:t={}}=this.node.getProps(),{root:n,margin:r,amount:s="some",once:i}=t,o={root:n?n.current:void 0,rootMargin:r,threshold:typeof s=="number"?s:zw[s]},a=l=>{const{isIntersecting:u}=l;if(this.isInView===u||(this.isInView=u,i&&!u&&this.hasEnteredView))return;u&&(this.hasEnteredView=!0),this.node.animationState&&this.node.animationState.setActive("whileInView",u);const{onViewportEnter:c,onViewportLeave:d}=this.node.getProps(),h=u?c:d;h&&h(l)};return Ow(this.node.current,o,a)}mount(){this.startObserver()}update(){if(typeof IntersectionObserver>"u")return;const{props:t,prevProps:n}=this.node;["amount","margin","root"].some(Uw(t,n))&&this.startObserver()}unmount(){}}function Uw({viewport:e={}},{viewport:t={}}={}){return n=>e[n]!==t[n]}const bw={inView:{Feature:Bw},tap:{Feature:Vw},focus:{Feature:Lw},hover:{Feature:Rw}},$w={layout:{ProjectionNode:Bm,MeasureLayout:Rm}},Ga={current:null},Um={current:!1};function Hw(){if(Um.current=!0,!!Gl)if(window.matchMedia){const e=window.matchMedia("(prefers-reduced-motion)"),t=()=>Ga.current=e.matches;e.addListener(t),t()}else Ga.current=!1}const Ww=[...dm,me,Bt],Kw=e=>Ww.find(fm(e)),Xf=new WeakMap;function Gw(e,t,n){for(const r in t){const s=t[r],i=n[r];if(ge(s))e.addValue(r,s);else if(ge(i))e.addValue(r,Jr(s,{owner:e}));else if(i!==s)if(e.hasValue(r)){const o=e.getValue(r);o.liveStyle===!0?o.jump(s):o.hasAnimated||o.set(s)}else{const o=e.getStaticValue(r);e.addValue(r,Jr(o!==void 0?o:s,{owner:e}))}}for(const r in n)t[r]===void 0&&e.removeValue(r);return t}const Yf=["AnimationStart","AnimationComplete","Update","BeforeLayoutMeasure","LayoutMeasure","LayoutAnimationStart","LayoutAnimationComplete"];class Qw{scrapeMotionValuesFromProps(t,n,r){return{}}constructor({parent:t,props:n,presenceContext:r,reducedMotionConfig:s,blockInitialAnimation:i,visualState:o},a={}){this.current=null,this.children=new Set,this.isVariantNode=!1,this.isControllingVariants=!1,this.shouldReduceMotion=null,this.values=new Map,this.KeyframeResolver=wu,this.features={},this.valueSubscriptions=new Map,this.prevMotionValues={},this.events={},this.propEventSubscriptions={},this.notifyUpdate=()=>this.notify("Update",this.latestValues),this.render=()=>{this.current&&(this.triggerBuild(),this.renderInstance(this.current,this.renderState,this.props.style,this.projection))},this.renderScheduledAt=0,this.scheduleRender=()=>{const g=ot.now();this.renderScheduledAt<g&&(this.renderScheduledAt=g,$.render(this.render,!1,!0))};const{latestValues:l,renderState:u,onUpdate:c}=o;this.onUpdate=c,this.latestValues=l,this.baseTarget={...l},this.initialValues=n.initial?{...l}:{},this.renderState=u,this.parent=t,this.props=n,this.presenceContext=r,this.depth=t?t.depth+1:0,this.reducedMotionConfig=s,this.options=a,this.blockInitialAnimation=!!i,this.isControllingVariants=Qi(n),this.isVariantNode=wp(n),this.isVariantNode&&(this.variantChildren=new Set),this.manuallyAnimateOnMount=!!(t&&t.current);const{willChange:d,...h}=this.scrapeMotionValuesFromProps(n,{},this);for(const g in h){const v=h[g];l[g]!==void 0&&ge(v)&&v.set(l[g],!1)}}mount(t){this.current=t,Xf.set(t,this),this.projection&&!this.projection.instance&&this.projection.mount(t),this.parent&&this.isVariantNode&&!this.isControllingVariants&&(this.removeFromVariantTree=this.parent.addVariantChild(this)),this.values.forEach((n,r)=>this.bindToMotionValue(r,n)),Um.current||Hw(),this.shouldReduceMotion=this.reducedMotionConfig==="never"?!1:this.reducedMotionConfig==="always"?!0:Ga.current,this.parent&&this.parent.children.add(this),this.update(this.props,this.presenceContext)}unmount(){Xf.delete(this.current),this.projection&&this.projection.unmount(),zt(this.notifyUpdate),zt(this.render),this.valueSubscriptions.forEach(t=>t()),this.valueSubscriptions.clear(),this.removeFromVariantTree&&this.removeFromVariantTree(),this.parent&&this.parent.children.delete(this);for(const t in this.events)this.events[t].clear();for(const t in this.features){const n=this.features[t];n&&(n.unmount(),n.isMounted=!1)}this.current=null}bindToMotionValue(t,n){this.valueSubscriptions.has(t)&&this.valueSubscriptions.get(t)();const r=gn.has(t),s=n.on("change",a=>{this.latestValues[t]=a,this.props.onUpdate&&$.preRender(this.notifyUpdate),r&&this.projection&&(this.projection.isTransformDirty=!0)}),i=n.on("renderRequest",this.scheduleRender);let o;window.MotionCheckAppearSync&&(o=window.MotionCheckAppearSync(this,t,n)),this.valueSubscriptions.set(t,()=>{s(),i(),o&&o(),n.owner&&n.stop()})}sortNodePosition(t){return!this.current||!this.sortInstanceNodePosition||this.type!==t.type?0:this.sortInstanceNodePosition(this.current,t.current)}updateFeatures(){let t="animation";for(t in Yn){const n=Yn[t];if(!n)continue;const{isEnabled:r,Feature:s}=n;if(!this.features[t]&&s&&r(this.props)&&(this.features[t]=new s(this)),this.features[t]){const i=this.features[t];i.isMounted?i.update():(i.mount(),i.isMounted=!0)}}}triggerBuild(){this.build(this.renderState,this.latestValues,this.props)}measureViewportBox(){return this.current?this.measureInstanceViewportBox(this.current,this.props):J()}getStaticValue(t){return this.latestValues[t]}setStaticValue(t,n){this.latestValues[t]=n}update(t,n){(t.transformTemplate||this.props.transformTemplate)&&this.scheduleRender(),this.prevProps=this.props,this.props=t,this.prevPresenceContext=this.presenceContext,this.presenceContext=n;for(let r=0;r<Yf.length;r++){const s=Yf[r];this.propEventSubscriptions[s]&&(this.propEventSubscriptions[s](),delete this.propEventSubscriptions[s]);const i="on"+s,o=t[i];o&&(this.propEventSubscriptions[s]=this.on(s,o))}this.prevMotionValues=Gw(this,this.scrapeMotionValuesFromProps(t,this.prevProps,this),this.prevMotionValues),this.handleChildMotionValue&&this.handleChildMotionValue(),this.onUpdate&&this.onUpdate(this)}getProps(){return this.props}getVariant(t){return this.props.variants?this.props.variants[t]:void 0}getDefaultTransition(){return this.props.transition}getTransformPagePoint(){return this.props.transformPagePoint}getClosestVariantNode(){return this.isVariantNode?this:this.parent?this.parent.getClosestVariantNode():void 0}addVariantChild(t){const n=this.getClosestVariantNode();if(n)return n.variantChildren&&n.variantChildren.add(t),()=>n.variantChildren.delete(t)}addValue(t,n){const r=this.values.get(t);n!==r&&(r&&this.removeValue(t),this.bindToMotionValue(t,n),this.values.set(t,n),this.latestValues[t]=n.get())}removeValue(t){this.values.delete(t);const n=this.valueSubscriptions.get(t);n&&(n(),this.valueSubscriptions.delete(t)),delete this.latestValues[t],this.removeValueFromRenderState(t,this.renderState)}hasValue(t){return this.values.has(t)}getValue(t,n){if(this.props.values&&this.props.values[t])return this.props.values[t];let r=this.values.get(t);return r===void 0&&n!==void 0&&(r=Jr(n===null?void 0:n,{owner:this}),this.addValue(t,r)),r}readValue(t,n){var r;let s=this.latestValues[t]!==void 0||!this.current?this.latestValues[t]:(r=this.getBaseTargetFromProps(this.props,t))!==null&&r!==void 0?r:this.readValueFromInstance(this.current,t,this.options);return s!=null&&(typeof s=="string"&&(um(s)||em(s))?s=parseFloat(s):!Kw(s)&&Bt.test(n)&&(s=om(t,n)),this.setBaseTarget(t,ge(s)?s.get():s)),ge(s)?s.get():s}setBaseTarget(t,n){this.baseTarget[t]=n}getBaseTarget(t){var n;const{initial:r}=this.props;let s;if(typeof r=="string"||typeof r=="object"){const o=eu(this.props,r,(n=this.presenceContext)===null||n===void 0?void 0:n.custom);o&&(s=o[t])}if(r&&s!==void 0)return s;const i=this.getBaseTargetFromProps(this.props,t);return i!==void 0&&!ge(i)?i:this.initialValues[t]!==void 0&&s===void 0?void 0:this.baseTarget[t]}on(t,n){return this.events[t]||(this.events[t]=new pu),this.events[t].add(n)}notify(t,...n){this.events[t]&&this.events[t].notify(...n)}}class bm extends Qw{constructor(){super(...arguments),this.KeyframeResolver=hm}sortInstanceNodePosition(t,n){return t.compareDocumentPosition(n)&2?1:-1}getBaseTargetFromProps(t,n){return t.style?t.style[n]:void 0}removeValueFromRenderState(t,{vars:n,style:r}){delete n[t],delete r[t]}handleChildMotionValue(){this.childSubscription&&(this.childSubscription(),delete this.childSubscription);const{children:t}=this.props;ge(t)&&(this.childSubscription=t.on("change",n=>{this.current&&(this.current.textContent=`${n}`)}))}}function Xw(e){return window.getComputedStyle(e)}class Yw extends bm{constructor(){super(...arguments),this.type="html",this.renderInstance=Ap}readValueFromInstance(t,n){if(gn.has(n)){const r=xu(n);return r&&r.default||0}else{const r=Xw(t),s=(Ep(n)?r.getPropertyValue(n):r[n])||0;return typeof s=="string"?s.trim():s}}measureInstanceViewportBox(t,{transformPagePoint:n}){return Mm(t,n)}build(t,n,r){ru(t,n,r.transformTemplate)}scrapeMotionValuesFromProps(t,n,r){return au(t,n,r)}}class qw extends bm{constructor(){super(...arguments),this.type="svg",this.isSVGTag=!1,this.measureInstanceViewportBox=J}getBaseTargetFromProps(t,n){return t[n]}readValueFromInstance(t,n){if(gn.has(n)){const r=xu(n);return r&&r.default||0}return n=Mp.has(n)?n:ql(n),t.getAttribute(n)}scrapeMotionValuesFromProps(t,n,r){return Lp(t,n,r)}build(t,n,r){su(t,n,this.isSVGTag,r.transformTemplate)}renderInstance(t,n,r,s){Dp(t,n,r,s)}mount(t){this.isSVGTag=ou(t.tagName),super.mount(t)}}const Zw=(e,t)=>Jl(e)?new qw(t):new Yw(t,{allowProjection:e!==k.Fragment}),Jw=Tv({...kx,...bw,...Dw,...$w},Zw),L=z0(Jw),eS={},tS=Object.freeze(Object.defineProperty({__proto__:null,default:eS},Symbol.toStringTag,{value:"Module"})),$m=ey(tS);var qf={};const Zf=$m,Qa=$m,Jf=Qa.join(__dirname,"path.txt");function nS(){let e;if(Zf.existsSync(Jf)&&(e=Zf.readFileSync(Jf,"utf-8")),qf.ELECTRON_OVERRIDE_DIST_PATH)return Qa.join(qf.ELECTRON_OVERRIDE_DIST_PATH,e||"electron");if(e)return Qa.join(__dirname,"dist",e);throw new Error("Electron failed to install correctly, please delete node_modules/electron and try installing again")}var St=nS();const en=class en{constructor(){Qt(this,"session",null);Qt(this,"user",null);Qt(this,"profile",null);Qt(this,"requiresMFA",!1)}static getInstance(){return en.instance||(en.instance=new en),en.instance}async login(t){try{const n=await St.ipcRenderer.invoke("auth:login",t);if(n.error)throw new Error(n.error);if(n.requiresMFA)throw this.requiresMFA=!0,new Error("MFA_REQUIRED");return this.session=n.session,this.user=n.user,this.profile=n.profile,this.session&&localStorage.setItem("authSession",JSON.stringify(this.session)),this.session}catch(n){throw console.error("Login failed:",n),n}}async verifyMFA(t){var n;try{if(!this.requiresMFA)throw new Error("MFA not required");const r=await St.ipcRenderer.invoke("auth:verify-mfa",{sessionId:(n=this.session)==null?void 0:n.sessionId,code:t});if(r.error)throw new Error(r.error);return this.session=r.session,this.user=r.user,this.profile=r.profile,this.requiresMFA=!1,this.session&&localStorage.setItem("authSession",JSON.stringify(this.session)),this.session}catch(r){throw console.error("MFA verification failed:",r),r}}async register(t){try{const n=await St.ipcRenderer.invoke("auth:register",t);if(n.error)throw new Error(n.error);return n}catch(n){throw console.error("Registration failed:",n),n}}async logout(){try{this.session&&await St.ipcRenderer.invoke("auth:logout",this.session.sessionId),this.session=null,this.user=null,this.profile=null,this.requiresMFA=!1,localStorage.removeItem("authSession")}catch(t){throw console.error("Logout failed:",t),this.session=null,this.user=null,this.profile=null,this.requiresMFA=!1,localStorage.removeItem("authSession"),t}}async validateSession(){var t;try{if(!this.session){const r=localStorage.getItem("authSession");if(r)this.session=JSON.parse(r);else return!1}const n=await St.ipcRenderer.invoke("auth:validate-session",(t=this.session)==null?void 0:t.sessionId);return n.error||!n.isValid?(this.session=null,this.user=null,this.profile=null,this.requiresMFA=!1,localStorage.removeItem("authSession"),!1):(this.session=n.session,this.user=n.user,this.profile=n.profile,localStorage.setItem("authSession",JSON.stringify(this.session)),!0)}catch(n){return console.error("Session validation failed:",n),this.session=null,this.user=null,this.profile=null,this.requiresMFA=!1,localStorage.removeItem("authSession"),!1}}async refreshSession(){try{if(!this.session)return null;const t=await St.ipcRenderer.invoke("auth:refresh-session",this.session.sessionId);if(t.error)throw new Error(t.error);return this.session=t.session,localStorage.setItem("authSession",JSON.stringify(this.session)),this.session}catch(t){return console.error("Session refresh failed:",t),null}}async changePassword(t,n){try{if(!this.user)throw new Error("User not authenticated");const r=await St.ipcRenderer.invoke("auth:change-password",{userId:this.user.id,currentPassword:t,newPassword:n});if(r.error)throw new Error(r.error)}catch(r){throw console.error("Password change failed:",r),r}}async resetPassword(t,n){try{const r=await St.ipcRenderer.invoke("auth:reset-password",{username:t,newPassword:n});if(r.error)throw new Error(r.error)}catch(r){throw console.error("Password reset failed:",r),r}}getSession(){return this.session}getUser(){return this.user}getProfile(){return this.profile}isAuthenticated(){return!!this.session&&!!this.user&&!this.requiresMFA}requiresMultiFactorAuth(){return this.requiresMFA}async initialize(){await this.validateSession()}};Qt(en,"instance");let Xa=en;const xe=Xa.getInstance();/**
 * @license lucide-react v0.427.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */const rS=e=>e.replace(/([a-z0-9])([A-Z])/g,"$1-$2").toLowerCase(),Hm=(...e)=>e.filter((t,n,r)=>!!t&&r.indexOf(t)===n).join(" ");/**
 * @license lucide-react v0.427.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */var sS={xmlns:"http://www.w3.org/2000/svg",width:24,height:24,viewBox:"0 0 24 24",fill:"none",stroke:"currentColor",strokeWidth:2,strokeLinecap:"round",strokeLinejoin:"round"};/**
 * @license lucide-react v0.427.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */const iS=k.forwardRef(({color:e="currentColor",size:t=24,strokeWidth:n=2,absoluteStrokeWidth:r,className:s="",children:i,iconNode:o,...a},l)=>k.createElement("svg",{ref:l,...sS,width:t,height:t,stroke:e,strokeWidth:r?Number(n)*24/Number(t):n,className:Hm("lucide",s),...a},[...o.map(([u,c])=>k.createElement(u,c)),...Array.isArray(i)?i:[i]]));/**
 * @license lucide-react v0.427.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */const H=(e,t)=>{const n=k.forwardRef(({className:r,...s},i)=>k.createElement(iS,{ref:i,iconNode:t,className:Hm(`lucide-${rS(e)}`,r),...s}));return n.displayName=`${e}`,n};/**
 * @license lucide-react v0.427.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */const oS=H("ArrowLeft",[["path",{d:"m12 19-7-7 7-7",key:"1l729n"}],["path",{d:"M19 12H5",key:"x3x0zl"}]]);/**
 * @license lucide-react v0.427.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */const qi=H("Calendar",[["path",{d:"M8 2v4",key:"1cmpym"}],["path",{d:"M16 2v4",key:"4m81vk"}],["rect",{width:"18",height:"18",x:"3",y:"4",rx:"2",key:"1hopcy"}],["path",{d:"M3 10h18",key:"8toen8"}]]);/**
 * @license lucide-react v0.427.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */const aS=H("Check",[["path",{d:"M20 6 9 17l-5-5",key:"1gmf2c"}]]);/**
 * @license lucide-react v0.427.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */const ns=H("EyeOff",[["path",{d:"M10.733 5.076a10.744 10.744 0 0 1 11.205 6.575 1 1 0 0 1 0 .696 10.747 10.747 0 0 1-1.444 2.49",key:"ct8e1f"}],["path",{d:"M14.084 14.158a3 3 0 0 1-4.242-4.242",key:"151rxh"}],["path",{d:"M17.479 17.499a10.75 10.75 0 0 1-15.417-5.151 1 1 0 0 1 0-.696 10.75 10.75 0 0 1 4.446-5.143",key:"13bj9a"}],["path",{d:"m2 2 20 20",key:"1ooewy"}]]);/**
 * @license lucide-react v0.427.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */const rs=H("Eye",[["path",{d:"M2.062 12.348a1 1 0 0 1 0-.696 10.75 10.75 0 0 1 19.876 0 1 1 0 0 1 0 .696 10.75 10.75 0 0 1-19.876 0",key:"1nclc0"}],["circle",{cx:"12",cy:"12",r:"3",key:"1v7zrd"}]]);/**
 * @license lucide-react v0.427.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */const lS=H("Flag",[["path",{d:"M4 15s1-1 4-1 5 2 8 2 4-1 4-1V3s-1 1-4 1-5-2-8-2-4 1-4 1z",key:"i9b6wo"}],["line",{x1:"4",x2:"4",y1:"22",y2:"15",key:"1cm3nv"}]]);/**
 * @license lucide-react v0.427.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */const uS=H("Folder",[["path",{d:"M20 20a2 2 0 0 0 2-2V8a2 2 0 0 0-2-2h-7.9a2 2 0 0 1-1.69-.9L9.6 3.9A2 2 0 0 0 7.93 3H4a2 2 0 0 0-2 2v13a2 2 0 0 0 2 2Z",key:"1kt360"}]]);/**
 * @license lucide-react v0.427.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */const cS=H("Heart",[["path",{d:"M19 14c1.49-1.46 3-3.21 3-5.5A5.5 5.5 0 0 0 16.5 3c-1.76 0-3 .5-4.5 2-1.5-1.5-2.74-2-4.5-2A5.5 5.5 0 0 0 2 8.5c0 2.3 1.5 4.05 3 5.5l7 7Z",key:"c3ymky"}]]);/**
 * @license lucide-react v0.427.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */const fS=H("House",[["path",{d:"M15 21v-8a1 1 0 0 0-1-1h-4a1 1 0 0 0-1 1v8",key:"5wwlr5"}],["path",{d:"M3 10a2 2 0 0 1 .709-1.528l7-5.999a2 2 0 0 1 2.582 0l7 5.999A2 2 0 0 1 21 10v9a2 2 0 0 1-2 2H5a2 2 0 0 1-2-2z",key:"1d0kgt"}]]);/**
 * @license lucide-react v0.427.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */const dS=H("List",[["line",{x1:"8",x2:"21",y1:"6",y2:"6",key:"7ey8pc"}],["line",{x1:"8",x2:"21",y1:"12",y2:"12",key:"rjfblc"}],["line",{x1:"8",x2:"21",y1:"18",y2:"18",key:"c3b1m8"}],["line",{x1:"3",x2:"3.01",y1:"6",y2:"6",key:"1g7gq3"}],["line",{x1:"3",x2:"3.01",y1:"12",y2:"12",key:"1pjlvk"}],["line",{x1:"3",x2:"3.01",y1:"18",y2:"18",key:"28t2mc"}]]);/**
 * @license lucide-react v0.427.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */const un=H("Lock",[["rect",{width:"18",height:"11",x:"3",y:"11",rx:"2",ry:"2",key:"1w4ew1"}],["path",{d:"M7 11V7a5 5 0 0 1 10 0v4",key:"fwvmzm"}]]);/**
 * @license lucide-react v0.427.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */const Tu=H("Mail",[["rect",{width:"20",height:"16",x:"2",y:"4",rx:"2",key:"18n3k1"}],["path",{d:"m22 7-8.97 5.7a1.94 1.94 0 0 1-2.06 0L2 7",key:"1ocrg3"}]]);/**
 * @license lucide-react v0.427.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */const hS=H("Moon",[["path",{d:"M12 3a6 6 0 0 0 9 9 9 9 0 1 1-9-9Z",key:"a7tn18"}]]);/**
 * @license lucide-react v0.427.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */const pS=H("PenLine",[["path",{d:"M12 20h9",key:"t2du7b"}],["path",{d:"M16.376 3.622a1 1 0 0 1 3.002 3.002L7.368 18.635a2 2 0 0 1-.855.506l-2.872.838a.5.5 0 0 1-.62-.62l.838-2.872a2 2 0 0 1 .506-.854z",key:"1ykcvy"}]]);/**
 * @license lucide-react v0.427.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */const Wm=H("Plus",[["path",{d:"M5 12h14",key:"1ays0h"}],["path",{d:"M12 5v14",key:"s699le"}]]);/**
 * @license lucide-react v0.427.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */const mS=H("Save",[["path",{d:"M15.2 3a2 2 0 0 1 1.4.6l3.8 3.8a2 2 0 0 1 .6 1.4V19a2 2 0 0 1-2 2H5a2 2 0 0 1-2-2V5a2 2 0 0 1 2-2z",key:"1c8476"}],["path",{d:"M17 21v-7a1 1 0 0 0-1-1H8a1 1 0 0 0-1 1v7",key:"1ydtos"}],["path",{d:"M7 3v4a1 1 0 0 0 1 1h7",key:"t51u73"}]]);/**
 * @license lucide-react v0.427.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */const yS=H("Settings",[["path",{d:"M12.22 2h-.44a2 2 0 0 0-2 2v.18a2 2 0 0 1-1 1.73l-.43.25a2 2 0 0 1-2 0l-.15-.08a2 2 0 0 0-2.73.73l-.22.38a2 2 0 0 0 .73 2.73l.15.1a2 2 0 0 1 1 1.72v.51a2 2 0 0 1-1 1.74l-.15.09a2 2 0 0 0-.73 2.73l.22.38a2 2 0 0 0 2.73.73l.15-.08a2 2 0 0 1 2 0l.43.25a2 2 0 0 1 1 1.73V20a2 2 0 0 0 2 2h.44a2 2 0 0 0 2-2v-.18a2 2 0 0 1 1-1.73l.43-.25a2 2 0 0 1 2 0l.15.08a2 2 0 0 0 2.73-.73l.22-.39a2 2 0 0 0-.73-2.73l-.15-.08a2 2 0 0 1-1-1.74v-.5a2 2 0 0 1 1-1.74l.15-.09a2 2 0 0 0 .73-2.73l-.22-.38a2 2 0 0 0-2.73-.73l-.15.08a2 2 0 0 1-2 0l-.43-.25a2 2 0 0 1-1-1.73V4a2 2 0 0 0-2-2z",key:"1qme2f"}],["circle",{cx:"12",cy:"12",r:"3",key:"1v7zrd"}]]);/**
 * @license lucide-react v0.427.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */const gS=H("Shield",[["path",{d:"M20 13c0 5-3.5 7.5-7.66 8.95a1 1 0 0 1-.67-.01C7.5 20.5 4 18 4 13V6a1 1 0 0 1 1-1c2 0 4.5-1.2 6.24-2.72a1.17 1.17 0 0 1 1.52 0C14.51 3.81 17 5 19 5a1 1 0 0 1 1 1z",key:"oel41y"}]]);/**
 * @license lucide-react v0.427.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */const vS=H("Smartphone",[["rect",{width:"14",height:"20",x:"5",y:"2",rx:"2",ry:"2",key:"1yt0o3"}],["path",{d:"M12 18h.01",key:"mhygvu"}]]);/**
 * @license lucide-react v0.427.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */const xS=H("Sun",[["circle",{cx:"12",cy:"12",r:"4",key:"4exip2"}],["path",{d:"M12 2v2",key:"tus03m"}],["path",{d:"M12 20v2",key:"1lh1kg"}],["path",{d:"m4.93 4.93 1.41 1.41",key:"149t6j"}],["path",{d:"m17.66 17.66 1.41 1.41",key:"ptbguv"}],["path",{d:"M2 12h2",key:"1t8f8n"}],["path",{d:"M20 12h2",key:"1q8mjw"}],["path",{d:"m6.34 17.66-1.41 1.41",key:"1m8zz5"}],["path",{d:"m19.07 4.93-1.41 1.41",key:"1shlcs"}]]);/**
 * @license lucide-react v0.427.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */const Km=H("Tag",[["path",{d:"M12.586 2.586A2 2 0 0 0 11.172 2H4a2 2 0 0 0-2 2v7.172a2 2 0 0 0 .586 1.414l8.704 8.704a2.426 2.426 0 0 0 3.42 0l6.58-6.58a2.426 2.426 0 0 0 0-3.42z",key:"vktsd0"}],["circle",{cx:"7.5",cy:"7.5",r:".5",fill:"currentColor",key:"kqv944"}]]);/**
 * @license lucide-react v0.427.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */const wS=H("Trash2",[["path",{d:"M3 6h18",key:"d0wm0j"}],["path",{d:"M19 6v14c0 1-1 2-2 2H7c-1 0-2-1-2-2V6",key:"4alrt4"}],["path",{d:"M8 6V4c0-1 1-2 2-2h4c1 0 2 1 2 2v2",key:"v07s0e"}],["line",{x1:"10",x2:"10",y1:"11",y2:"17",key:"1uufr5"}],["line",{x1:"14",x2:"14",y1:"11",y2:"17",key:"xtxkd"}]]);/**
 * @license lucide-react v0.427.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */const Ut=H("User",[["path",{d:"M19 21v-2a4 4 0 0 0-4-4H9a4 4 0 0 0-4 4v2",key:"975kel"}],["circle",{cx:"12",cy:"7",r:"4",key:"17ys0d"}]]);/**
 * @license lucide-react v0.427.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */const Gm=H("X",[["path",{d:"M18 6 6 18",key:"1bl5f8"}],["path",{d:"m6 6 12 12",key:"d8bk6v"}]]);/**
 * @license lucide-react v0.427.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */const SS=H("Zap",[["path",{d:"M4 14a1 1 0 0 1-.78-1.63l9.9-10.2a.5.5 0 0 1 .86.46l-1.92 6.02A1 1 0 0 0 13 10h7a1 1 0 0 1 .78 1.63l-9.9 10.2a.5.5 0 0 1-.86-.46l1.92-6.02A1 1 0 0 0 11 14z",key:"1xq2db"}]]),kS=({onClose:e,onVerify:t})=>{const[n,r]=k.useState(""),[s,i]=k.useState(!1),[o,a]=k.useState(null),[l,u]=k.useState("totp"),c=async h=>{h.preventDefault(),i(!0),a(null);try{await t(n),e()}catch(g){a(g instanceof Error?g.message:"Verification failed")}finally{i(!1)}},d=async()=>{i(!0),a(null);try{await new Promise(h=>setTimeout(h,1e3)),a("Code sent successfully")}catch{a("Failed to send code")}finally{i(!1)}};return f.jsx(L.div,{initial:{opacity:0},animate:{opacity:1},exit:{opacity:0},className:"fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center p-4 z-50",onClick:h=>h.target===h.currentTarget&&e(),children:f.jsxs(L.div,{initial:{scale:.9,opacity:0},animate:{scale:1,opacity:1},exit:{scale:.9,opacity:0},className:"fa-glass-panel-frosted rounded-2xl p-6 w-full max-w-md",children:[f.jsxs("div",{className:"flex justify-between items-center mb-6",children:[f.jsx("h2",{className:"fa-heading-2 text-gray-800",children:"Two-Factor Authentication"}),f.jsx("button",{onClick:e,className:"text-gray-500 hover:text-gray-700",children:f.jsx(Gm,{className:"w-6 h-6"})})]}),o&&f.jsx(L.div,{initial:{opacity:0,y:-10},animate:{opacity:1,y:0},className:"mb-4 p-3 bg-red-50 border border-red-200 rounded-lg text-red-700 text-sm",children:o}),f.jsxs("div",{className:"mb-6",children:[f.jsx("p",{className:"fa-body text-gray-600 mb-4",children:"Enter the verification code from your authenticator app"}),f.jsxs("div",{className:"flex space-x-2 mb-6",children:[f.jsxs("button",{onClick:()=>u("totp"),className:`flex-1 py-2 px-3 rounded-lg text-sm font-medium flex items-center justify-center ${l==="totp"?"bg-blue-100 text-blue-700 border border-blue-200":"bg-white bg-opacity-20 text-gray-700"}`,children:[f.jsx(vS,{className:"w-4 h-4 mr-2"}),"Authenticator App"]}),f.jsxs("button",{onClick:()=>u("email"),className:`flex-1 py-2 px-3 rounded-lg text-sm font-medium flex items-center justify-center ${l==="email"?"bg-blue-100 text-blue-700 border border-blue-200":"bg-white bg-opacity-20 text-gray-700"}`,children:[f.jsx(Tu,{className:"w-4 h-4 mr-2"}),"Email"]})]})]}),f.jsxs("form",{onSubmit:c,className:"space-y-6",children:[f.jsxs("div",{children:[f.jsx("label",{htmlFor:"code",className:"block text-sm font-medium text-gray-700 mb-2",children:"Verification Code"}),f.jsx("input",{id:"code",type:"text",value:n,onChange:h=>r(h.target.value),className:"fa-input w-full text-center text-lg tracking-widest",placeholder:"000000",maxLength:6,required:!0})]}),f.jsxs("div",{className:"flex space-x-3",children:[f.jsx(L.button,{whileHover:{scale:1.02},whileTap:{scale:.98},type:"button",onClick:d,disabled:s,className:"flex-1 fa-button-glass py-3 px-4 rounded-lg font-medium text-gray-700 disabled:opacity-50 disabled:cursor-not-allowed",children:"Resend Code"}),f.jsx(L.button,{whileHover:{scale:1.02},whileTap:{scale:.98},type:"submit",disabled:s||n.length!==6,className:"flex-1 fa-button-primary py-3 px-4 rounded-lg font-medium text-white disabled:opacity-50 disabled:cursor-not-allowed",children:s?f.jsxs("div",{className:"flex items-center justify-center",children:[f.jsx("div",{className:"animate-spin rounded-full h-5 w-5 border-b-2 border-white mr-2"}),"Verifying..."]}):"Verify"})]})]}),f.jsx("div",{className:"mt-6 pt-6 border-t border-gray-200",children:f.jsxs("div",{className:"flex items-center text-sm text-gray-500",children:[f.jsx(gS,{className:"w-4 h-4 mr-2"}),f.jsx("span",{children:"Two-factor authentication adds an extra layer of security to your account"})]})})]})})},Qm=k.createContext(void 0),PS=({children:e})=>{const[t,n]=k.useState(null),[r,s]=k.useState(null),[i,o]=k.useState(null),[a,l]=k.useState(!1),[u,c]=k.useState(!1),[d,h]=k.useState(!1),[g,v]=k.useState(!0),[x,P]=k.useState(null);k.useEffect(()=>{(async()=>{try{v(!0),await xe.initialize();const D=xe.getSession(),A=xe.getUser(),O=xe.getProfile();n(D),s(A),o(O),l(!!D&&!!A),c(xe.requiresMultiFactorAuth())}catch(D){console.error("Auth initialization error:",D),P("Failed to initialize authentication")}finally{v(!1)}})()},[]);const m=async(T,D)=>{try{v(!0),P(null);const A={username:T,password:D},O=await xe.login(A),de=xe.getUser(),We=xe.getProfile();n(O),s(de),o(We),l(!0),c(!1)}catch(A){if(A instanceof Error&&A.message==="MFA_REQUIRED")c(!0),h(!0),P(null);else{const O=A instanceof Error?A.message:"Login failed";throw P(O),A}}finally{v(!1)}},p=async T=>{try{v(!0),P(null);const D=await xe.verifyMFA(T),A=xe.getUser(),O=xe.getProfile();n(D),s(A),o(O),l(!0),c(!1),h(!1)}catch(D){const A=D instanceof Error?D.message:"MFA verification failed";throw P(A),D}finally{v(!1)}},y=async(T,D,A,O)=>{try{v(!0),P(null);const de={username:T,password:D,email:A,fullName:O};await xe.register(de),await m(T,D)}catch(de){const We=de instanceof Error?de.message:"Registration failed";throw P(We),de}finally{v(!1)}},w=async()=>{try{v(!0),P(null),await xe.logout(),n(null),s(null),o(null),l(!1),c(!1),h(!1)}catch(T){const D=T instanceof Error?T.message:"Logout failed";P(D)}finally{v(!1)}},j={session:t,user:r,profile:i,isAuthenticated:a,requiresMFA:u,login:m,register:y,verifyMFA:p,logout:w,refreshSession:async()=>{try{v(!0),P(null);const T=await xe.refreshSession();T?(n(T),l(!0)):await w()}catch(T){const D=T instanceof Error?T.message:"Session refresh failed";P(D)}finally{v(!1)}},changePassword:async(T,D)=>{try{v(!0),P(null),await xe.changePassword(T,D)}catch(A){const O=A instanceof Error?A.message:"Password change failed";throw P(O),A}finally{v(!1)}},loading:g,error:x};return f.jsxs(Qm.Provider,{value:j,children:[e,d&&f.jsx(kS,{onClose:()=>h(!1),onVerify:p})]})},rr=()=>{const e=k.useContext(Qm);if(e===void 0)throw new Error("useAuth must be used within an AuthProvider");return e},TS=()=>{const{session:e,refreshSession:t,logout:n}=rr(),r=k.useCallback(async()=>{if(e){const s=new Date().getTime();if(new Date(e.expiresAt).getTime()-s<5*60*1e3)try{await t()}catch(o){console.error("Failed to refresh session:",o),await n()}}},[e,t,n]);return k.useEffect(()=>{r();const s=setInterval(r,60*1e3);return()=>clearInterval(s)},[r]),{session:e,checkSessionExpiry:r}},CS=k.createContext(void 0),ES=({children:e})=>{const[t,n]=k.useState(!1),[r,s]=k.useState(null);TS(),k.useEffect(()=>{(async()=>{try{if(window.electronAPI){const a=await window.electronAPI.system.getInfo();s(a)}n(!0)}catch(a){console.error("Failed to initialize application:",a),n(!0)}})()},[]);const i={isInitialized:t,electronAPI:window.electronAPI||null,systemInfo:r};return f.jsx(CS.Provider,{value:i,children:e})},Xm=k.createContext(void 0),jS=()=>{const e=k.useContext(Xm);if(!e)throw new Error("useTheme must be used within ThemeProvider");return e},NS=({children:e})=>{const[t,n]=k.useState("light");k.useEffect(()=>{const o=localStorage.getItem("fa-theme");if(o)n(o);else{const a=window.matchMedia("(prefers-color-scheme: dark)").matches?"dark":"light";n(a)}},[]),k.useEffect(()=>{document.documentElement.setAttribute("data-theme",t),localStorage.setItem("fa-theme",t),t==="dark"?document.body.style.background=`linear-gradient(135deg, 
        rgba(30, 58, 138, 0.1) 0%, 
        rgba(37, 99, 235, 0.05) 50%, 
        rgba(59, 130, 246, 0.1) 100%)`:document.body.style.background=`linear-gradient(135deg, 
        rgba(74, 144, 226, 0.1) 0%, 
        rgba(126, 211, 33, 0.05) 50%, 
        rgba(135, 206, 235, 0.1) 100%)`},[t]);const i={theme:t,toggleTheme:()=>{n(o=>o==="light"?"dark":"light")},setTheme:o=>{n(o)}};return f.jsx(Xm.Provider,{value:i,children:e})},AS=({onClose:e})=>{const{user:t,profile:n,changePassword:r}=rr(),[s,i]=k.useState(""),[o,a]=k.useState(""),[l,u]=k.useState(""),[c,d]=k.useState(!1),[h,g]=k.useState(null),[v,x]=k.useState(!1),P=async m=>{if(m.preventDefault(),o!==l){g({type:"error",text:"New passwords do not match"});return}if(o.length<12){g({type:"error",text:"Password must be at least 12 characters long"});return}x(!0),g(null);try{await r(s,o),g({type:"success",text:"Password changed successfully"}),i(""),a(""),u(""),d(!1)}catch(p){g({type:"error",text:p instanceof Error?p.message:"Failed to change password"})}finally{x(!1)}};return f.jsx(L.div,{initial:{opacity:0},animate:{opacity:1},exit:{opacity:0},className:"fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center p-4 z-50",onClick:m=>m.target===m.currentTarget&&e(),children:f.jsxs(L.div,{initial:{scale:.9,opacity:0},animate:{scale:1,opacity:1},exit:{scale:.9,opacity:0},className:"fa-glass-panel-frosted rounded-2xl p-6 w-full max-w-md",children:[f.jsxs("div",{className:"flex justify-between items-center mb-6",children:[f.jsx("h2",{className:"fa-heading-2 text-gray-800",children:"User Profile"}),f.jsx("button",{onClick:e,className:"text-gray-500 hover:text-gray-700",children:f.jsx(Gm,{className:"w-6 h-6"})})]}),h&&f.jsx(L.div,{initial:{opacity:0,y:-10},animate:{opacity:1,y:0},className:`mb-4 p-3 rounded-lg text-sm ${h.type==="success"?"bg-green-50 text-green-700 border border-green-200":"bg-red-50 text-red-700 border border-red-200"}`,children:h.text}),f.jsxs("div",{className:"space-y-6",children:[f.jsxs("div",{className:"flex flex-col items-center",children:[f.jsx("div",{className:"w-20 h-20 bg-gradient-to-br from-blue-400 to-aqua-400 rounded-full flex items-center justify-center mb-4",children:f.jsx(Ut,{className:"w-10 h-10 text-white"})}),f.jsx("h3",{className:"fa-heading-3 text-gray-800",children:t==null?void 0:t.username}),(n==null?void 0:n.full_name)&&f.jsx("p",{className:"fa-body text-gray-600",children:n.full_name})]}),f.jsxs("div",{className:"space-y-4",children:[f.jsxs("div",{className:"flex items-center p-3 bg-white bg-opacity-20 rounded-lg",children:[f.jsx(Ut,{className:"w-5 h-5 text-gray-500 mr-3"}),f.jsxs("div",{children:[f.jsx("p",{className:"fa-caption text-gray-500",children:"Username"}),f.jsx("p",{className:"fa-body text-gray-800",children:t==null?void 0:t.username})]})]}),(n==null?void 0:n.email)&&f.jsxs("div",{className:"flex items-center p-3 bg-white bg-opacity-20 rounded-lg",children:[f.jsx(Tu,{className:"w-5 h-5 text-gray-500 mr-3"}),f.jsxs("div",{children:[f.jsx("p",{className:"fa-caption text-gray-500",children:"Email"}),f.jsx("p",{className:"fa-body text-gray-800",children:n.email})]})]}),f.jsxs("div",{className:"flex items-center p-3 bg-white bg-opacity-20 rounded-lg",children:[f.jsx(qi,{className:"w-5 h-5 text-gray-500 mr-3"}),f.jsxs("div",{children:[f.jsx("p",{className:"fa-caption text-gray-500",children:"Member since"}),f.jsx("p",{className:"fa-body text-gray-800",children:t!=null&&t.created_at?new Date(t.created_at).toLocaleDateString():"Unknown"})]})]})]}),f.jsxs("div",{className:"pt-4",children:[f.jsxs("button",{onClick:()=>d(!c),className:"w-full fa-button-glass py-3 px-4 rounded-lg font-medium text-gray-700 flex items-center justify-center",children:[f.jsx(un,{className:"w-5 h-5 mr-2"}),c?"Cancel":"Change Password"]}),c&&f.jsxs(L.form,{initial:{opacity:0,height:0},animate:{opacity:1,height:"auto"},exit:{opacity:0,height:0},onSubmit:P,className:"mt-4 space-y-4",children:[f.jsxs("div",{children:[f.jsx("label",{htmlFor:"currentPassword",className:"block text-sm font-medium text-gray-700 mb-1",children:"Current Password"}),f.jsx("input",{id:"currentPassword",type:"password",value:s,onChange:m=>i(m.target.value),className:"fa-input w-full",required:!0})]}),f.jsxs("div",{children:[f.jsx("label",{htmlFor:"newPassword",className:"block text-sm font-medium text-gray-700 mb-1",children:"New Password"}),f.jsx("input",{id:"newPassword",type:"password",value:o,onChange:m=>a(m.target.value),className:"fa-input w-full",required:!0})]}),f.jsxs("div",{children:[f.jsx("label",{htmlFor:"confirmPassword",className:"block text-sm font-medium text-gray-700 mb-1",children:"Confirm New Password"}),f.jsx("input",{id:"confirmPassword",type:"password",value:l,onChange:m=>u(m.target.value),className:"fa-input w-full",required:!0})]}),f.jsx(L.button,{whileHover:{scale:1.02},whileTap:{scale:.98},type:"submit",disabled:v,className:"w-full fa-button-primary py-3 px-4 rounded-lg font-medium text-white flex items-center justify-center disabled:opacity-50 disabled:cursor-not-allowed",children:v?f.jsxs(f.Fragment,{children:[f.jsx("div",{className:"animate-spin rounded-full h-4 w-4 border-b-2 border-white mr-2"}),"Changing..."]}):f.jsxs(f.Fragment,{children:[f.jsx(mS,{className:"w-4 h-4 mr-2"}),"Save Changes"]})})]})]})]})]})})},MS=({systemInfo:e,onToggleTheme:t,theme:n})=>{const{user:r,logout:s}=rr(),[i,o]=k.useState(!1);return f.jsxs(f.Fragment,{children:[f.jsxs("div",{className:"w-full h-12 flex items-center justify-between px-4 bg-fa-white-glass backdrop-blur-xl border-b border-fa-white-frosted",children:[f.jsxs("div",{className:"flex items-center space-x-3",children:[f.jsx(L.button,{whileHover:{scale:1.05},whileTap:{scale:.95},className:"p-2 rounded-lg fa-hover-lift",onClick:t,children:n==="light"?f.jsx(xS,{className:"w-5 h-5 text-fa-blue-600"}):f.jsx(hS,{className:"w-5 h-5 text-fa-aqua-400"})}),f.jsx("h1",{className:"fa-heading-3 font-bold text-fa-gray-800",children:"Modern Todo"})]}),f.jsxs("div",{className:"flex items-center space-x-3",children:[r&&f.jsxs("div",{className:"flex items-center space-x-2",children:[f.jsxs(L.button,{whileHover:{scale:1.05},whileTap:{scale:.95},className:"p-2 rounded-lg fa-hover-lift flex items-center text-fa-gray-700",onClick:()=>o(!0),children:[f.jsx(Ut,{className:"w-5 h-5"}),f.jsx("span",{className:"ml-2 text-sm font-medium hidden md:inline",children:r.username})]}),f.jsx(L.button,{whileHover:{scale:1.05},whileTap:{scale:.95},className:"p-2 rounded-lg fa-hover-lift text-fa-gray-700",onClick:s,children:f.jsx("span",{className:"text-sm font-medium",children:"Logout"})})]}),f.jsxs("div",{className:"text-xs text-fa-gray-500",children:[e==null?void 0:e.platform," • ",e==null?void 0:e.version]})]})]}),i&&f.jsx(AS,{onClose:()=>o(!1)})]})},DS=()=>{const e=[{icon:fS,label:"Dashboard",active:!0},{icon:dS,label:"All Tasks"},{icon:qi,label:"Today"},{icon:uS,label:"Projects"},{icon:Km,label:"Tags"},{icon:cS,label:"Favorites"},{icon:SS,label:"Quick Add"}],t=[{name:"Personal",count:12},{name:"Work",count:8},{name:"Shopping",count:3},{name:"Health",count:5}];return f.jsxs("div",{className:"h-full fa-glass-panel rounded-r-2xl flex flex-col",children:[f.jsx("div",{className:"p-4 border-b border-fa-white-frosted",children:f.jsx("h2",{className:"fa-heading-3 font-bold text-fa-gray-800 mb-2",children:"Navigation"})}),f.jsxs("div",{className:"flex-1 overflow-y-auto p-2",children:[f.jsx("nav",{className:"space-y-1",children:e.map((n,r)=>{const s=n.icon;return f.jsxs(L.button,{whileHover:{x:4},whileTap:{scale:.98},className:`w-full flex items-center space-x-3 px-4 py-3 rounded-xl text-left transition-all ${n.active?"bg-fa-white-frosted text-fa-blue-600 shadow-md":"text-fa-gray-600 hover:bg-fa-white-glass hover:text-fa-gray-800"}`,children:[f.jsx(s,{className:"w-5 h-5"}),f.jsx("span",{className:"font-medium",children:n.label})]},r)})}),f.jsxs("div",{className:"mt-8",children:[f.jsxs("div",{className:"flex items-center justify-between px-4 mb-3",children:[f.jsx("h3",{className:"fa-body font-semibold text-fa-gray-700",children:"Categories"}),f.jsx(L.button,{whileHover:{scale:1.05},whileTap:{scale:.95},className:"p-1 rounded-lg text-fa-blue-500 hover:bg-fa-white-glass",children:f.jsx(Wm,{className:"w-4 h-4"})})]}),f.jsx("div",{className:"space-y-1",children:t.map((n,r)=>f.jsxs(L.button,{whileHover:{x:4},whileTap:{scale:.98},className:"w-full flex items-center justify-between px-4 py-2 rounded-lg text-left text-fa-gray-600 hover:bg-fa-white-glass hover:text-fa-gray-800",children:[f.jsx("span",{children:n.name}),f.jsx("span",{className:"text-xs bg-fa-white-glass px-2 py-1 rounded-full",children:n.count})]},r))})]})]}),f.jsx("div",{className:"p-4 border-t border-fa-white-frosted",children:f.jsxs(L.button,{whileHover:{scale:1.02},whileTap:{scale:.98},className:"w-full flex items-center space-x-3 px-4 py-3 rounded-xl text-fa-gray-600 hover:bg-fa-white-glass hover:text-fa-gray-800",children:[f.jsx(yS,{className:"w-5 h-5"}),f.jsx("span",{className:"font-medium",children:"Settings"})]})})]})},RS=({todo:e})=>{const[t,n]=k.useState(!1),[r,s]=k.useState(!1),[i,o]=k.useState(e.title),a=()=>{console.log("Toggle complete:",e.id)},l=()=>{console.log("Delete todo:",e.id)},u=()=>{s(!0)},c=()=>{console.log("Save edit:",e.id,i),s(!1)},d=()=>{switch(e.priority){case"high":return"text-fa-error";case"medium":return"text-fa-warning";case"low":return"text-fa-success";default:return"text-fa-gray-400"}},h=g=>g?g.toLocaleDateString("en-US",{month:"short",day:"numeric"}):"";return f.jsxs("div",{className:`fa-todo-card relative transition-all duration-300 ${e.completed?"opacity-70":""}`,onMouseEnter:()=>n(!0),onMouseLeave:()=>n(!1),children:[f.jsxs("div",{className:"flex items-start",children:[f.jsx(L.button,{whileHover:{scale:1.1},whileTap:{scale:.9},onClick:a,className:`flex-shrink-0 w-6 h-6 rounded-full border-2 flex items-center justify-center mr-4 mt-1 ${e.completed?"bg-fa-success border-fa-success text-white":"border-fa-gray-300 hover:border-fa-blue-400"}`,children:e.completed&&f.jsx(aS,{className:"w-4 h-4"})}),f.jsx("div",{className:"flex-1 min-w-0",children:r?f.jsx("input",{type:"text",value:i,onChange:g=>o(g.target.value),onBlur:c,onKeyDown:g=>g.key==="Enter"&&c(),className:"w-full bg-transparent text-fa-gray-800 focus:outline-none border-b border-fa-blue-300 pb-1",autoFocus:!0}):f.jsxs(f.Fragment,{children:[f.jsx("h3",{className:`text-lg font-medium ${e.completed?"line-through text-fa-gray-500":"text-fa-gray-800"}`,children:e.title}),f.jsxs("div",{className:"flex items-center space-x-4 mt-2",children:[e.category&&f.jsx("span",{className:"fa-caption bg-fa-white-glass px-2 py-1 rounded-full text-fa-gray-600",children:e.category}),e.dueDate&&f.jsxs("div",{className:"flex items-center text-fa-gray-500",children:[f.jsx(qi,{className:"w-4 h-4 mr-1"}),f.jsx("span",{className:"fa-caption",children:h(e.dueDate)})]}),f.jsx("div",{className:"flex items-center",children:f.jsx(lS,{className:`w-4 h-4 ${d()}`})})]})]})}),f.jsxs(L.div,{className:"flex items-center space-x-1 ml-2",initial:{opacity:0,width:0},animate:{opacity:t?1:0,width:t?"auto":0},children:[f.jsx(L.button,{whileHover:{scale:1.1},whileTap:{scale:.9},onClick:u,className:"p-1 text-fa-gray-400 hover:text-fa-blue-500",children:f.jsx(pS,{className:"w-4 h-4"})}),f.jsx(L.button,{whileHover:{scale:1.1},whileTap:{scale:.9},onClick:l,className:"p-1 text-fa-gray-400 hover:text-fa-error",children:f.jsx(wS,{className:"w-4 h-4"})})]})]}),e.priority==="high"&&f.jsx("div",{className:"absolute top-0 left-0 w-full h-1 bg-fa-error rounded-t-2xl"})]})},ed=[{id:"1",title:"Complete Frutiger Aero UI implementation",completed:!1,priority:"high",dueDate:new Date(Date.now()+864e5),category:"Work"},{id:"2",title:"Review design system documentation",completed:!0,priority:"medium",dueDate:new Date,category:"Documentation"},{id:"3",title:"Implement glassmorphism effects",completed:!1,priority:"medium",dueDate:new Date(Date.now()+1728e5),category:"Development"},{id:"4",title:"Test responsive design on mobile",completed:!1,priority:"low",dueDate:new Date(Date.now()+2592e5),category:"Testing"}],LS=()=>f.jsxs("div",{className:"w-full h-full overflow-y-auto",children:[f.jsx("div",{className:"space-y-3",children:ed.map((e,t)=>f.jsx(L.div,{initial:{opacity:0,y:20},animate:{opacity:1,y:0},transition:{delay:t*.1},children:f.jsx(RS,{todo:e})},e.id))}),ed.length===0&&f.jsx("div",{className:"h-full flex items-center justify-center",children:f.jsxs("div",{className:"text-center",children:[f.jsx("div",{className:"text-5xl mb-4",children:"🍃"}),f.jsx("h3",{className:"fa-heading-3 mb-2",children:"No tasks yet"}),f.jsx("p",{className:"fa-body text-fa-gray-500",children:"Add your first task to get started!"})]})})]}),VS=()=>{const[e,t]=k.useState(""),[n,r]=k.useState(!1),s=i=>{i.preventDefault(),e.trim()&&(console.log("Adding todo:",e),t(""))};return f.jsx("form",{onSubmit:s,className:"w-full",children:f.jsx("div",{className:`fa-glass-panel transition-all duration-300 ${n?"ring-2 ring-fa-blue-400 shadow-lg":"shadow-md"}`,children:f.jsxs("div",{className:"flex items-center",children:[f.jsx(L.button,{whileHover:{scale:1.05},whileTap:{scale:.95},type:"submit",className:"p-4 text-fa-blue-500 hover:text-fa-blue-600",children:f.jsx(Wm,{className:"w-5 h-5"})}),f.jsx("input",{type:"text",value:e,onChange:i=>t(i.target.value),onFocus:()=>r(!0),onBlur:()=>r(!1),placeholder:"What needs to be done?",className:"flex-1 bg-transparent py-4 px-2 text-fa-gray-800 placeholder-fa-gray-400 focus:outline-none"}),f.jsxs("div",{className:"flex items-center space-x-2 pr-4",children:[f.jsx(L.button,{whileHover:{scale:1.05},whileTap:{scale:.95},type:"button",className:"p-2 text-fa-gray-400 hover:text-fa-gray-600",children:f.jsx(qi,{className:"w-4 h-4"})}),f.jsx(L.button,{whileHover:{scale:1.05},whileTap:{scale:.95},type:"button",className:"p-2 text-fa-gray-400 hover:text-fa-gray-600",children:f.jsx(Km,{className:"w-4 h-4"})})]})]})})})},_S=({systemInfo:e})=>{const{theme:t,toggleTheme:n}=jS(),[r,s]=k.useState(!0);return f.jsxs("div",{className:"w-full h-full flex flex-col bg-transparent",children:[f.jsx(MS,{systemInfo:e,onToggleTheme:n,theme:t}),f.jsxs("div",{className:"flex flex-1 overflow-hidden",children:[f.jsx(L.div,{className:`h-full ${r?"w-64":"w-0"}`,animate:{width:r?256:0},transition:{duration:.3,ease:[.4,0,.2,1]},children:f.jsx(DS,{})}),f.jsx("div",{className:"flex-1 flex flex-col overflow-hidden p-6",children:f.jsxs("div",{className:"fa-glass-panel-frosted flex-1 flex flex-col rounded-2xl p-6",children:[f.jsxs("div",{className:"mb-6",children:[f.jsx("h1",{className:"fa-heading-1 mb-2",children:"My Tasks"}),f.jsx("p",{className:"fa-body text-fa-gray-600",children:"Stay organized and productive"})]}),f.jsx("div",{className:"mb-6",children:f.jsx(VS,{})}),f.jsx("div",{className:"flex-1 overflow-hidden",children:f.jsx(LS,{})})]})})]})]})},FS=()=>f.jsx("div",{className:"w-full h-full flex items-center justify-center bg-gradient-to-br from-fa-blue-50 to-fa-aqua-50",children:f.jsxs("div",{className:"text-center",children:[f.jsx(L.div,{className:"w-16 h-16 mx-auto mb-6 rounded-full border-4 border-fa-blue-500 border-t-transparent",animate:{rotate:360},transition:{duration:1,repeat:1/0,ease:"linear"}}),f.jsx(L.h1,{className:"fa-heading-1 mb-2",initial:{opacity:0,y:20},animate:{opacity:1,y:0},transition:{delay:.2},children:"Modern Todo"}),f.jsx(L.p,{className:"fa-caption text-fa-gray-600",initial:{opacity:0},animate:{opacity:1},transition:{delay:.4},children:"Loading your productivity experience..."})]})});class IS extends k.Component{constructor(){super(...arguments);Qt(this,"state",{hasError:!1,error:void 0})}static getDerivedStateFromError(n){return{hasError:!0,error:n}}componentDidCatch(n,r){console.error("Uncaught error:",n,r)}render(){var n;return this.state.hasError?f.jsx("div",{className:"w-full h-full flex items-center justify-center p-6",children:f.jsxs(L.div,{className:"fa-glass-panel-frosted p-8 max-w-md w-full text-center",initial:{opacity:0,y:20},animate:{opacity:1,y:0},transition:{duration:.5},children:[f.jsx("div",{className:"text-fa-error text-5xl mb-4",children:"⚠️"}),f.jsx("h2",{className:"fa-heading-2 mb-4",children:"Something went wrong"}),f.jsx("p",{className:"fa-body text-fa-gray-600 mb-6",children:((n=this.state.error)==null?void 0:n.message)||"An unexpected error occurred."}),f.jsx("button",{className:"fa-button-primary px-6 py-3 rounded-lg font-medium",onClick:()=>window.location.reload(),children:"Reload Application"})]})}):this.props.children}}const OS=({onSwitchToRegister:e})=>{const[t,n]=k.useState(""),[r,s]=k.useState(""),[i,o]=k.useState(!1),[a,l]=k.useState(!1),[u,c]=k.useState(null),{login:d}=rr(),h=async g=>{g.preventDefault(),l(!0),c(null);try{await d(t,r)}catch(v){c(v instanceof Error?v.message:"Login failed")}finally{l(!1)}};return f.jsx(L.div,{initial:{opacity:0,y:20},animate:{opacity:1,y:0},transition:{duration:.5},className:"w-full max-w-md",children:f.jsxs("div",{className:"fa-glass-panel-frosted rounded-2xl p-8 shadow-xl",children:[f.jsxs("div",{className:"text-center mb-8",children:[f.jsx(L.div,{initial:{scale:0},animate:{scale:1},transition:{delay:.2,type:"spring",stiffness:200},className:"w-16 h-16 bg-gradient-to-br from-blue-400 to-aqua-400 rounded-full flex items-center justify-center mx-auto mb-4",children:f.jsx(Ut,{className:"w-8 h-8 text-white"})}),f.jsx("h2",{className:"fa-heading-2 text-gray-800 mb-2",children:"Welcome Back"}),f.jsx("p",{className:"fa-body text-gray-600",children:"Sign in to your account"})]}),u&&f.jsx(L.div,{initial:{opacity:0,y:-10},animate:{opacity:1,y:0},className:"mb-6 p-3 bg-red-50 border border-red-200 rounded-lg text-red-700 text-sm",children:u}),f.jsxs("form",{onSubmit:h,className:"space-y-6",children:[f.jsxs("div",{children:[f.jsx("label",{htmlFor:"username",className:"block text-sm font-medium text-gray-700 mb-2",children:"Username"}),f.jsxs("div",{className:"relative",children:[f.jsx("div",{className:"absolute inset-y-0 left-0 pl-3 flex items-center pointer-events-none",children:f.jsx(Ut,{className:"h-5 w-5 text-gray-400"})}),f.jsx("input",{id:"username",type:"text",value:t,onChange:g=>n(g.target.value),className:"fa-input pl-10 w-full",placeholder:"Enter your username",required:!0})]})]}),f.jsxs("div",{children:[f.jsx("label",{htmlFor:"password",className:"block text-sm font-medium text-gray-700 mb-2",children:"Password"}),f.jsxs("div",{className:"relative",children:[f.jsx("div",{className:"absolute inset-y-0 left-0 pl-3 flex items-center pointer-events-none",children:f.jsx(un,{className:"h-5 w-5 text-gray-400"})}),f.jsx("input",{id:"password",type:i?"text":"password",value:r,onChange:g=>s(g.target.value),className:"fa-input pl-10 w-full pr-12",placeholder:"Enter your password",required:!0}),f.jsx("button",{type:"button",onClick:()=>o(!i),className:"absolute inset-y-0 right-0 pr-3 flex items-center text-gray-400 hover:text-gray-600",children:i?f.jsx(ns,{className:"h-5 w-5"}):f.jsx(rs,{className:"h-5 w-5"})})]})]}),f.jsxs("div",{className:"flex items-center justify-between",children:[f.jsxs("div",{className:"flex items-center",children:[f.jsx("input",{id:"remember-me",name:"remember-me",type:"checkbox",className:"h-4 w-4 text-blue-600 focus:ring-blue-500 border-gray-300 rounded"}),f.jsx("label",{htmlFor:"remember-me",className:"ml-2 block text-sm text-gray-700",children:"Remember me"})]}),f.jsx("div",{className:"text-sm",children:f.jsx("button",{type:"button",onClick:e,className:"font-medium text-blue-600 hover:text-blue-500",children:"Forgot password?"})})]}),f.jsx(L.button,{whileHover:{scale:1.02},whileTap:{scale:.98},type:"submit",disabled:a,className:"w-full fa-button-primary py-3 px-4 rounded-lg font-medium text-white shadow-lg disabled:opacity-50 disabled:cursor-not-allowed",children:a?f.jsxs("div",{className:"flex items-center justify-center",children:[f.jsx("div",{className:"animate-spin rounded-full h-5 w-5 border-b-2 border-white mr-2"}),"Signing in..."]}):"Sign in"})]}),f.jsx("div",{className:"mt-6 text-center",children:f.jsxs("p",{className:"text-sm text-gray-600",children:["Don't have an account?"," ",f.jsx("button",{onClick:e,className:"font-medium text-blue-600 hover:text-blue-500",children:"Sign up"})]})})]})})},zS=({onSwitchToLogin:e})=>{const[t,n]=k.useState(""),[r,s]=k.useState(""),[i,o]=k.useState(""),[a,l]=k.useState(""),[u,c]=k.useState(!1),[d,h]=k.useState(!1),[g,v]=k.useState(!1),[x,P]=k.useState(null),{register:m}=rr(),p=async y=>{if(y.preventDefault(),i!==a){P("Passwords do not match");return}v(!0),P(null);try{await m(t,i,r)}catch(w){P(w instanceof Error?w.message:"Registration failed")}finally{v(!1)}};return f.jsx(L.div,{initial:{opacity:0,y:20},animate:{opacity:1,y:0},transition:{duration:.5},className:"w-full max-w-md",children:f.jsxs("div",{className:"fa-glass-panel-frosted rounded-2xl p-8 shadow-xl",children:[f.jsxs("div",{className:"text-center mb-8",children:[f.jsx(L.div,{initial:{scale:0},animate:{scale:1},transition:{delay:.2,type:"spring",stiffness:200},className:"w-16 h-16 bg-gradient-to-br from-blue-400 to-aqua-400 rounded-full flex items-center justify-center mx-auto mb-4",children:f.jsx(Ut,{className:"w-8 h-8 text-white"})}),f.jsx("h2",{className:"fa-heading-2 text-gray-800 mb-2",children:"Create Account"}),f.jsx("p",{className:"fa-body text-gray-600",children:"Join our todo application"})]}),x&&f.jsx(L.div,{initial:{opacity:0,y:-10},animate:{opacity:1,y:0},className:"mb-6 p-3 bg-red-50 border border-red-200 rounded-lg text-red-700 text-sm",children:x}),f.jsxs("form",{onSubmit:p,className:"space-y-6",children:[f.jsxs("div",{children:[f.jsx("label",{htmlFor:"username",className:"block text-sm font-medium text-gray-700 mb-2",children:"Username"}),f.jsxs("div",{className:"relative",children:[f.jsx("div",{className:"absolute inset-y-0 left-0 pl-3 flex items-center pointer-events-none",children:f.jsx(Ut,{className:"h-5 w-5 text-gray-400"})}),f.jsx("input",{id:"username",type:"text",value:t,onChange:y=>n(y.target.value),className:"fa-input pl-10 w-full",placeholder:"Choose a username",required:!0})]})]}),f.jsxs("div",{children:[f.jsx("label",{htmlFor:"email",className:"block text-sm font-medium text-gray-700 mb-2",children:"Email Address"}),f.jsxs("div",{className:"relative",children:[f.jsx("div",{className:"absolute inset-y-0 left-0 pl-3 flex items-center pointer-events-none",children:f.jsx(Tu,{className:"h-5 w-5 text-gray-400"})}),f.jsx("input",{id:"email",type:"email",value:r,onChange:y=>s(y.target.value),className:"fa-input pl-10 w-full",placeholder:"Enter your email"})]})]}),f.jsxs("div",{children:[f.jsx("label",{htmlFor:"password",className:"block text-sm font-medium text-gray-700 mb-2",children:"Password"}),f.jsxs("div",{className:"relative",children:[f.jsx("div",{className:"absolute inset-y-0 left-0 pl-3 flex items-center pointer-events-none",children:f.jsx(un,{className:"h-5 w-5 text-gray-400"})}),f.jsx("input",{id:"password",type:u?"text":"password",value:i,onChange:y=>o(y.target.value),className:"fa-input pl-10 w-full pr-12",placeholder:"Create a strong password",required:!0}),f.jsx("button",{type:"button",onClick:()=>c(!u),className:"absolute inset-y-0 right-0 pr-3 flex items-center text-gray-400 hover:text-gray-600",children:u?f.jsx(ns,{className:"h-5 w-5"}):f.jsx(rs,{className:"h-5 w-5"})})]})]}),f.jsxs("div",{children:[f.jsx("label",{htmlFor:"confirmPassword",className:"block text-sm font-medium text-gray-700 mb-2",children:"Confirm Password"}),f.jsxs("div",{className:"relative",children:[f.jsx("div",{className:"absolute inset-y-0 left-0 pl-3 flex items-center pointer-events-none",children:f.jsx(un,{className:"h-5 w-5 text-gray-400"})}),f.jsx("input",{id:"confirmPassword",type:d?"text":"password",value:a,onChange:y=>l(y.target.value),className:"fa-input pl-10 w-full pr-12",placeholder:"Confirm your password",required:!0}),f.jsx("button",{type:"button",onClick:()=>h(!d),className:"absolute inset-y-0 right-0 pr-3 flex items-center text-gray-400 hover:text-gray-600",children:d?f.jsx(ns,{className:"h-5 w-5"}):f.jsx(rs,{className:"h-5 w-5"})})]})]}),f.jsx(L.button,{whileHover:{scale:1.02},whileTap:{scale:.98},type:"submit",disabled:g,className:"w-full fa-button-primary py-3 px-4 rounded-lg font-medium text-white shadow-lg disabled:opacity-50 disabled:cursor-not-allowed",children:g?f.jsxs("div",{className:"flex items-center justify-center",children:[f.jsx("div",{className:"animate-spin rounded-full h-5 w-5 border-b-2 border-white mr-2"}),"Creating account..."]}):"Create Account"})]}),f.jsx("div",{className:"mt-6 text-center",children:f.jsxs("p",{className:"text-sm text-gray-600",children:["Already have an account?"," ",f.jsx("button",{onClick:e,className:"font-medium text-blue-600 hover:text-blue-500",children:"Sign in"})]})})]})})},BS=({onBackToLogin:e})=>{const[t,n]=k.useState(""),[r,s]=k.useState(""),[i,o]=k.useState(""),[a,l]=k.useState(!1),[u,c]=k.useState(!1),[d,h]=k.useState("request"),[g,v]=k.useState(!1),[x,P]=k.useState(null),[m,p]=k.useState(null),y=async S=>{S.preventDefault(),v(!0),P(null),p(null);try{await new Promise(C=>setTimeout(C,1e3)),h("reset"),p("Reset instructions sent to your email")}catch{P("Failed to send reset instructions")}finally{v(!1)}},w=async S=>{if(S.preventDefault(),r!==i){P("Passwords do not match");return}if(r.length<12){P("Password must be at least 12 characters long");return}v(!0),P(null),p(null);try{await new Promise(C=>setTimeout(C,1e3)),p("Password reset successfully")}catch{P("Failed to reset password")}finally{v(!1)}};return f.jsx(L.div,{initial:{opacity:0,y:20},animate:{opacity:1,y:0},transition:{duration:.5},className:"w-full max-w-md",children:f.jsxs("div",{className:"fa-glass-panel-frosted rounded-2xl p-8 shadow-xl",children:[f.jsxs("div",{className:"text-center mb-8",children:[f.jsx(L.div,{initial:{scale:0},animate:{scale:1},transition:{delay:.2,type:"spring",stiffness:200},className:"w-16 h-16 bg-gradient-to-br from-blue-400 to-aqua-400 rounded-full flex items-center justify-center mx-auto mb-4",children:f.jsx(un,{className:"w-8 h-8 text-white"})}),f.jsx("h2",{className:"fa-heading-2 text-gray-800 mb-2",children:d==="request"?"Reset Password":"Set New Password"}),f.jsx("p",{className:"fa-body text-gray-600",children:d==="request"?"Enter your username to receive reset instructions":"Enter your new password"})]}),x&&f.jsx(L.div,{initial:{opacity:0,y:-10},animate:{opacity:1,y:0},className:"mb-6 p-3 bg-red-50 border border-red-200 rounded-lg text-red-700 text-sm",children:x}),m&&f.jsx(L.div,{initial:{opacity:0,y:-10},animate:{opacity:1,y:0},className:"mb-6 p-3 bg-green-50 border border-green-200 rounded-lg text-green-700 text-sm",children:m}),d==="request"?f.jsxs("form",{onSubmit:y,className:"space-y-6",children:[f.jsxs("div",{children:[f.jsx("label",{htmlFor:"username",className:"block text-sm font-medium text-gray-700 mb-2",children:"Username"}),f.jsxs("div",{className:"relative",children:[f.jsx("div",{className:"absolute inset-y-0 left-0 pl-3 flex items-center pointer-events-none",children:f.jsx(Ut,{className:"h-5 w-5 text-gray-400"})}),f.jsx("input",{id:"username",type:"text",value:t,onChange:S=>n(S.target.value),className:"fa-input pl-10 w-full",placeholder:"Enter your username",required:!0})]})]}),f.jsx(L.button,{whileHover:{scale:1.02},whileTap:{scale:.98},type:"submit",disabled:g,className:"w-full fa-button-primary py-3 px-4 rounded-lg font-medium text-white shadow-lg disabled:opacity-50 disabled:cursor-not-allowed",children:g?f.jsxs("div",{className:"flex items-center justify-center",children:[f.jsx("div",{className:"animate-spin rounded-full h-5 w-5 border-b-2 border-white mr-2"}),"Sending instructions..."]}):"Send Reset Instructions"})]}):f.jsxs("form",{onSubmit:w,className:"space-y-6",children:[f.jsxs("div",{children:[f.jsx("label",{htmlFor:"newPassword",className:"block text-sm font-medium text-gray-700 mb-2",children:"New Password"}),f.jsxs("div",{className:"relative",children:[f.jsx("div",{className:"absolute inset-y-0 left-0 pl-3 flex items-center pointer-events-none",children:f.jsx(un,{className:"h-5 w-5 text-gray-400"})}),f.jsx("input",{id:"newPassword",type:a?"text":"password",value:r,onChange:S=>s(S.target.value),className:"fa-input pl-10 w-full pr-12",placeholder:"Enter new password",required:!0}),f.jsx("button",{type:"button",onClick:()=>l(!a),className:"absolute inset-y-0 right-0 pr-3 flex items-center text-gray-400 hover:text-gray-600",children:a?f.jsx(ns,{className:"h-5 w-5"}):f.jsx(rs,{className:"h-5 w-5"})})]})]}),f.jsxs("div",{children:[f.jsx("label",{htmlFor:"confirmPassword",className:"block text-sm font-medium text-gray-700 mb-2",children:"Confirm New Password"}),f.jsxs("div",{className:"relative",children:[f.jsx("div",{className:"absolute inset-y-0 left-0 pl-3 flex items-center pointer-events-none",children:f.jsx(un,{className:"h-5 w-5 text-gray-400"})}),f.jsx("input",{id:"confirmPassword",type:u?"text":"password",value:i,onChange:S=>o(S.target.value),className:"fa-input pl-10 w-full pr-12",placeholder:"Confirm new password",required:!0}),f.jsx("button",{type:"button",onClick:()=>c(!u),className:"absolute inset-y-0 right-0 pr-3 flex items-center text-gray-400 hover:text-gray-600",children:u?f.jsx(ns,{className:"h-5 w-5"}):f.jsx(rs,{className:"h-5 w-5"})})]})]}),f.jsx(L.button,{whileHover:{scale:1.02},whileTap:{scale:.98},type:"submit",disabled:g,className:"w-full fa-button-primary py-3 px-4 rounded-lg font-medium text-white shadow-lg disabled:opacity-50 disabled:cursor-not-allowed",children:g?f.jsxs("div",{className:"flex items-center justify-center",children:[f.jsx("div",{className:"animate-spin rounded-full h-5 w-5 border-b-2 border-white mr-2"}),"Resetting password..."]}):"Reset Password"})]}),f.jsx("div",{className:"mt-6 text-center",children:f.jsxs("button",{onClick:e,className:"flex items-center justify-center mx-auto text-sm text-blue-600 hover:text-blue-500",children:[f.jsx(oS,{className:"w-4 h-4 mr-1"}),"Back to login"]})})]})})},US=()=>{const[e,t]=k.useState("login"),n=()=>{t("login")},r=()=>{t("reset")},s=()=>{t("login")};return f.jsx("div",{className:"min-h-screen flex items-center justify-center bg-gradient-to-br from-blue-50 via-aqua-50 to-blue-100 p-4",children:f.jsx(mp,{mode:"wait",children:f.jsxs(L.div,{initial:{opacity:0,x:e==="login"?-20:e==="register"?0:20},animate:{opacity:1,x:0},exit:{opacity:0,x:e==="login"?20:e==="register"?0:-20},transition:{duration:.3},className:"w-full",children:[e==="login"&&f.jsx(OS,{onSwitchToRegister:r}),e==="register"&&f.jsx(zS,{onSwitchToLogin:n}),e==="reset"&&f.jsx(BS,{onBackToLogin:s})]},e)})})},bS=({children:e})=>{const{isAuthenticated:t,loading:n}=rr();return n?f.jsx("div",{className:"min-h-screen flex items-center justify-center bg-gradient-to-br from-blue-50 via-aqua-50 to-blue-100",children:f.jsxs(L.div,{initial:{opacity:0},animate:{opacity:1},className:"text-center",children:[f.jsx("div",{className:"animate-spin rounded-full h-12 w-12 border-b-2 border-blue-500 mx-auto mb-4"}),f.jsx("p",{className:"text-gray-600",children:"Loading..."})]})}):t?f.jsx(f.Fragment,{children:e}):f.jsx(US,{})},$S=()=>{const[e,t]=k.useState(!0),[n,r]=k.useState(null);return k.useEffect(()=>{(async()=>{try{if(window.electronAPI){const i=await window.electronAPI.system.getInfo();r(i)}await new Promise(i=>setTimeout(i,1500)),t(!1)}catch(i){console.error("Failed to initialize app:",i),t(!1)}})()},[]),e?f.jsx(FS,{}):f.jsx(IS,{children:f.jsx(PS,{children:f.jsx(ES,{children:f.jsx(NS,{children:f.jsx(bS,{children:f.jsx(mp,{mode:"wait",children:f.jsx(L.div,{initial:{opacity:0,scale:.95},animate:{opacity:1,scale:1},transition:{duration:.6,ease:[.4,0,.2,1]},className:"w-full h-full",children:f.jsx(_S,{systemInfo:n})},"main-app")})})})})})})},Ym=document.getElementById("root");if(!Ym)throw new Error("Root element not found");zo.createRoot(Ym).render(f.jsx(yy.StrictMode,{children:f.jsx($S,{})}));
