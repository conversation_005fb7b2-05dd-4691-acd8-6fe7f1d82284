"use strict";
Object.defineProperty(exports, "__esModule", { value: true });
exports.BaseDAO = void 0;
const connection_1 = require("@main/database/connection");
const types_1 = require("@shared/types");
class BaseDAO {
    tableName;
    constructor(tableName) {
        this.tableName = tableName;
    }
    async executeQuery(query, params = []) {
        try {
            return await connection_1.dbConnection.executeQuery(query, params);
        }
        catch (error) {
            const errorMessage = error instanceof Error ? error.message : 'Unknown error';
            throw new types_1.DatabaseError(`Query failed for table ${this.tableName}: ${errorMessage}`, 'QUERY_ERROR', error);
        }
    }
    async executeTransaction(callback) {
        try {
            return await connection_1.dbConnection.executeTransaction(callback);
        }
        catch (error) {
            const errorMessage = error instanceof Error ? error.message : 'Unknown error';
            throw new types_1.DatabaseError(`Transaction failed for table ${this.tableName}: ${errorMessage}`, 'TRANSACTION_ERROR', error);
        }
    }
    async findById(id) {
        const result = await this.executeQuery(`SELECT * FROM ${this.tableName} WHERE id = $1`, [id]);
        return result.rows[0] || null;
    }
    async findAll(pagination, filters) {
        let whereClause = '';
        let params = [];
        let paramIndex = 1;
        // Build WHERE clause from filters
        if (filters) {
            const conditions = [];
            Object.entries(filters).forEach(([key, value]) => {
                if (value !== undefined && value !== null) {
                    if (Array.isArray(value)) {
                        // Handle array filters (e.g., tags)
                        conditions.push(`${key} && $${paramIndex}`);
                        params.push(value);
                    }
                    else if (key.includes('_from') || key.includes('_to')) {
                        // Handle date range filters
                        const baseKey = key.replace('_from', '').replace('_to', '');
                        if (key.includes('_from')) {
                            conditions.push(`${baseKey} >= $${paramIndex}`);
                        }
                        else {
                            conditions.push(`${baseKey} <= $${paramIndex}`);
                        }
                        params.push(value);
                    }
                    else {
                        conditions.push(`${key} = $${paramIndex}`);
                        params.push(value);
                    }
                    paramIndex++;
                }
            });
            if (conditions.length > 0) {
                whereClause = `WHERE ${conditions.join(' AND ')}`;
            }
        }
        // Get total count
        const countQuery = `SELECT COUNT(*) as total FROM ${this.tableName} ${whereClause}`;
        const countResult = await this.executeQuery(countQuery, params);
        const total = countResult.rows[0]?.total || 0;
        // Build main query with pagination
        let query = `SELECT * FROM ${this.tableName} ${whereClause}`;
        if (pagination?.sortBy) {
            const sortOrder = pagination.sortOrder || 'ASC';
            query += ` ORDER BY ${pagination.sortBy} ${sortOrder}`;
        }
        if (pagination?.limit) {
            query += ` LIMIT $${paramIndex}`;
            params.push(pagination.limit);
            paramIndex++;
            if (pagination.page && pagination.page > 1) {
                const offset = (pagination.page - 1) * pagination.limit;
                query += ` OFFSET $${paramIndex}`;
                params.push(offset);
            }
        }
        const result = await this.executeQuery(query, params);
        return {
            data: result.rows,
            total
        };
    }
    async create(data) {
        const fields = Object.keys(data).filter(key => data[key] !== undefined);
        const values = fields.map(key => data[key]);
        const placeholders = fields.map((_, index) => `$${index + 1}`).join(', ');
        const query = `
      INSERT INTO ${this.tableName} (${fields.join(', ')})
      VALUES (${placeholders})
      RETURNING *
    `;
        const result = await this.executeQuery(query, values);
        if (result.rows.length === 0) {
            throw new types_1.DatabaseError(`Failed to create record in ${this.tableName}`, 'CREATE_ERROR');
        }
        return result.rows[0];
    }
    async update(id, data) {
        const fields = Object.keys(data).filter(key => data[key] !== undefined && key !== 'id');
        if (fields.length === 0) {
            throw new types_1.DatabaseError('No fields to update', 'UPDATE_ERROR');
        }
        const setClause = fields.map((field, index) => `${field} = $${index + 1}`).join(', ');
        const values = fields.map(key => data[key]);
        values.push(id);
        const query = `
      UPDATE ${this.tableName}
      SET ${setClause}, updated_at = CURRENT_TIMESTAMP
      WHERE id = $${values.length}
      RETURNING *
    `;
        const result = await this.executeQuery(query, values);
        if (result.rows.length === 0) {
            throw new types_1.DatabaseError(`Record not found in ${this.tableName}`, 'NOT_FOUND');
        }
        return result.rows[0];
    }
    async delete(id) {
        const result = await this.executeQuery(`DELETE FROM ${this.tableName} WHERE id = $1`, [id]);
        return result.rowCount > 0;
    }
    async softDelete(id) {
        const result = await this.executeQuery(`UPDATE ${this.tableName} 
       SET is_deleted = TRUE, updated_at = CURRENT_TIMESTAMP 
       WHERE id = $1 
       RETURNING *`, [id]);
        if (result.rows.length === 0) {
            throw new types_1.DatabaseError(`Record not found in ${this.tableName}`, 'NOT_FOUND');
        }
        return result.rows[0];
    }
    async exists(id) {
        const result = await this.executeQuery(`SELECT EXISTS(SELECT 1 FROM ${this.tableName} WHERE id = $1) as exists`, [id]);
        return result.rows[0]?.exists || false;
    }
    async count(filters) {
        let whereClause = '';
        let params = [];
        if (filters) {
            const conditions = [];
            let paramIndex = 1;
            Object.entries(filters).forEach(([key, value]) => {
                if (value !== undefined && value !== null) {
                    conditions.push(`${key} = $${paramIndex}`);
                    params.push(value);
                    paramIndex++;
                }
            });
            if (conditions.length > 0) {
                whereClause = `WHERE ${conditions.join(' AND ')}`;
            }
        }
        const result = await this.executeQuery(`SELECT COUNT(*) as count FROM ${this.tableName} ${whereClause}`, params);
        return result.rows[0]?.count || 0;
    }
    buildWhereClause(filters) {
        const conditions = [];
        const params = [];
        let paramIndex = 1;
        Object.entries(filters).forEach(([key, value]) => {
            if (value !== undefined && value !== null) {
                if (Array.isArray(value)) {
                    conditions.push(`${key} = ANY($${paramIndex})`);
                }
                else {
                    conditions.push(`${key} = $${paramIndex}`);
                }
                params.push(value);
                paramIndex++;
            }
        });
        const clause = conditions.length > 0 ? `WHERE ${conditions.join(' AND ')}` : '';
        return { clause, params };
    }
    generateUUID() {
        return 'xxxxxxxx-xxxx-4xxx-yxxx-xxxxxxxxxxxx'.replace(/[xy]/g, function (c) {
            const r = Math.random() * 16 | 0;
            const v = c == 'x' ? r : (r & 0x3 | 0x8);
            return v.toString(16);
        });
    }
}
exports.BaseDAO = BaseDAO;
