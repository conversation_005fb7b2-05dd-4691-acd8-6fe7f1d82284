import { QueryResult, PaginationOptions, FilterOptions } from '@shared/types';
export declare abstract class BaseDAO<T> {
    protected tableName: string;
    constructor(tableName: string);
    protected executeQuery<R = any>(query: string, params?: any[]): Promise<QueryResult<R>>;
    protected executeTransaction<R>(callback: (execute: (query: string, params?: any[]) => Promise<QueryResult>) => Promise<R>): Promise<R>;
    findById(id: string): Promise<T | null>;
    findAll(pagination?: PaginationOptions, filters?: FilterOptions): Promise<{
        data: T[];
        total: number;
    }>;
    create(data: Partial<T>): Promise<T>;
    update(id: string, data: Partial<T>): Promise<T>;
    delete(id: string): Promise<boolean>;
    softDelete(id: string): Promise<T>;
    exists(id: string): Promise<boolean>;
    count(filters?: FilterOptions): Promise<number>;
    protected buildWhereClause(filters: Record<string, any>): {
        clause: string;
        params: any[];
    };
    protected generateUUID(): string;
}
