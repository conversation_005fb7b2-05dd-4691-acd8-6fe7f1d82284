"use strict";
Object.defineProperty(exports, "__esModule", { value: true });
exports.todoDAO = exports.TodoDAO = void 0;
const base_dao_1 = require("./base.dao");
const types_1 = require("@shared/types");
class TodoDAO extends base_dao_1.BaseDAO {
    constructor() {
        super('todos');
    }
    async findByUserId(userId, pagination, filters) {
        let whereClause = 'WHERE user_id = $1 AND is_deleted = FALSE';
        let params = [userId];
        let paramIndex = 2;
        // Build additional filters
        if (filters) {
            if (filters.status) {
                whereClause += ` AND status = $${paramIndex}`;
                params.push(filters.status);
                paramIndex++;
            }
            if (filters.priority) {
                whereClause += ` AND priority = $${paramIndex}`;
                params.push(filters.priority);
                paramIndex++;
            }
            if (filters.category_id) {
                whereClause += ` AND category_id = $${paramIndex}`;
                params.push(filters.category_id);
                paramIndex++;
            }
            if (filters.due_date_from) {
                whereClause += ` AND due_date >= $${paramIndex}`;
                params.push(filters.due_date_from);
                paramIndex++;
            }
            if (filters.due_date_to) {
                whereClause += ` AND due_date <= $${paramIndex}`;
                params.push(filters.due_date_to);
                paramIndex++;
            }
            if (filters.tags && filters.tags.length > 0) {
                whereClause += ` AND tags && $${paramIndex}`;
                params.push(filters.tags);
                paramIndex++;
            }
            if (filters.search) {
                whereClause += ` AND (title ILIKE $${paramIndex} OR description ILIKE $${paramIndex})`;
                params.push(`%${filters.search}%`);
                paramIndex++;
            }
            if (filters.is_completed !== undefined) {
                if (filters.is_completed) {
                    whereClause += ` AND status = 'completed'`;
                }
                else {
                    whereClause += ` AND status != 'completed'`;
                }
            }
            if (filters.is_overdue) {
                whereClause += ` AND due_date < CURRENT_TIMESTAMP AND status NOT IN ('completed', 'cancelled')`;
            }
        }
        // Get total count
        const countQuery = `SELECT COUNT(*) as total FROM todos ${whereClause}`;
        const countResult = await this.executeQuery(countQuery, params);
        const total = countResult.rows[0]?.total || 0;
        // Build main query
        let query = `
      SELECT t.*, c.name as category_name, c.color as category_color
      FROM todos t
      LEFT JOIN categories c ON t.category_id = c.id
      ${whereClause}
    `;
        // Add ordering
        if (pagination?.sortBy) {
            const sortOrder = pagination.sortOrder || 'ASC';
            query += ` ORDER BY ${pagination.sortBy} ${sortOrder}`;
        }
        else {
            // Default ordering: by position, then by created_at
            query += ` ORDER BY position ASC, created_at DESC`;
        }
        // Add pagination
        if (pagination?.limit) {
            query += ` LIMIT $${paramIndex}`;
            params.push(pagination.limit);
            paramIndex++;
            if (pagination.page && pagination.page > 1) {
                const offset = (pagination.page - 1) * pagination.limit;
                query += ` OFFSET $${paramIndex}`;
                params.push(offset);
            }
        }
        const result = await this.executeQuery(query, params);
        return {
            data: result.rows,
            total
        };
    }
    async updateStatus(todoId, status, userId) {
        const updates = { status, updated_at: new Date() };
        // Set completed_at when status is completed
        if (status === 'completed') {
            updates.completed_at = new Date();
        }
        else {
            updates.completed_at = null;
        }
        const result = await this.executeQuery(`UPDATE todos 
       SET status = $1, completed_at = $2, updated_at = CURRENT_TIMESTAMP
       WHERE id = $3 AND user_id = $4 AND is_deleted = FALSE
       RETURNING *`, [status, updates.completed_at, todoId, userId]);
        if (result.rows.length === 0) {
            throw new types_1.DatabaseError('Todo not found or access denied', 'NOT_FOUND');
        }
        return result.rows[0];
    }
    async updatePriority(todoId, priority, userId) {
        const result = await this.executeQuery(`UPDATE todos 
       SET priority = $1, updated_at = CURRENT_TIMESTAMP
       WHERE id = $2 AND user_id = $3 AND is_deleted = FALSE
       RETURNING *`, [priority, todoId, userId]);
        if (result.rows.length === 0) {
            throw new types_1.DatabaseError('Todo not found or access denied', 'NOT_FOUND');
        }
        return result.rows[0];
    }
    async updatePosition(todoId, newPosition, userId) {
        return await this.executeTransaction(async (execute) => {
            // Get the current todo
            const currentResult = await execute('SELECT position, category_id FROM todos WHERE id = $1 AND user_id = $2 AND is_deleted = FALSE', [todoId, userId]);
            if (currentResult.rows.length === 0) {
                throw new types_1.DatabaseError('Todo not found or access denied', 'NOT_FOUND');
            }
            const currentTodo = currentResult.rows[0];
            const oldPosition = currentTodo.position;
            const categoryId = currentTodo.category_id;
            // Update positions of other todos in the same category
            if (newPosition > oldPosition) {
                // Moving down: shift todos up
                await execute(`UPDATE todos 
           SET position = position - 1, updated_at = CURRENT_TIMESTAMP
           WHERE user_id = $1 AND category_id = $2 AND position > $3 AND position <= $4 AND is_deleted = FALSE`, [userId, categoryId, oldPosition, newPosition]);
            }
            else if (newPosition < oldPosition) {
                // Moving up: shift todos down
                await execute(`UPDATE todos 
           SET position = position + 1, updated_at = CURRENT_TIMESTAMP
           WHERE user_id = $1 AND category_id = $2 AND position >= $3 AND position < $4 AND is_deleted = FALSE`, [userId, categoryId, newPosition, oldPosition]);
            }
            // Update the target todo's position
            const result = await execute(`UPDATE todos 
         SET position = $1, updated_at = CURRENT_TIMESTAMP
         WHERE id = $2 AND user_id = $3 AND is_deleted = FALSE
         RETURNING *`, [newPosition, todoId, userId]);
            return result.rows[0];
        });
    }
    async addTags(todoId, tags, userId) {
        const result = await this.executeQuery(`UPDATE todos 
       SET tags = array_cat(tags, $1), updated_at = CURRENT_TIMESTAMP
       WHERE id = $2 AND user_id = $3 AND is_deleted = FALSE
       RETURNING *`, [tags, todoId, userId]);
        if (result.rows.length === 0) {
            throw new types_1.DatabaseError('Todo not found or access denied', 'NOT_FOUND');
        }
        return result.rows[0];
    }
    async removeTags(todoId, tags, userId) {
        const result = await this.executeQuery(`UPDATE todos 
       SET tags = array_remove_all(tags, $1), updated_at = CURRENT_TIMESTAMP
       WHERE id = $2 AND user_id = $3 AND is_deleted = FALSE
       RETURNING *`, [tags, todoId, userId]);
        if (result.rows.length === 0) {
            throw new types_1.DatabaseError('Todo not found or access denied', 'NOT_FOUND');
        }
        return result.rows[0];
    }
    async getOverdueTodos(userId) {
        const result = await this.executeQuery(`SELECT t.*, c.name as category_name, c.color as category_color
       FROM todos t
       LEFT JOIN categories c ON t.category_id = c.id
       WHERE t.user_id = $1 
         AND t.due_date < CURRENT_TIMESTAMP 
         AND t.status NOT IN ('completed', 'cancelled')
         AND t.is_deleted = FALSE
       ORDER BY t.due_date ASC`, [userId]);
        return result.rows;
    }
    async getTodosWithReminders(beforeTime) {
        const time = beforeTime || new Date();
        const result = await this.executeQuery(`SELECT t.*, c.name as category_name, c.color as category_color
       FROM todos t
       LEFT JOIN categories c ON t.category_id = c.id
       WHERE t.reminder_at <= $1 
         AND t.status NOT IN ('completed', 'cancelled')
         AND t.is_deleted = FALSE
       ORDER BY t.reminder_at ASC`, [time]);
        return result.rows;
    }
    async getTodosByCategory(categoryId, userId) {
        const result = await this.executeQuery(`SELECT * FROM todos 
       WHERE category_id = $1 AND user_id = $2 AND is_deleted = FALSE
       ORDER BY position ASC, created_at DESC`, [categoryId, userId]);
        return result.rows;
    }
    async searchTodos(userId, searchTerm, limit = 20) {
        const result = await this.executeQuery(`SELECT t.*, c.name as category_name, c.color as category_color
       FROM todos t
       LEFT JOIN categories c ON t.category_id = c.id
       WHERE t.user_id = $1 
         AND t.is_deleted = FALSE
         AND (
           t.title ILIKE $2 
           OR t.description ILIKE $2 
           OR $3 = ANY(t.tags)
         )
       ORDER BY 
         CASE WHEN t.title ILIKE $2 THEN 1 ELSE 2 END,
         t.updated_at DESC
       LIMIT $4`, [userId, `%${searchTerm}%`, searchTerm, limit]);
        return result.rows;
    }
    async getUserTodoStats(userId) {
        const result = await this.executeQuery(`SELECT 
        COUNT(*) as total,
        COUNT(CASE WHEN status = 'completed' THEN 1 END) as completed,
        COUNT(CASE WHEN status = 'pending' THEN 1 END) as pending,
        COUNT(CASE WHEN status = 'in_progress' THEN 1 END) as in_progress,
        COUNT(CASE WHEN due_date < CURRENT_TIMESTAMP AND status NOT IN ('completed', 'cancelled') THEN 1 END) as overdue
       FROM todos 
       WHERE user_id = $1 AND is_deleted = FALSE`, [userId]);
        const stats = result.rows[0] || {
            total: 0,
            completed: 0,
            pending: 0,
            in_progress: 0,
            overdue: 0
        };
        const completionRate = stats.total > 0 ? (stats.completed / stats.total) * 100 : 0;
        return {
            total: stats.total,
            completed: stats.completed,
            pending: stats.pending,
            inProgress: stats.in_progress,
            overdue: stats.overdue,
            completionRate: Math.round(completionRate * 100) / 100,
        };
    }
    async getNextPosition(categoryId, userId) {
        const result = await this.executeQuery(`SELECT COALESCE(MAX(position), 0) + 1 as max_position
       FROM todos 
       WHERE user_id = $1 AND category_id = $2 AND is_deleted = FALSE`, [userId, categoryId]);
        return result.rows[0]?.max_position || 1;
    }
    async moveToCategory(todoId, newCategoryId, userId) {
        return await this.executeTransaction(async (execute) => {
            // Get next position in the new category
            const nextPosition = await this.getNextPosition(newCategoryId, userId);
            // Update the todo
            const result = await execute(`UPDATE todos 
         SET category_id = $1, position = $2, updated_at = CURRENT_TIMESTAMP
         WHERE id = $3 AND user_id = $4 AND is_deleted = FALSE
         RETURNING *`, [newCategoryId, nextPosition, todoId, userId]);
            if (result.rows.length === 0) {
                throw new types_1.DatabaseError('Todo not found or access denied', 'NOT_FOUND');
            }
            return result.rows[0];
        });
    }
}
exports.TodoDAO = TodoDAO;
exports.todoDAO = new TodoDAO();
