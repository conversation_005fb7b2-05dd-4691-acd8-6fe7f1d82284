"use strict";
var __importDefault = (this && this.__importDefault) || function (mod) {
    return (mod && mod.__esModule) ? mod : { "default": mod };
};
Object.defineProperty(exports, "__esModule", { value: true });
const electron_1 = require("electron");
const path_1 = require("path");
const environment_1 = require("./utils/environment");
const connection_1 = require("./database/connection");
const crypto_service_1 = require("./auth/crypto.service");
const service_1 = require("./mcp/service");
const electron_log_1 = __importDefault(require("electron-log"));
// Configure logging
electron_log_1.default.transports.file.level = 'info';
electron_log_1.default.transports.console.level = 'debug';
class ModernTodoApp {
    mainWindow = null;
    databaseService = null;
    securityService = null;
    mcpService = null;
    constructor() {
        this.setupApp();
        this.setupServices();
        this.setupIpcHandlers();
    }
    setupApp() {
        // Set app user model id for Windows
        electron_1.app.setAppUserModelId('com.moderntodo.app');
        // Handle app events
        electron_1.app.whenReady().then(() => {
            this.createMainWindow();
            this.setupMenu();
            electron_1.app.on('activate', () => {
                if (electron_1.BrowserWindow.getAllWindows().length === 0) {
                    this.createMainWindow();
                }
            });
        });
        electron_1.app.on('window-all-closed', () => {
            if (process.platform !== 'darwin') {
                electron_1.app.quit();
            }
        });
        electron_1.app.on('before-quit', async () => {
            await this.cleanup();
        });
    }
    async setupServices() {
        try {
            // Initialize core services
            this.securityService = new crypto_service_1.CryptographyService();
            this.databaseService = connection_1.DatabaseConnection.getInstance();
            this.mcpService = service_1.MCPService.getInstance();
            // Initialize services in order
            await this.databaseService.initialize();
            electron_log_1.default.info('Database service initialized');
            await this.mcpService.initialize();
            electron_log_1.default.info('MCP service initialized');
        }
        catch (error) {
            electron_log_1.default.error('Failed to initialize services:', error);
            electron_1.app.quit();
        }
    }
    createMainWindow() {
        this.mainWindow = new electron_1.BrowserWindow({
            width: 1200,
            height: 800,
            minWidth: 800,
            minHeight: 600,
            show: false,
            webPreferences: {
                nodeIntegration: false,
                contextIsolation: true,
                preload: (0, path_1.join)(__dirname, '../preload/index.js'),
                webSecurity: true,
                allowRunningInsecureContent: false,
            },
            titleBarStyle: 'hiddenInset',
            vibrancy: 'under-window', // macOS glassmorphism effect
            backgroundMaterial: 'acrylic', // Windows 11 glassmorphism
            transparent: true,
            frame: false,
        });
        // Load the renderer
        if ((0, environment_1.isDev)()) {
            this.mainWindow.loadURL('http://localhost:5173');
            this.mainWindow.webContents.openDevTools();
        }
        else {
            this.mainWindow.loadFile((0, path_1.join)(__dirname, '../renderer/index.html'));
        }
        // Show window when ready
        this.mainWindow.once('ready-to-show', () => {
            this.mainWindow?.show();
            if ((0, environment_1.isDev)()) {
                this.mainWindow?.webContents.openDevTools();
            }
        });
        // Handle window closed
        this.mainWindow.on('closed', () => {
            this.mainWindow = null;
        });
    }
    setupMenu() {
        const template = [
            {
                label: 'File',
                submenu: [
                    {
                        label: 'New Todo',
                        accelerator: 'CmdOrCtrl+N',
                        click: () => {
                            this.mainWindow?.webContents.send('menu-new-todo');
                        },
                    },
                    { type: 'separator' },
                    {
                        label: 'Import',
                        accelerator: 'CmdOrCtrl+I',
                        click: () => {
                            this.mainWindow?.webContents.send('menu-import');
                        },
                    },
                    {
                        label: 'Export',
                        accelerator: 'CmdOrCtrl+E',
                        click: () => {
                            this.mainWindow?.webContents.send('menu-export');
                        },
                    },
                    { type: 'separator' },
                    {
                        label: 'Quit',
                        accelerator: process.platform === 'darwin' ? 'Cmd+Q' : 'Ctrl+Q',
                        click: () => {
                            electron_1.app.quit();
                        },
                    },
                ],
            },
            {
                label: 'Edit',
                submenu: [
                    { role: 'undo' },
                    { role: 'redo' },
                    { type: 'separator' },
                    { role: 'cut' },
                    { role: 'copy' },
                    { role: 'paste' },
                    { role: 'selectAll' },
                ],
            },
            {
                label: 'View',
                submenu: [
                    { role: 'reload' },
                    { role: 'forceReload' },
                    { role: 'toggleDevTools' },
                    { type: 'separator' },
                    { role: 'resetZoom' },
                    { role: 'zoomIn' },
                    { role: 'zoomOut' },
                    { type: 'separator' },
                    { role: 'togglefullscreen' },
                ],
            },
            {
                label: 'Window',
                submenu: [
                    { role: 'minimize' },
                    { role: 'close' },
                ],
            },
        ];
        const menu = electron_1.Menu.buildFromTemplate(template);
        electron_1.Menu.setApplicationMenu(menu);
    }
    setupIpcHandlers() {
        // Database operations
        electron_1.ipcMain.handle('db:query', async (event, sql, params) => {
            try {
                return await this.databaseService?.executeQuery(sql, params);
            }
            catch (error) {
                electron_log_1.default.error('Database query error:', error);
                throw error;
            }
        });
        electron_1.ipcMain.handle('db:transaction', async (event, operations) => {
            try {
                return await this.databaseService?.executeTransaction(async (execute) => {
                    const results = [];
                    for (const op of operations) {
                        results.push(await execute(op.query, op.params));
                    }
                    return results;
                });
            }
            catch (error) {
                electron_log_1.default.error('Database transaction error:', error);
                throw error;
            }
        });
        // Security operations
        electron_1.ipcMain.handle('security:encrypt', async (event, data, password) => {
            try {
                return await this.securityService?.encryptData(data, password);
            }
            catch (error) {
                electron_log_1.default.error('Encryption error:', error);
                throw error;
            }
        });
        electron_1.ipcMain.handle('security:decrypt', async (event, encryptedData, password) => {
            try {
                return await this.securityService?.decryptData(encryptedData, password);
            }
            catch (error) {
                electron_log_1.default.error('Decryption error:', error);
                throw error;
            }
        });
        // MCP operations
        electron_1.ipcMain.handle('mcp:sync', async (event, data) => {
            try {
                return await this.mcpService?.executeQuery(data.sql, data.params);
            }
            catch (error) {
                electron_log_1.default.error('MCP sync error:', error);
                throw error;
            }
        });
        // System operations
        electron_1.ipcMain.handle('system:getInfo', async () => {
            return {
                platform: process.platform,
                arch: process.arch,
                version: electron_1.app.getVersion(),
                electronVersion: process.versions.electron,
                nodeVersion: process.versions.node,
            };
        });
        electron_1.ipcMain.handle('app:quit', () => {
            electron_1.app.quit();
        });
        electron_1.ipcMain.handle('app:minimize', () => {
            this.mainWindow?.minimize();
        });
        electron_1.ipcMain.handle('app:maximize', () => {
            if (this.mainWindow?.isMaximized()) {
                this.mainWindow.unmaximize();
            }
            else {
                this.mainWindow?.maximize();
            }
        });
        electron_1.ipcMain.handle('app:close', () => {
            this.mainWindow?.close();
        });
    }
    async cleanup() {
        try {
            await this.databaseService?.close();
            await this.mcpService?.disconnect();
            electron_log_1.default.info('Application cleanup completed');
        }
        catch (error) {
            electron_log_1.default.error('Cleanup error:', error);
        }
    }
}
// Create and initialize the application
new ModernTodoApp();
