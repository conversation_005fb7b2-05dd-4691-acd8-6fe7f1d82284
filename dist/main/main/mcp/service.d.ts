import { MCPToolOptions, ConnectionStatus, QueryResult } from '@shared/types';
export declare class MCPService {
    private static instance;
    private client;
    private transport;
    private connectionStatus;
    private connectionRetryCount;
    private maxRetries;
    private retryDelay;
    private constructor();
    static getInstance(): MCPService;
    initialize(): Promise<void>;
    private connect;
    private handleConnectionFailure;
    private setupEventHandlers;
    executeTool(options: MCPToolOptions): Promise<any>;
    private mockExecuteQuery;
    private mockCreateTable;
    private mockInsertData;
    private mockUpdateData;
    private mockDeleteData;
    executeQuery(sql: string, parameters?: any[]): Promise<QueryResult>;
    createTable(schema: any): Promise<any>;
    insertData(table: string, data: any[]): Promise<any>;
    updateData(table: string, updates: any, where: any): Promise<any>;
    deleteData(table: string, where: any): Promise<any>;
    getConnectionStatus(): ConnectionStatus;
    isConnected(): boolean;
    disconnect(): Promise<void>;
    healthCheck(): Promise<boolean>;
    reconnect(): Promise<void>;
}
export declare const mcpService: MCPService;
