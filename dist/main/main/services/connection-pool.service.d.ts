export interface PooledConnection {
    id: string;
    isActive: boolean;
    lastUsed: Date;
    useCount: number;
    healthStatus: 'healthy' | 'unhealthy' | 'unknown';
}
export interface PoolStats {
    totalConnections: number;
    activeConnections: number;
    idleConnections: number;
    unhealthyConnections: number;
    totalRequests: number;
    failedRequests: number;
    averageResponseTime: number;
}
export declare class ConnectionPoolService {
    private static instance;
    private pool;
    private config;
    private stats;
    private responseTimes;
    private healthCheckTimer;
    private cleanupTimer;
    private constructor();
    static getInstance(): ConnectionPoolService;
    initialize(): Promise<void>;
    getConnection(): Promise<string>;
    releaseConnection(connectionId: string): Promise<void>;
    executeWithConnection<T>(operation: () => Promise<T>, retryAttempts?: number): Promise<T>;
    private findIdleConnection;
    private createConnection;
    private waitForConnection;
    private ensureMinimumConnections;
    private testConnection;
    private handleConnectionError;
    private startHealthChecks;
    private startCleanup;
    private updateStats;
    private recordResponseTime;
    private generateConnectionId;
    private sleep;
    getStats(): PoolStats;
    getConnectionDetails(): PooledConnection[];
    forceReconnectAll(): Promise<void>;
    shutdown(): Promise<void>;
}
export declare const connectionPool: ConnectionPoolService;
