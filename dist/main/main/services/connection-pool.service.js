"use strict";
Object.defineProperty(exports, "__esModule", { value: true });
exports.connectionPool = exports.ConnectionPoolService = void 0;
const connection_1 = require("@main/database/connection");
const service_1 = require("@main/mcp/service");
const config_1 = require("@main/utils/config");
const types_1 = require("@shared/types");
class ConnectionPoolService {
    static instance;
    pool = new Map();
    config = config_1.config.getDatabaseConfig();
    stats = {
        totalConnections: 0,
        activeConnections: 0,
        idleConnections: 0,
        unhealthyConnections: 0,
        totalRequests: 0,
        failedRequests: 0,
        averageResponseTime: 0,
    };
    responseTimes = [];
    healthCheckTimer = null;
    cleanupTimer = null;
    constructor() {
        this.startHealthChecks();
        this.startCleanup();
    }
    static getInstance() {
        if (!ConnectionPoolService.instance) {
            ConnectionPoolService.instance = new ConnectionPoolService();
        }
        return ConnectionPoolService.instance;
    }
    async initialize() {
        try {
            console.log('Initializing connection pool...');
            // Initialize primary database connection
            await connection_1.dbConnection.initialize();
            // Initialize MCP service
            try {
                await service_1.mcpService.initialize();
                console.log('MCP service initialized successfully');
            }
            catch (error) {
                console.warn('MCP service initialization failed, continuing with local database only:', error);
            }
            // Create initial pool connections
            await this.ensureMinimumConnections();
            console.log('Connection pool initialized successfully');
        }
        catch (error) {
            console.error('Failed to initialize connection pool:', error);
            throw new types_1.DatabaseError('Connection pool initialization failed', 'POOL_INIT_ERROR', error);
        }
    }
    async getConnection() {
        const startTime = Date.now();
        try {
            this.stats.totalRequests++;
            // Find an idle connection
            let connectionId = this.findIdleConnection();
            if (!connectionId) {
                // Create new connection if under limit
                if (this.pool.size < this.config.maxConnections) {
                    connectionId = await this.createConnection();
                }
                else {
                    // Wait for a connection to become available
                    connectionId = await this.waitForConnection();
                }
            }
            // Mark connection as active
            const connection = this.pool.get(connectionId);
            if (connection) {
                connection.isActive = true;
                connection.lastUsed = new Date();
                connection.useCount++;
            }
            this.updateStats();
            this.recordResponseTime(Date.now() - startTime);
            return connectionId;
        }
        catch (error) {
            this.stats.failedRequests++;
            throw error;
        }
    }
    async releaseConnection(connectionId) {
        const connection = this.pool.get(connectionId);
        if (connection) {
            connection.isActive = false;
            connection.lastUsed = new Date();
            this.updateStats();
        }
    }
    async executeWithConnection(operation, retryAttempts = 3) {
        let lastError = null;
        for (let attempt = 1; attempt <= retryAttempts; attempt++) {
            const connectionId = await this.getConnection();
            try {
                const result = await operation();
                await this.releaseConnection(connectionId);
                return result;
            }
            catch (error) {
                await this.releaseConnection(connectionId);
                lastError = error instanceof Error ? error : new Error('Unknown error');
                if (attempt < retryAttempts) {
                    console.warn(`Operation failed (attempt ${attempt}/${retryAttempts}), retrying...`, error);
                    // Exponential backoff
                    const delay = Math.min(1000 * Math.pow(2, attempt - 1), 10000);
                    await this.sleep(delay);
                    // Check if we need to reconnect
                    if (error instanceof types_1.DatabaseError || error instanceof types_1.MCPError) {
                        await this.handleConnectionError(connectionId);
                    }
                }
            }
        }
        throw new types_1.DatabaseError(`Operation failed after ${retryAttempts} attempts: ${lastError?.message}`, 'OPERATION_FAILED', lastError);
    }
    findIdleConnection() {
        for (const [id, connection] of this.pool.entries()) {
            if (!connection.isActive && connection.healthStatus === 'healthy') {
                return id;
            }
        }
        return null;
    }
    async createConnection() {
        const connectionId = this.generateConnectionId();
        const connection = {
            id: connectionId,
            isActive: false,
            lastUsed: new Date(),
            useCount: 0,
            healthStatus: 'unknown',
        };
        this.pool.set(connectionId, connection);
        // Test the connection
        try {
            await this.testConnection(connectionId);
            connection.healthStatus = 'healthy';
        }
        catch (error) {
            connection.healthStatus = 'unhealthy';
            console.warn(`Failed to create healthy connection ${connectionId}:`, error);
        }
        return connectionId;
    }
    async waitForConnection() {
        return new Promise((resolve, reject) => {
            const timeout = setTimeout(() => {
                reject(new types_1.DatabaseError('Connection timeout', 'CONNECTION_TIMEOUT'));
            }, this.config.connectionTimeout);
            const checkForConnection = () => {
                const connectionId = this.findIdleConnection();
                if (connectionId) {
                    clearTimeout(timeout);
                    resolve(connectionId);
                }
                else {
                    setTimeout(checkForConnection, 100);
                }
            };
            checkForConnection();
        });
    }
    async ensureMinimumConnections() {
        const currentConnections = this.pool.size;
        const minConnections = this.config.minConnections;
        if (currentConnections < minConnections) {
            const connectionsToCreate = minConnections - currentConnections;
            for (let i = 0; i < connectionsToCreate; i++) {
                try {
                    await this.createConnection();
                }
                catch (error) {
                    console.warn(`Failed to create minimum connection ${i + 1}:`, error);
                }
            }
        }
    }
    async testConnection(connectionId) {
        // Test database connection
        const isDbHealthy = await connection_1.dbConnection.healthCheck();
        if (!isDbHealthy) {
            throw new types_1.DatabaseError('Database health check failed', 'HEALTH_CHECK_FAILED');
        }
        // Test MCP connection if available
        try {
            const isMcpHealthy = await service_1.mcpService.healthCheck();
            if (!isMcpHealthy) {
                console.warn('MCP health check failed for connection', connectionId);
                // Don't throw here as MCP is optional
            }
        }
        catch (error) {
            console.warn('MCP health check error:', error);
        }
    }
    async handleConnectionError(connectionId) {
        const connection = this.pool.get(connectionId);
        if (connection) {
            connection.healthStatus = 'unhealthy';
        }
        // Try to reconnect the database
        try {
            await connection_1.dbConnection.reconnect();
            console.log('Database reconnected successfully');
            if (connection) {
                connection.healthStatus = 'healthy';
            }
        }
        catch (error) {
            console.error('Failed to reconnect database:', error);
        }
        // Try to reconnect MCP service
        try {
            await service_1.mcpService.reconnect();
            console.log('MCP service reconnected successfully');
        }
        catch (error) {
            console.warn('Failed to reconnect MCP service:', error);
        }
    }
    startHealthChecks() {
        this.healthCheckTimer = setInterval(async () => {
            for (const [connectionId, connection] of this.pool.entries()) {
                if (!connection.isActive) {
                    try {
                        await this.testConnection(connectionId);
                        connection.healthStatus = 'healthy';
                    }
                    catch (error) {
                        connection.healthStatus = 'unhealthy';
                        console.warn(`Health check failed for connection ${connectionId}:`, error);
                    }
                }
            }
            this.updateStats();
        }, 60000); // Check every minute
    }
    startCleanup() {
        this.cleanupTimer = setInterval(() => {
            const maxIdleTime = 300000; // 5 minutes
            const now = Date.now();
            for (const [connectionId, connection] of this.pool.entries()) {
                const idleTime = now - connection.lastUsed.getTime();
                // Remove old idle connections (but keep minimum)
                if (!connection.isActive &&
                    idleTime > maxIdleTime &&
                    this.pool.size > this.config.minConnections) {
                    this.pool.delete(connectionId);
                    console.log(`Removed idle connection ${connectionId}`);
                }
            }
            // Clean up old response times
            if (this.responseTimes.length > 1000) {
                this.responseTimes = this.responseTimes.slice(-100);
            }
            this.updateStats();
        }, 120000); // Clean up every 2 minutes
    }
    updateStats() {
        this.stats.totalConnections = this.pool.size;
        this.stats.activeConnections = Array.from(this.pool.values()).filter(c => c.isActive).length;
        this.stats.idleConnections = this.stats.totalConnections - this.stats.activeConnections;
        this.stats.unhealthyConnections = Array.from(this.pool.values()).filter(c => c.healthStatus === 'unhealthy').length;
        if (this.responseTimes.length > 0) {
            this.stats.averageResponseTime = this.responseTimes.reduce((a, b) => a + b) / this.responseTimes.length;
        }
    }
    recordResponseTime(responseTime) {
        this.responseTimes.push(responseTime);
        if (this.responseTimes.length > 100) {
            this.responseTimes.shift();
        }
    }
    generateConnectionId() {
        return `conn_${Date.now()}_${Math.random().toString(36).substr(2, 9)}`;
    }
    sleep(ms) {
        return new Promise(resolve => setTimeout(resolve, ms));
    }
    getStats() {
        return { ...this.stats };
    }
    getConnectionDetails() {
        return Array.from(this.pool.values());
    }
    async forceReconnectAll() {
        console.log('Force reconnecting all connections...');
        // Clear all existing connections
        this.pool.clear();
        // Reconnect primary services
        await connection_1.dbConnection.reconnect();
        try {
            await service_1.mcpService.reconnect();
        }
        catch (error) {
            console.warn('MCP reconnection failed:', error);
        }
        // Recreate minimum connections
        await this.ensureMinimumConnections();
        console.log('All connections reconnected successfully');
    }
    async shutdown() {
        console.log('Shutting down connection pool...');
        if (this.healthCheckTimer) {
            clearInterval(this.healthCheckTimer);
            this.healthCheckTimer = null;
        }
        if (this.cleanupTimer) {
            clearInterval(this.cleanupTimer);
            this.cleanupTimer = null;
        }
        // Close all connections
        this.pool.clear();
        // Shutdown services
        await connection_1.dbConnection.close();
        await service_1.mcpService.disconnect();
        console.log('Connection pool shut down successfully');
    }
}
exports.ConnectionPoolService = ConnectionPoolService;
exports.connectionPool = ConnectionPoolService.getInstance();
