"use strict";
Object.defineProperty(exports, "__esModule", { value: true });
exports.runTests = runTests;
const init_1 = require("@main/database/init");
const connection_pool_service_1 = require("@main/services/connection-pool.service");
const auth_service_1 = require("@main/auth/auth.service");
const dao_1 = require("@main/dao");
const config_1 = require("@main/utils/config");
async function testDatabaseSetup() {
    console.log('🚀 Starting database setup test...\n');
    try {
        // Test 1: Configuration validation
        console.log('📋 Test 1: Configuration validation');
        config_1.config.validateConfig();
        console.log('✅ Configuration is valid\n');
        // Test 2: Database initialization
        console.log('🗄️  Test 2: Database initialization');
        await (0, init_1.initializeDatabase)();
        console.log('✅ Database initialized successfully\n');
        // Test 3: Connection pool initialization
        console.log('🏊 Test 3: Connection pool initialization');
        await connection_pool_service_1.connectionPool.initialize();
        const poolStats = connection_pool_service_1.connectionPool.getStats();
        console.log('✅ Connection pool initialized');
        console.log(`   📊 Pool stats: ${poolStats.totalConnections} connections\n`);
    }
    catch (error) {
        console.error('❌ Database setup failed:', error);
        throw error;
    }
}
async function testUserOperations() {
    console.log('👤 Testing user operations...\n');
    try {
        // Test user registration
        console.log('📝 Test: User registration');
        const registerData = {
            username: 'testuser',
            password: 'TestPassword123!',
            email: '<EMAIL>',
            fullName: 'Test User'
        };
        const { user, profile } = await auth_service_1.authService.register(registerData);
        console.log('✅ User registered successfully');
        console.log(`   👤 User ID: ${user.id}`);
        console.log(`   📧 Email: ${profile.email}\n`);
        // Test user login
        console.log('🔐 Test: User login');
        const loginCredentials = {
            username: 'testuser',
            password: 'TestPassword123!',
            deviceInfo: {
                platform: 'test',
                userAgent: 'test-agent'
            }
        };
        const session = await auth_service_1.authService.login(loginCredentials);
        console.log('✅ User login successful');
        console.log(`   🎫 Session ID: ${session.sessionId}`);
        console.log(`   ⏰ Expires: ${session.expiresAt}\n`);
        // Test session validation
        console.log('🔍 Test: Session validation');
        const validatedSession = await auth_service_1.authService.validateSession(session.sessionId);
        console.log('✅ Session validation successful');
        console.log(`   👤 Username: ${validatedSession?.username}\n`);
        return user.id;
    }
    catch (error) {
        console.error('❌ User operations failed:', error);
        throw error;
    }
}
async function testCategoryOperations(userId) {
    console.log('📁 Testing category operations...\n');
    try {
        // Test: Get default categories (should be created during user registration)
        console.log('📋 Test: Get default categories');
        const defaultCategories = await dao_1.dao.category.findByUserId(userId);
        console.log('✅ Default categories retrieved');
        console.log(`   📊 Count: ${defaultCategories.length} categories`);
        defaultCategories.forEach(cat => {
            console.log(`   • ${cat.name} (${cat.color})`);
        });
        console.log();
        // Test: Create new category
        console.log('➕ Test: Create new category');
        const newCategory = await dao_1.dao.category.createCategory(userId, 'Test Project', '#ff6b35', 'folder');
        console.log('✅ New category created');
        console.log(`   📁 Category: ${newCategory.name} (${newCategory.id})\n`);
        // Test: Update category
        console.log('✏️  Test: Update category');
        const updatedCategory = await dao_1.dao.category.updateCategory(newCategory.id, userId, { name: 'Updated Test Project', color: '#35ff6b' });
        console.log('✅ Category updated');
        console.log(`   📁 New name: ${updatedCategory.name}\n`);
        // Test: Get category stats
        console.log('📊 Test: Category stats');
        const categoryStats = await dao_1.dao.category.getCategoryStats(newCategory.id, userId);
        console.log('✅ Category stats retrieved');
        console.log(`   📊 Total todos: ${categoryStats.totalTodos}`);
        console.log(`   ✅ Completed: ${categoryStats.completedTodos}\n`);
        return newCategory.id;
    }
    catch (error) {
        console.error('❌ Category operations failed:', error);
        throw error;
    }
}
async function testTodoOperations(userId, categoryId) {
    console.log('📝 Testing todo operations...\n');
    try {
        // Test: Create todo
        console.log('➕ Test: Create todo');
        const newTodo = await dao_1.dao.todo.create({
            user_id: userId,
            category_id: categoryId,
            title: 'Test Todo Item',
            description: 'This is a test todo item for database testing',
            priority: 'high',
            status: 'pending',
            tags: ['test', 'database'],
            due_date: new Date(Date.now() + 86400000), // Tomorrow
            metadata: { source: 'test-script' }
        });
        console.log('✅ Todo created successfully');
        console.log(`   📝 Todo: ${newTodo.title} (${newTodo.id})`);
        console.log(`   🏷️  Tags: ${newTodo.tags.join(', ')}\n`);
        // Test: Update todo status
        console.log('🔄 Test: Update todo status');
        const updatedTodo = await dao_1.dao.todo.updateStatus(newTodo.id, 'in_progress', userId);
        console.log('✅ Todo status updated');
        console.log(`   📊 Status: ${updatedTodo.status}\n`);
        // Test: Get todos by user
        console.log('📋 Test: Get todos by user');
        const userTodos = await dao_1.dao.todo.findByUserId(userId, { page: 1, limit: 10 });
        console.log('✅ User todos retrieved');
        console.log(`   📊 Total: ${userTodos.total} todos`);
        userTodos.data.forEach(todo => {
            console.log(`   • ${todo.title} [${todo.status}]`);
        });
        console.log();
        // Test: Search todos
        console.log('🔍 Test: Search todos');
        const searchResults = await dao_1.dao.todo.searchTodos(userId, 'test');
        console.log('✅ Todo search completed');
        console.log(`   📊 Found: ${searchResults.length} matching todos\n`);
        // Test: Get user todo stats
        console.log('📊 Test: User todo statistics');
        const todoStats = await dao_1.dao.todo.getUserTodoStats(userId);
        console.log('✅ Todo stats retrieved');
        console.log(`   📊 Total: ${todoStats.total}`);
        console.log(`   ✅ Completed: ${todoStats.completed}`);
        console.log(`   ⏳ Pending: ${todoStats.pending}`);
        console.log(`   📈 Completion rate: ${todoStats.completionRate}%\n`);
        // Test: Complete todo
        console.log('✅ Test: Complete todo');
        const completedTodo = await dao_1.dao.todo.updateStatus(newTodo.id, 'completed', userId);
        console.log('✅ Todo marked as completed');
        console.log(`   ⏰ Completed at: ${completedTodo.completed_at}\n`);
    }
    catch (error) {
        console.error('❌ Todo operations failed:', error);
        throw error;
    }
}
async function testAdvancedFeatures(userId) {
    console.log('🚀 Testing advanced features...\n');
    try {
        // Test: Connection pool stats
        console.log('🏊 Test: Connection pool statistics');
        const poolStats = connection_pool_service_1.connectionPool.getStats();
        console.log('✅ Pool stats retrieved');
        console.log(`   🔗 Total connections: ${poolStats.totalConnections}`);
        console.log(`   🏃 Active: ${poolStats.activeConnections}`);
        console.log(`   💤 Idle: ${poolStats.idleConnections}`);
        console.log(`   📊 Total requests: ${poolStats.totalRequests}`);
        console.log(`   ⚡ Avg response time: ${poolStats.averageResponseTime.toFixed(2)}ms\n`);
        // Test: Database health check
        console.log('🏥 Test: Database health check');
        await connection_pool_service_1.connectionPool.executeWithConnection(async () => {
            const result = await dao_1.dao.user.findById(userId);
            return result;
        });
        console.log('✅ Database health check passed\n');
        // Test: Error recovery simulation
        console.log('🔄 Test: Error recovery simulation');
        try {
            await dao_1.dao.todo.findById('invalid-uuid-format');
        }
        catch (error) {
            console.log('✅ Error handling working correctly');
            const errorMessage = error instanceof Error ? error.message : 'Unknown error';
            console.log(`   ⚠️  Expected error caught: ${errorMessage}\n`);
        }
        // Test: Authentication session management
        console.log('🎫 Test: Session management');
        const activeSessions = auth_service_1.authService.getSessionCount();
        const activeUsers = auth_service_1.authService.getActiveUsers();
        console.log('✅ Session management working');
        console.log(`   📊 Active sessions: ${activeSessions}`);
        console.log(`   👥 Active users: ${activeUsers.join(', ')}\n`);
    }
    catch (error) {
        console.error('❌ Advanced features test failed:', error);
        throw error;
    }
}
async function cleanupTestData(userId) {
    console.log('🧹 Cleaning up test data...\n');
    try {
        // Soft delete all test todos
        const userTodos = await dao_1.dao.todo.findByUserId(userId);
        for (const todo of userTodos.data) {
            await dao_1.dao.todo.softDelete(todo.id);
        }
        // Clean up test categories (except default ones)
        const categories = await dao_1.dao.category.findByUserId(userId);
        for (const category of categories) {
            if (!category.is_default && category.name.includes('Test')) {
                await dao_1.dao.category.deleteCategory(category.id, userId);
            }
        }
        // Logout user sessions
        const userSessions = await auth_service_1.authService.getUserSessions(userId);
        for (const session of userSessions) {
            await auth_service_1.authService.logout(session.sessionId);
        }
        console.log('✅ Test data cleanup completed\n');
    }
    catch (error) {
        console.warn('⚠️  Cleanup failed (this is not critical):', error);
    }
}
async function runTests() {
    console.log('🧪 MotherDuck DuckDB Integration Test Suite');
    console.log('==========================================\n');
    const startTime = Date.now();
    let userId = '';
    try {
        // Run all tests
        await testDatabaseSetup();
        userId = await testUserOperations();
        const categoryId = await testCategoryOperations(userId);
        await testTodoOperations(userId, categoryId);
        await testAdvancedFeatures(userId);
        const endTime = Date.now();
        const duration = (endTime - startTime) / 1000;
        console.log('🎉 All tests completed successfully!');
        console.log(`⏱️  Total time: ${duration.toFixed(2)} seconds\n`);
        // Show final statistics
        const finalStats = connection_pool_service_1.connectionPool.getStats();
        console.log('📊 Final Statistics:');
        console.log(`   🔗 Database connections: ${finalStats.totalConnections}`);
        console.log(`   📊 Total requests: ${finalStats.totalRequests}`);
        console.log(`   ❌ Failed requests: ${finalStats.failedRequests}`);
        console.log(`   ⚡ Average response time: ${finalStats.averageResponseTime.toFixed(2)}ms`);
    }
    catch (error) {
        console.error('\n💥 Test suite failed:', error);
        throw error;
    }
    finally {
        // Always attempt cleanup
        if (userId) {
            await cleanupTestData(userId);
        }
        // Shutdown services
        console.log('🔚 Shutting down services...');
        await auth_service_1.authService.shutdown();
        await connection_pool_service_1.connectionPool.shutdown();
        console.log('✅ Services shut down successfully');
    }
}
// Run tests if this file is executed directly
if (require.main === module) {
    runTests()
        .then(() => {
        console.log('\n✅ Test suite completed successfully!');
        process.exit(0);
    })
        .catch((error) => {
        console.error('\n❌ Test suite failed:', error);
        process.exit(1);
    });
}
