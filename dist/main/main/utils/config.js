"use strict";
var __createBinding = (this && this.__createBinding) || (Object.create ? (function(o, m, k, k2) {
    if (k2 === undefined) k2 = k;
    var desc = Object.getOwnPropertyDescriptor(m, k);
    if (!desc || ("get" in desc ? !m.__esModule : desc.writable || desc.configurable)) {
      desc = { enumerable: true, get: function() { return m[k]; } };
    }
    Object.defineProperty(o, k2, desc);
}) : (function(o, m, k, k2) {
    if (k2 === undefined) k2 = k;
    o[k2] = m[k];
}));
var __setModuleDefault = (this && this.__setModuleDefault) || (Object.create ? (function(o, v) {
    Object.defineProperty(o, "default", { enumerable: true, value: v });
}) : function(o, v) {
    o["default"] = v;
});
var __importStar = (this && this.__importStar) || (function () {
    var ownKeys = function(o) {
        ownKeys = Object.getOwnPropertyNames || function (o) {
            var ar = [];
            for (var k in o) if (Object.prototype.hasOwnProperty.call(o, k)) ar[ar.length] = k;
            return ar;
        };
        return ownKeys(o);
    };
    return function (mod) {
        if (mod && mod.__esModule) return mod;
        var result = {};
        if (mod != null) for (var k = ownKeys(mod), i = 0; i < k.length; i++) if (k[i] !== "default") __createBinding(result, mod, k[i]);
        __setModuleDefault(result, mod);
        return result;
    };
})();
Object.defineProperty(exports, "__esModule", { value: true });
exports.config = exports.ConfigService = void 0;
const dotenv = __importStar(require("dotenv"));
// Load environment variables
dotenv.config();
class ConfigService {
    static instance;
    constructor() { }
    static getInstance() {
        if (!ConfigService.instance) {
            ConfigService.instance = new ConfigService();
        }
        return ConfigService.instance;
    }
    getDatabaseConfig() {
        return {
            path: process.env.DATABASE_PATH || './data/todo.db',
            motherduckToken: process.env.MOTHERDUCK_TOKEN,
            connectionTimeout: parseInt(process.env.CONNECTION_TIMEOUT || '10000'),
            maxConnections: parseInt(process.env.MAX_CONNECTIONS || '5'),
            minConnections: parseInt(process.env.MIN_CONNECTIONS || '1'),
            enableWAL: process.env.ENABLE_WAL === 'true',
            enableEncryption: process.env.ENCRYPTION_ENABLED === 'true',
        };
    }
    getMCPConfig() {
        const motherduckToken = process.env.MOTHERDUCK_TOKEN;
        if (!motherduckToken) {
            throw new Error('MOTHERDUCK_TOKEN environment variable is required');
        }
        return {
            motherduckToken,
            databaseName: process.env.DATABASE_NAME || 'todo_app',
            connectionTimeout: parseInt(process.env.CONNECTION_TIMEOUT || '10000'),
            retryAttempts: parseInt(process.env.MCP_RETRY_ATTEMPTS || '3'),
            retryDelay: parseInt(process.env.MCP_RETRY_DELAY || '1000'),
            enableMetrics: process.env.ENABLE_MCP_METRICS === 'true',
            logLevel: process.env.LOG_LEVEL || 'info',
        };
    }
    getAppConfig() {
        return {
            nodeEnv: process.env.NODE_ENV || 'development',
            logLevel: process.env.LOG_LEVEL || 'debug',
            sessionTimeout: parseInt(process.env.SESSION_TIMEOUT || '86400000'),
            maxLoginAttempts: parseInt(process.env.MAX_LOGIN_ATTEMPTS || '5'),
            lockoutDuration: parseInt(process.env.LOCKOUT_DURATION || '900000'),
            syncInterval: parseInt(process.env.SYNC_INTERVAL || '30000'),
            enableOfflineMode: process.env.ENABLE_OFFLINE_MODE === 'true',
            autoSync: process.env.AUTO_SYNC === 'true',
            enableDevtools: process.env.ENABLE_DEVTOOLS === 'true',
        };
    }
    getSecurityConfig() {
        return {
            encryptionEnabled: process.env.ENCRYPTION_ENABLED === 'true',
            require2FA: process.env.REQUIRE_2FA === 'true',
            enableBiometric: process.env.ENABLE_BIOMETRIC === 'true',
            passwordMinLength: parseInt(process.env.PASSWORD_MIN_LENGTH || '12'),
            sessionTimeout: parseInt(process.env.SESSION_TIMEOUT || '86400000'),
            maxLoginAttempts: parseInt(process.env.MAX_LOGIN_ATTEMPTS || '5'),
            lockoutDuration: parseInt(process.env.LOCKOUT_DURATION || '900000'),
        };
    }
    validateConfig() {
        const requiredEnvVars = ['DATABASE_NAME'];
        const missingVars = requiredEnvVars.filter(varName => !process.env[varName]);
        if (missingVars.length > 0) {
            throw new Error(`Missing required environment variables: ${missingVars.join(', ')}`);
        }
        // Warn about missing MotherDuck token for cloud features
        if (!process.env.MOTHERDUCK_TOKEN) {
            console.warn('Warning: MOTHERDUCK_TOKEN not set. Cloud sync features will be disabled.');
        }
    }
    isDevelopment() {
        return process.env.NODE_ENV === 'development';
    }
    isProduction() {
        return process.env.NODE_ENV === 'production';
    }
}
exports.ConfigService = ConfigService;
// Export singleton instance
exports.config = ConfigService.getInstance();
