import { DatabaseConfig, MCPConfig } from '@shared/types';
export declare class ConfigService {
    private static instance;
    private constructor();
    static getInstance(): ConfigService;
    getDatabaseConfig(): DatabaseConfig;
    getMCPConfig(): MCPConfig;
    getAppConfig(): {
        nodeEnv: string;
        logLevel: string;
        sessionTimeout: number;
        maxLoginAttempts: number;
        lockoutDuration: number;
        syncInterval: number;
        enableOfflineMode: boolean;
        autoSync: boolean;
        enableDevtools: boolean;
    };
    getSecurityConfig(): {
        encryptionEnabled: boolean;
        require2FA: boolean;
        enableBiometric: boolean;
        passwordMinLength: number;
        sessionTimeout: number;
        maxLoginAttempts: number;
        lockoutDuration: number;
    };
    validateConfig(): void;
    isDevelopment(): boolean;
    isProduction(): boolean;
}
export declare const config: ConfigService;
