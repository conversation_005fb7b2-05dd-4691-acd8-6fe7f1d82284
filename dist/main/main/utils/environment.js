"use strict";
Object.defineProperty(exports, "__esModule", { value: true });
exports.getLogsPath = exports.getUserDataPath = exports.getAppPath = exports.isDev = void 0;
const isDev = () => {
    return process.env.NODE_ENV === 'development' || !electron_1.app.isPackaged;
};
exports.isDev = isDev;
const getAppPath = () => {
    return electron_1.app.getAppPath();
};
exports.getAppPath = getAppPath;
const getUserDataPath = () => {
    return electron_1.app.getPath('userData');
};
exports.getUserDataPath = getUserDataPath;
const getLogsPath = () => {
    return electron_1.app.getPath('logs');
};
exports.getLogsPath = getLogsPath;
// Import app from electron
const electron_1 = require("electron");
