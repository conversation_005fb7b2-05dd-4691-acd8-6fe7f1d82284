export interface APIResponse<T = any> {
    success: boolean;
    data?: T;
    error?: string;
    timestamp: string;
}
export interface APIError {
    code: string;
    message: string;
    details?: any;
}
export declare const API_ROUTES: {
    readonly AUTH: {
        readonly LOGIN: "/api/auth/login";
        readonly REGISTER: "/api/auth/register";
        readonly LOGOUT: "/api/auth/logout";
        readonly REFRESH: "/api/auth/refresh";
        readonly PROFILE: "/api/auth/profile";
    };
    readonly TODOS: {
        readonly LIST: "/api/todos";
        readonly CREATE: "/api/todos";
        readonly UPDATE: "/api/todos/:id";
        readonly DELETE: "/api/todos/:id";
        readonly SEARCH: "/api/todos/search";
        readonly STATS: "/api/todos/stats";
    };
    readonly CATEGORIES: {
        readonly LIST: "/api/categories";
        readonly CREATE: "/api/categories";
        readonly UPDATE: "/api/categories/:id";
        readonly DELETE: "/api/categories/:id";
        readonly REORDER: "/api/categories/reorder";
    };
    readonly SYSTEM: {
        readonly STATUS: "/api/system/status";
        readonly HEALTH: "/api/system/health";
        readonly METRICS: "/api/system/metrics";
    };
};
export declare const IPC_CHANNELS: {
    readonly AUTH: {
        readonly LOGIN: "auth:login";
        readonly REGISTER: "auth:register";
        readonly LOGOUT: "auth:logout";
        readonly GET_SESSION: "auth:getSession";
    };
    readonly TODOS: {
        readonly GET_ALL: "todos:getAll";
        readonly CREATE: "todos:create";
        readonly UPDATE: "todos:update";
        readonly DELETE: "todos:delete";
        readonly SEARCH: "todos:search";
    };
    readonly CATEGORIES: {
        readonly GET_ALL: "categories:getAll";
        readonly CREATE: "categories:create";
        readonly UPDATE: "categories:update";
        readonly DELETE: "categories:delete";
    };
    readonly SYSTEM: {
        readonly GET_STATUS: "system:getStatus";
        readonly GET_METRICS: "system:getMetrics";
    };
};
export declare function createAPIResponse<T>(success: boolean, data?: T, error?: string): APIResponse<T>;
export interface FrontendContext {
    user: {
        id: string;
        username: string;
        email?: string;
        profile?: any;
    } | null;
    todos: any[];
    categories: any[];
    loading: boolean;
    error: string | null;
}
export interface AppState extends FrontendContext {
    auth: {
        isAuthenticated: boolean;
        session: any | null;
        loginLoading: boolean;
    };
    ui: {
        theme: 'light' | 'dark' | 'frutiger-aero';
        sidebarOpen: boolean;
        activeView: string;
    };
    sync: {
        status: 'idle' | 'syncing' | 'error' | 'offline';
        lastSyncAt: Date | null;
        pendingChanges: number;
    };
}
