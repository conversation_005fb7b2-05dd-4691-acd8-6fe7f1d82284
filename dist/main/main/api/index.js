"use strict";
// Future API layer for Electron/React integration
// This will be implemented in the next phase
Object.defineProperty(exports, "__esModule", { value: true });
exports.IPC_CHANNELS = exports.API_ROUTES = void 0;
exports.createAPIResponse = createAPIResponse;
// Future API endpoints structure
exports.API_ROUTES = {
    AUTH: {
        LOGIN: '/api/auth/login',
        REGISTER: '/api/auth/register',
        LOGOUT: '/api/auth/logout',
        REFRESH: '/api/auth/refresh',
        PROFILE: '/api/auth/profile',
    },
    TODOS: {
        LIST: '/api/todos',
        CREATE: '/api/todos',
        UPDATE: '/api/todos/:id',
        DELETE: '/api/todos/:id',
        SEARCH: '/api/todos/search',
        STATS: '/api/todos/stats',
    },
    CATEGORIES: {
        LIST: '/api/categories',
        CREATE: '/api/categories',
        UPDATE: '/api/categories/:id',
        DELETE: '/api/categories/:id',
        REORDER: '/api/categories/reorder',
    },
    SYSTEM: {
        STATUS: '/api/system/status',
        HEALTH: '/api/system/health',
        METRICS: '/api/system/metrics',
    },
};
// Future IPC channels for Electron
exports.IPC_CHANNELS = {
    AUTH: {
        LOGIN: 'auth:login',
        REGISTER: 'auth:register',
        LOGOUT: 'auth:logout',
        GET_SESSION: 'auth:getSession',
    },
    TODOS: {
        GET_ALL: 'todos:getAll',
        CREATE: 'todos:create',
        UPDATE: 'todos:update',
        DELETE: 'todos:delete',
        SEARCH: 'todos:search',
    },
    CATEGORIES: {
        GET_ALL: 'categories:getAll',
        CREATE: 'categories:create',
        UPDATE: 'categories:update',
        DELETE: 'categories:delete',
    },
    SYSTEM: {
        GET_STATUS: 'system:getStatus',
        GET_METRICS: 'system:getMetrics',
    },
};
// Utility function for creating API responses
function createAPIResponse(success, data, error) {
    return {
        success,
        data,
        error,
        timestamp: new Date().toISOString(),
    };
}
