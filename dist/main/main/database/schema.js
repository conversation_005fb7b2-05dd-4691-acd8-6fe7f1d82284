"use strict";
Object.defineProperty(exports, "__esModule", { value: true });
exports.databaseSchema = exports.DatabaseSchema = void 0;
const connection_1 = require("./connection");
const types_1 = require("@shared/types");
class DatabaseSchema {
    async initializeSchema() {
        try {
            console.log('Initializing database schema...');
            await connection_1.dbConnection.executeTransaction(async (execute) => {
                // Create ENUM types first
                await this.createEnumTypes(execute);
                // Create core tables
                await this.createUsersTable(execute);
                await this.createUserProfilesTable(execute);
                await this.createCategoriesTable(execute);
                await this.createTodosTable(execute);
                // Create sync and audit tables
                await this.createSyncMetadataTable(execute);
                await this.createAuditLogTable(execute);
                // Create session and search tables
                await this.createUserSessionsTable(execute);
                await this.createSearchVectorsTable(execute);
                await this.createEventsTable(execute);
                // Create indexes
                await this.createIndexes(execute);
                // Create triggers and functions
                await this.createTriggersAndFunctions(execute);
            });
            console.log('Database schema initialized successfully');
        }
        catch (error) {
            console.error('Failed to initialize database schema:', error);
            throw new types_1.DatabaseError('Schema initialization failed', 'SCHEMA_INIT_ERROR', error);
        }
    }
    async createEnumTypes(execute) {
        // DuckDB doesn't have native ENUMs, so we'll use CHECK constraints
        // The enum validation will be handled in the table creation
    }
    async createUsersTable(execute) {
        const sql = `
      CREATE TABLE IF NOT EXISTS users (
        id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
        username VARCHAR(50) UNIQUE NOT NULL,
        password_hash VARCHAR(255) NOT NULL,
        created_at TIMESTAMPTZ DEFAULT CURRENT_TIMESTAMP,
        updated_at TIMESTAMPTZ DEFAULT CURRENT_TIMESTAMP,
        last_login_at TIMESTAMPTZ,
        is_active BOOLEAN DEFAULT TRUE,
        email_verified BOOLEAN DEFAULT FALSE
      )
    `;
        await execute(sql);
    }
    async createUserProfilesTable(execute) {
        const sql = `
      CREATE TABLE IF NOT EXISTS user_profiles (
        user_id UUID PRIMARY KEY REFERENCES users(id) ON DELETE CASCADE,
        full_name VARCHAR(100),
        email VARCHAR(255),
        date_of_birth DATE,
        profile_picture TEXT,
        theme_preference VARCHAR(20) DEFAULT 'light' 
          CHECK (theme_preference IN ('light', 'dark', 'frutiger-aero')),
        notification_enabled BOOLEAN DEFAULT TRUE,
        timezone VARCHAR(50) DEFAULT 'UTC',
        language_preference VARCHAR(10) DEFAULT 'en',
        created_at TIMESTAMPTZ DEFAULT CURRENT_TIMESTAMP,
        updated_at TIMESTAMPTZ DEFAULT CURRENT_TIMESTAMP
      )
    `;
        await execute(sql);
    }
    async createCategoriesTable(execute) {
        const sql = `
      CREATE TABLE IF NOT EXISTS categories (
        id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
        user_id UUID NOT NULL REFERENCES users(id) ON DELETE CASCADE,
        name VARCHAR(100) NOT NULL,
        color VARCHAR(7) DEFAULT '#007bff',
        icon VARCHAR(50),
        sort_order INTEGER DEFAULT 0,
        is_default BOOLEAN DEFAULT FALSE,
        created_at TIMESTAMPTZ DEFAULT CURRENT_TIMESTAMP,
        updated_at TIMESTAMPTZ DEFAULT CURRENT_TIMESTAMP
      )
    `;
        await execute(sql);
    }
    async createTodosTable(execute) {
        const sql = `
      CREATE TABLE IF NOT EXISTS todos (
        id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
        user_id UUID NOT NULL REFERENCES users(id) ON DELETE CASCADE,
        category_id UUID REFERENCES categories(id) ON DELETE SET NULL,
        title VARCHAR(500) NOT NULL,
        description TEXT,
        status VARCHAR(20) DEFAULT 'pending' 
          CHECK (status IN ('pending', 'in_progress', 'completed', 'archived', 'cancelled')),
        priority VARCHAR(20) DEFAULT 'medium' 
          CHECK (priority IN ('very_low', 'low', 'medium', 'high', 'very_high')),
        due_date TIMESTAMPTZ,
        reminder_at TIMESTAMPTZ,
        tags VARCHAR[] DEFAULT ARRAY[]::VARCHAR[],
        position INTEGER DEFAULT 0,
        estimated_duration INTERVAL,
        actual_duration INTERVAL,
        metadata JSON DEFAULT '{}',
        created_at TIMESTAMPTZ DEFAULT CURRENT_TIMESTAMP,
        updated_at TIMESTAMPTZ DEFAULT CURRENT_TIMESTAMP,
        completed_at TIMESTAMPTZ,
        is_deleted BOOLEAN DEFAULT FALSE
      )
    `;
        await execute(sql);
    }
    async createSyncMetadataTable(execute) {
        const sql = `
      CREATE TABLE IF NOT EXISTS sync_metadata (
        id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
        table_name VARCHAR(50) NOT NULL,
        record_id UUID NOT NULL,
        local_version INTEGER DEFAULT 1,
        remote_version INTEGER DEFAULT 0,
        last_synced_at TIMESTAMPTZ,
        sync_status VARCHAR(20) DEFAULT 'pending' 
          CHECK (sync_status IN ('pending', 'synced', 'conflict', 'error')),
        conflict_data JSON,
        error_message TEXT,
        created_at TIMESTAMPTZ DEFAULT CURRENT_TIMESTAMP,
        updated_at TIMESTAMPTZ DEFAULT CURRENT_TIMESTAMP,
        UNIQUE(table_name, record_id)
      )
    `;
        await execute(sql);
    }
    async createAuditLogTable(execute) {
        const sql = `
      CREATE TABLE IF NOT EXISTS audit_log (
        id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
        table_name VARCHAR(50) NOT NULL,
        record_id UUID NOT NULL,
        operation VARCHAR(10) NOT NULL CHECK (operation IN ('INSERT', 'UPDATE', 'DELETE')),
        old_values JSON,
        new_values JSON,
        user_id UUID REFERENCES users(id),
        session_id UUID,
        created_at TIMESTAMPTZ DEFAULT CURRENT_TIMESTAMP,
        ip_address VARCHAR(45),
        user_agent TEXT
      )
    `;
        await execute(sql);
    }
    async createUserSessionsTable(execute) {
        const sql = `
      CREATE TABLE IF NOT EXISTS user_sessions (
        session_id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
        user_id UUID NOT NULL REFERENCES users(id) ON DELETE CASCADE,
        device_info JSON,
        ip_address VARCHAR(45),
        user_agent TEXT,
        created_at TIMESTAMPTZ DEFAULT CURRENT_TIMESTAMP,
        expires_at TIMESTAMPTZ NOT NULL,
        last_activity_at TIMESTAMPTZ DEFAULT CURRENT_TIMESTAMP,
        is_active BOOLEAN DEFAULT TRUE
      )
    `;
        await execute(sql);
    }
    async createSearchVectorsTable(execute) {
        const sql = `
      CREATE TABLE IF NOT EXISTS search_vectors (
        id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
        todo_id UUID NOT NULL REFERENCES todos(id) ON DELETE CASCADE,
        search_content TEXT NOT NULL,
        content_hash VARCHAR(64),
        created_at TIMESTAMPTZ DEFAULT CURRENT_TIMESTAMP,
        updated_at TIMESTAMPTZ DEFAULT CURRENT_TIMESTAMP,
        UNIQUE(todo_id)
      )
    `;
        await execute(sql);
    }
    async createEventsTable(execute) {
        const sql = `
      CREATE TABLE IF NOT EXISTS events (
        id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
        event_type VARCHAR(50) NOT NULL CHECK (event_type IN (
          'todo_created', 'todo_updated', 'todo_deleted', 
          'category_created', 'category_updated', 'category_deleted', 
          'user_login', 'user_logout', 'sync_completed'
        )),
        entity_type VARCHAR(50) NOT NULL,
        entity_id UUID NOT NULL,
        user_id UUID NOT NULL REFERENCES users(id),
        session_id UUID REFERENCES user_sessions(session_id),
        data JSON DEFAULT '{}',
        created_at TIMESTAMPTZ DEFAULT CURRENT_TIMESTAMP
      )
    `;
        await execute(sql);
    }
    async createIndexes(execute) {
        const indexes = [
            // Users table indexes
            'CREATE INDEX IF NOT EXISTS idx_users_username ON users(username)',
            'CREATE INDEX IF NOT EXISTS idx_users_active ON users(is_active)',
            // User profiles indexes
            'CREATE INDEX IF NOT EXISTS idx_user_profiles_email ON user_profiles(email)',
            // Categories indexes
            'CREATE INDEX IF NOT EXISTS idx_categories_user_id ON categories(user_id)',
            'CREATE INDEX IF NOT EXISTS idx_categories_sort_order ON categories(user_id, sort_order)',
            // Todos table indexes
            'CREATE INDEX IF NOT EXISTS idx_todos_user_id ON todos(user_id) WHERE is_deleted = FALSE',
            'CREATE INDEX IF NOT EXISTS idx_todos_status ON todos(user_id, status) WHERE is_deleted = FALSE',
            'CREATE INDEX IF NOT EXISTS idx_todos_due_date ON todos(due_date) WHERE is_deleted = FALSE AND due_date IS NOT NULL',
            'CREATE INDEX IF NOT EXISTS idx_todos_category ON todos(category_id) WHERE is_deleted = FALSE',
            'CREATE INDEX IF NOT EXISTS idx_todos_priority ON todos(user_id, priority) WHERE is_deleted = FALSE',
            'CREATE INDEX IF NOT EXISTS idx_todos_position ON todos(user_id, category_id, position) WHERE is_deleted = FALSE',
            // Sync metadata indexes
            'CREATE INDEX IF NOT EXISTS idx_sync_metadata_status ON sync_metadata(sync_status)',
            'CREATE INDEX IF NOT EXISTS idx_sync_metadata_table ON sync_metadata(table_name, sync_status)',
            // Audit log indexes
            'CREATE INDEX IF NOT EXISTS idx_audit_log_table_record ON audit_log(table_name, record_id)',
            'CREATE INDEX IF NOT EXISTS idx_audit_log_user ON audit_log(user_id, created_at)',
            'CREATE INDEX IF NOT EXISTS idx_audit_log_created_at ON audit_log(created_at)',
            // User sessions indexes
            'CREATE INDEX IF NOT EXISTS idx_user_sessions_user_id ON user_sessions(user_id)',
            'CREATE INDEX IF NOT EXISTS idx_user_sessions_expires_at ON user_sessions(expires_at)',
            'CREATE INDEX IF NOT EXISTS idx_user_sessions_active ON user_sessions(user_id, is_active) WHERE is_active = TRUE',
            // Events indexes
            'CREATE INDEX IF NOT EXISTS idx_events_user_id ON events(user_id, created_at)',
            'CREATE INDEX IF NOT EXISTS idx_events_entity ON events(entity_type, entity_id)',
            'CREATE INDEX IF NOT EXISTS idx_events_type ON events(event_type, created_at)',
        ];
        for (const indexSql of indexes) {
            try {
                await execute(indexSql);
            }
            catch (error) {
                console.warn(`Failed to create index: ${indexSql}`, error);
                // Continue with other indexes
            }
        }
    }
    async createTriggersAndFunctions(execute) {
        // DuckDB doesn't support triggers in the same way as PostgreSQL
        // We'll implement these as application-level logic in the DAOs
        console.log('Note: Triggers will be implemented as application-level logic');
    }
    async createDefaultCategories(userId, execute) {
        const executeQuery = execute || ((sql, params) => connection_1.dbConnection.executeQuery(sql, params));
        const defaultCategories = [
            { name: 'Personal', color: '#007bff', icon: 'user', sort_order: 1, is_default: true },
            { name: 'Work', color: '#28a745', icon: 'briefcase', sort_order: 2, is_default: false },
            { name: 'Shopping', color: '#ffc107', icon: 'shopping-cart', sort_order: 3, is_default: false },
            { name: 'Health', color: '#e74c3c', icon: 'heart', sort_order: 4, is_default: false },
        ];
        for (const category of defaultCategories) {
            const sql = `
        INSERT INTO categories (user_id, name, color, icon, sort_order, is_default)
        VALUES ($1, $2, $3, $4, $5, $6)
      `;
            await executeQuery(sql, [userId, category.name, category.color, category.icon, category.sort_order, category.is_default]);
        }
    }
    async dropAllTables() {
        const tables = [
            'events',
            'search_vectors',
            'user_sessions',
            'audit_log',
            'sync_metadata',
            'todos',
            'categories',
            'user_profiles',
            'users'
        ];
        await connection_1.dbConnection.executeTransaction(async (execute) => {
            for (const table of tables) {
                try {
                    await execute(`DROP TABLE IF EXISTS ${table} CASCADE`);
                }
                catch (error) {
                    console.warn(`Failed to drop table ${table}:`, error);
                }
            }
        });
    }
    async validateSchema() {
        try {
            const tables = [
                'users', 'user_profiles', 'categories', 'todos',
                'sync_metadata', 'audit_log', 'user_sessions',
                'search_vectors', 'events'
            ];
            for (const table of tables) {
                const result = await connection_1.dbConnection.executeQuery(`SELECT COUNT(*) as count FROM information_schema.tables WHERE table_name = $1`, [table]);
                if (result.rows[0]?.count === 0) {
                    console.error(`Table ${table} does not exist`);
                    return false;
                }
            }
            console.log('Schema validation passed');
            return true;
        }
        catch (error) {
            console.error('Schema validation failed:', error);
            return false;
        }
    }
}
exports.DatabaseSchema = DatabaseSchema;
exports.databaseSchema = new DatabaseSchema();
