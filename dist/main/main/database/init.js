"use strict";
Object.defineProperty(exports, "__esModule", { value: true });
exports.initializeDatabase = initializeDatabase;
exports.resetDatabase = resetDatabase;
const connection_1 = require("./connection");
const schema_1 = require("./schema");
const config_1 = require("@main/utils/config");
async function initializeDatabase() {
    try {
        console.log('Starting database initialization...');
        // Validate configuration
        config_1.config.validateConfig();
        // Initialize database connection
        await connection_1.dbConnection.initialize();
        // Check if database is ready
        if (!connection_1.dbConnection.isReady()) {
            throw new Error('Database connection is not ready');
        }
        // Initialize schema
        await schema_1.databaseSchema.initializeSchema();
        // Validate schema
        const isValid = await schema_1.databaseSchema.validateSchema();
        if (!isValid) {
            throw new Error('Schema validation failed');
        }
        console.log('Database initialization completed successfully');
        // Log database statistics
        const stats = await connection_1.dbConnection.getStats();
        console.log('Database statistics:', stats);
    }
    catch (error) {
        console.error('Database initialization failed:', error);
        throw error;
    }
}
async function resetDatabase() {
    try {
        console.log('Resetting database...');
        await connection_1.dbConnection.initialize();
        await schema_1.databaseSchema.dropAllTables();
        await schema_1.databaseSchema.initializeSchema();
        console.log('Database reset completed successfully');
    }
    catch (error) {
        console.error('Database reset failed:', error);
        throw error;
    }
}
// CLI interface
if (require.main === module) {
    const command = process.argv[2];
    (async () => {
        try {
            switch (command) {
                case 'reset':
                    await resetDatabase();
                    break;
                default:
                    await initializeDatabase();
                    break;
            }
            process.exit(0);
        }
        catch (error) {
            console.error('Database operation failed:', error);
            process.exit(1);
        }
    })();
}
