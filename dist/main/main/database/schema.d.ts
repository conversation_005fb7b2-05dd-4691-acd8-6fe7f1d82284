export declare class DatabaseSchema {
    initializeSchema(): Promise<void>;
    private createEnumTypes;
    private createUsersTable;
    private createUserProfilesTable;
    private createCategoriesTable;
    private createTodosTable;
    private createSyncMetadataTable;
    private createAuditLogTable;
    private createUserSessionsTable;
    private createSearchVectorsTable;
    private createEventsTable;
    private createIndexes;
    private createTriggersAndFunctions;
    createDefaultCategories(userId: string, execute?: Function): Promise<void>;
    dropAllTables(): Promise<void>;
    validateSchema(): Promise<boolean>;
}
export declare const databaseSchema: DatabaseSchema;
