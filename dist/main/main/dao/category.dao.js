"use strict";
Object.defineProperty(exports, "__esModule", { value: true });
exports.categoryDAO = exports.CategoryDAO = void 0;
const base_dao_1 = require("./base.dao");
const types_1 = require("@shared/types");
class CategoryDAO extends base_dao_1.BaseDAO {
    constructor() {
        super('categories');
    }
    async findByUserId(userId) {
        const result = await this.executeQuery('SELECT * FROM categories WHERE user_id = $1 ORDER BY sort_order ASC, created_at ASC', [userId]);
        return result.rows;
    }
    async findByName(userId, name) {
        const result = await this.executeQuery('SELECT * FROM categories WHERE user_id = $1 AND name = $2', [userId, name]);
        return result.rows[0] || null;
    }
    async getDefaultCategory(userId) {
        const result = await this.executeQuery('SELECT * FROM categories WHERE user_id = $1 AND is_default = TRUE', [userId]);
        return result.rows[0] || null;
    }
    async createCategory(userId, name, color = '#007bff', icon, isDefault = false) {
        // Get the next sort order
        const nextSortOrder = await this.getNextSortOrder(userId);
        // If setting as default, remove default from other categories
        if (isDefault) {
            await this.removeDefaultStatus(userId);
        }
        const result = await this.executeQuery(`INSERT INTO categories (user_id, name, color, icon, sort_order, is_default)
       VALUES ($1, $2, $3, $4, $5, $6)
       RETURNING *`, [userId, name, color, icon, nextSortOrder, isDefault]);
        if (result.rows.length === 0) {
            throw new types_1.DatabaseError('Failed to create category', 'CREATE_ERROR');
        }
        return result.rows[0];
    }
    async updateCategory(categoryId, userId, updates) {
        // If setting as default, remove default from other categories
        if (updates.is_default) {
            await this.removeDefaultStatus(userId);
        }
        const result = await this.executeQuery(`UPDATE categories 
       SET name = COALESCE($1, name),
           color = COALESCE($2, color),
           icon = COALESCE($3, icon),
           is_default = COALESCE($4, is_default),
           updated_at = CURRENT_TIMESTAMP
       WHERE id = $5 AND user_id = $6
       RETURNING *`, [updates.name, updates.color, updates.icon, updates.is_default, categoryId, userId]);
        if (result.rows.length === 0) {
            throw new types_1.DatabaseError('Category not found or access denied', 'NOT_FOUND');
        }
        return result.rows[0];
    }
    async deleteCategory(categoryId, userId) {
        return await this.executeTransaction(async (execute) => {
            // Check if this is the default category
            const categoryResult = await execute('SELECT is_default FROM categories WHERE id = $1 AND user_id = $2', [categoryId, userId]);
            if (categoryResult.rows.length === 0) {
                throw new types_1.DatabaseError('Category not found or access denied', 'NOT_FOUND');
            }
            const isDefault = categoryResult.rows[0].is_default;
            if (isDefault) {
                throw new types_1.DatabaseError('Cannot delete the default category', 'VALIDATION_ERROR');
            }
            // Move todos to default category
            const defaultCategory = await this.getDefaultCategory(userId);
            if (defaultCategory) {
                await execute('UPDATE todos SET category_id = $1 WHERE category_id = $2 AND user_id = $3', [defaultCategory.id, categoryId, userId]);
            }
            else {
                // If no default category, set todos to null category
                await execute('UPDATE todos SET category_id = NULL WHERE category_id = $1 AND user_id = $2', [categoryId, userId]);
            }
            // Delete the category
            const deleteResult = await execute('DELETE FROM categories WHERE id = $1 AND user_id = $2', [categoryId, userId]);
            return deleteResult.rowCount > 0;
        });
    }
    async reorderCategories(userId, categoryOrders) {
        return await this.executeTransaction(async (execute) => {
            const updatedCategories = [];
            for (const { id, sort_order } of categoryOrders) {
                const result = await execute(`UPDATE categories 
           SET sort_order = $1, updated_at = CURRENT_TIMESTAMP
           WHERE id = $2 AND user_id = $3
           RETURNING *`, [sort_order, id, userId]);
                if (result.rows.length > 0) {
                    updatedCategories.push(result.rows[0]);
                }
            }
            return updatedCategories;
        });
    }
    async getCategoryWithTodoCount(userId) {
        const result = await this.executeQuery(`SELECT c.*, 
              COUNT(t.id) as todo_count
       FROM categories c
       LEFT JOIN todos t ON c.id = t.category_id AND t.is_deleted = FALSE
       WHERE c.user_id = $1
       GROUP BY c.id, c.user_id, c.name, c.color, c.icon, c.sort_order, c.is_default, c.created_at, c.updated_at
       ORDER BY c.sort_order ASC, c.created_at ASC`, [userId]);
        return result.rows;
    }
    async setDefaultCategory(categoryId, userId) {
        return await this.executeTransaction(async (execute) => {
            // Remove default status from all categories
            await execute('UPDATE categories SET is_default = FALSE WHERE user_id = $1', [userId]);
            // Set the specified category as default
            const result = await execute(`UPDATE categories 
         SET is_default = TRUE, updated_at = CURRENT_TIMESTAMP
         WHERE id = $1 AND user_id = $2
         RETURNING *`, [categoryId, userId]);
            if (result.rows.length === 0) {
                throw new types_1.DatabaseError('Category not found or access denied', 'NOT_FOUND');
            }
            return result.rows[0];
        });
    }
    async getNextSortOrder(userId) {
        const result = await this.executeQuery('SELECT COALESCE(MAX(sort_order), 0) + 1 as max_order FROM categories WHERE user_id = $1', [userId]);
        return result.rows[0]?.max_order || 1;
    }
    async removeDefaultStatus(userId) {
        await this.executeQuery('UPDATE categories SET is_default = FALSE WHERE user_id = $1 AND is_default = TRUE', [userId]);
    }
    async getCategoryStats(categoryId, userId) {
        const result = await this.executeQuery(`SELECT 
        COUNT(t.id) as total_todos,
        COUNT(CASE WHEN t.status = 'completed' THEN 1 END) as completed_todos,
        COUNT(CASE WHEN t.status = 'pending' THEN 1 END) as pending_todos,
        COUNT(CASE WHEN t.due_date < CURRENT_TIMESTAMP AND t.status NOT IN ('completed', 'cancelled') THEN 1 END) as overdue_todos
       FROM todos t
       WHERE t.category_id = $1 AND t.user_id = $2 AND t.is_deleted = FALSE`, [categoryId, userId]);
        const stats = result.rows[0] || {
            total_todos: 0,
            completed_todos: 0,
            pending_todos: 0,
            overdue_todos: 0
        };
        return {
            totalTodos: stats.total_todos,
            completedTodos: stats.completed_todos,
            pendingTodos: stats.pending_todos,
            overdueTodos: stats.overdue_todos,
        };
    }
    async searchCategories(userId, searchTerm) {
        const result = await this.executeQuery(`SELECT * FROM categories 
       WHERE user_id = $1 AND name ILIKE $2
       ORDER BY sort_order ASC, created_at ASC`, [userId, `%${searchTerm}%`]);
        return result.rows;
    }
    async duplicateCategory(categoryId, userId, newName) {
        const originalCategory = await this.findById(categoryId);
        if (!originalCategory || originalCategory.user_id !== userId) {
            throw new types_1.DatabaseError('Category not found or access denied', 'NOT_FOUND');
        }
        return await this.createCategory(userId, newName, originalCategory.color, originalCategory.icon, false // Never duplicate as default
        );
    }
}
exports.CategoryDAO = CategoryDAO;
exports.categoryDAO = new CategoryDAO();
