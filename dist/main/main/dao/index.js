"use strict";
Object.defineProperty(exports, "__esModule", { value: true });
exports.dao = exports.categoryDAO = exports.CategoryDAO = exports.todoDAO = exports.TodoDAO = exports.userDAO = exports.UserDAO = exports.BaseDAO = void 0;
var base_dao_1 = require("./base.dao");
Object.defineProperty(exports, "BaseDAO", { enumerable: true, get: function () { return base_dao_1.BaseDAO; } });
var user_dao_1 = require("./user.dao");
Object.defineProperty(exports, "UserDAO", { enumerable: true, get: function () { return user_dao_1.UserDAO; } });
Object.defineProperty(exports, "userDAO", { enumerable: true, get: function () { return user_dao_1.userDAO; } });
var todo_dao_1 = require("./todo.dao");
Object.defineProperty(exports, "TodoDAO", { enumerable: true, get: function () { return todo_dao_1.TodoDAO; } });
Object.defineProperty(exports, "todoDAO", { enumerable: true, get: function () { return todo_dao_1.todoDAO; } });
var category_dao_1 = require("./category.dao");
Object.defineProperty(exports, "CategoryDAO", { enumerable: true, get: function () { return category_dao_1.CategoryDAO; } });
Object.defineProperty(exports, "categoryDAO", { enumerable: true, get: function () { return category_dao_1.categoryDAO; } });
// Import instances for the convenience object
const user_dao_2 = require("./user.dao");
const todo_dao_2 = require("./todo.dao");
const category_dao_2 = require("./category.dao");
// Convenience object for all DAOs
exports.dao = {
    user: user_dao_2.userDAO,
    todo: todo_dao_2.todoDAO,
    category: category_dao_2.categoryDAO,
};
