import { BaseDAO } from './base.dao';
import { Todo, TodoStatus, TodoPriority, FilterOptions, PaginationOptions } from '@shared/types';
export interface TodoFilterOptions extends FilterOptions {
    status?: TodoStatus;
    priority?: TodoPriority;
    category_id?: string;
    due_date_from?: Date;
    due_date_to?: Date;
    tags?: string[];
    search?: string;
    is_completed?: boolean;
    is_overdue?: boolean;
}
export declare class TodoDAO extends BaseDAO<Todo> {
    constructor();
    findByUserId(userId: string, pagination?: PaginationOptions, filters?: TodoFilterOptions): Promise<{
        data: Todo[];
        total: number;
    }>;
    updateStatus(todoId: string, status: TodoStatus, userId: string): Promise<Todo>;
    updatePriority(todoId: string, priority: TodoPriority, userId: string): Promise<Todo>;
    updatePosition(todoId: string, newPosition: number, userId: string): Promise<Todo>;
    addTags(todoId: string, tags: string[], userId: string): Promise<Todo>;
    removeTags(todoId: string, tags: string[], userId: string): Promise<Todo>;
    getOverdueTodos(userId: string): Promise<Todo[]>;
    getTodosWithReminders(beforeTime?: Date): Promise<Todo[]>;
    getTodosByCategory(categoryId: string, userId: string): Promise<Todo[]>;
    searchTodos(userId: string, searchTerm: string, limit?: number): Promise<Todo[]>;
    getUserTodoStats(userId: string): Promise<{
        total: number;
        completed: number;
        pending: number;
        inProgress: number;
        overdue: number;
        completionRate: number;
    }>;
    getNextPosition(categoryId: string | null, userId: string): Promise<number>;
    moveToCategory(todoId: string, newCategoryId: string | null, userId: string): Promise<Todo>;
}
export declare const todoDAO: TodoDAO;
