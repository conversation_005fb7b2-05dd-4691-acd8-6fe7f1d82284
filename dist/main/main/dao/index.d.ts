export { BaseDAO } from './base.dao';
export { UserDAO, userDAO } from './user.dao';
export { TodoDAO, todoDAO } from './todo.dao';
export { CategoryDAO, categoryDAO } from './category.dao';
export declare const dao: {
    user: import("./user.dao").UserDAO;
    todo: import("./todo.dao").TodoDAO;
    category: import("./category.dao").CategoryDAO;
};
export type { TodoFilterOptions } from './todo.dao';
