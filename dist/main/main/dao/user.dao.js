"use strict";
Object.defineProperty(exports, "__esModule", { value: true });
exports.userDAO = exports.UserDAO = void 0;
const base_dao_1 = require("./base.dao");
const types_1 = require("@shared/types");
const schema_1 = require("@main/database/schema");
class UserDAO extends base_dao_1.BaseDAO {
    constructor() {
        super('users');
    }
    async findByUsername(username) {
        const result = await this.executeQuery('SELECT * FROM users WHERE username = $1', [username]);
        return result.rows[0] || null;
    }
    async findByEmail(email) {
        const result = await this.executeQuery(`SELECT u.* FROM users u 
       JOIN user_profiles up ON u.id = up.user_id 
       WHERE up.email = $1`, [email]);
        return result.rows[0] || null;
    }
    async createWithProfile(userData, profileData) {
        return await this.executeTransaction(async (execute) => {
            // Create user
            const userResult = await execute(`INSERT INTO users (username, password_hash, is_active, email_verified)
         VALUES ($1, $2, $3, $4)
         RETURNING *`, [userData.username, userData.password_hash, userData.is_active, userData.email_verified]);
            if (userResult.rows.length === 0) {
                throw new types_1.DatabaseError('Failed to create user', 'CREATE_ERROR');
            }
            const user = userResult.rows[0];
            // Create user profile
            const profileResult = await execute(`INSERT INTO user_profiles (
          user_id, full_name, email, date_of_birth, profile_picture,
          theme_preference, notification_enabled, timezone, language_preference
         )
         VALUES ($1, $2, $3, $4, $5, $6, $7, $8, $9)
         RETURNING *`, [
                user.id,
                profileData.full_name,
                profileData.email,
                profileData.date_of_birth,
                profileData.profile_picture,
                profileData.theme_preference || 'light',
                profileData.notification_enabled !== false,
                profileData.timezone || 'UTC',
                profileData.language_preference || 'en'
            ]);
            if (profileResult.rows.length === 0) {
                throw new types_1.DatabaseError('Failed to create user profile', 'CREATE_ERROR');
            }
            const profile = profileResult.rows[0];
            // Create default categories for the user
            await schema_1.databaseSchema.createDefaultCategories(user.id, execute);
            return { user, profile };
        });
    }
    async updateLastLogin(userId) {
        await this.executeQuery('UPDATE users SET last_login_at = CURRENT_TIMESTAMP WHERE id = $1', [userId]);
    }
    async activateUser(userId) {
        const result = await this.executeQuery('UPDATE users SET is_active = TRUE, updated_at = CURRENT_TIMESTAMP WHERE id = $1 RETURNING *', [userId]);
        if (result.rows.length === 0) {
            throw new types_1.DatabaseError('User not found', 'NOT_FOUND');
        }
        return result.rows[0];
    }
    async deactivateUser(userId) {
        const result = await this.executeQuery('UPDATE users SET is_active = FALSE, updated_at = CURRENT_TIMESTAMP WHERE id = $1 RETURNING *', [userId]);
        if (result.rows.length === 0) {
            throw new types_1.DatabaseError('User not found', 'NOT_FOUND');
        }
        return result.rows[0];
    }
    async verifyEmail(userId) {
        const result = await this.executeQuery('UPDATE users SET email_verified = TRUE, updated_at = CURRENT_TIMESTAMP WHERE id = $1 RETURNING *', [userId]);
        if (result.rows.length === 0) {
            throw new types_1.DatabaseError('User not found', 'NOT_FOUND');
        }
        return result.rows[0];
    }
    async updatePassword(userId, passwordHash) {
        const result = await this.executeQuery('UPDATE users SET password_hash = $1, updated_at = CURRENT_TIMESTAMP WHERE id = $2 RETURNING *', [passwordHash, userId]);
        if (result.rows.length === 0) {
            throw new types_1.DatabaseError('User not found', 'NOT_FOUND');
        }
        return result.rows[0];
    }
    async getActiveUsersCount() {
        const result = await this.executeQuery('SELECT COUNT(*) as count FROM users WHERE is_active = TRUE');
        return result.rows[0]?.count || 0;
    }
    async getRecentUsers(limit = 10) {
        const result = await this.executeQuery('SELECT * FROM users ORDER BY created_at DESC LIMIT $1', [limit]);
        return result.rows;
    }
    async searchUsers(searchTerm, limit = 20) {
        const result = await this.executeQuery(`SELECT u.* FROM users u
       JOIN user_profiles up ON u.id = up.user_id
       WHERE u.username ILIKE $1 
          OR up.full_name ILIKE $1 
          OR up.email ILIKE $1
       ORDER BY u.created_at DESC
       LIMIT $2`, [`%${searchTerm}%`, limit]);
        return result.rows;
    }
    async getUserStats(userId) {
        const result = await this.executeQuery(`SELECT 
        COUNT(t.id) as total_todos,
        COUNT(CASE WHEN t.status = 'completed' THEN 1 END) as completed_todos,
        COUNT(CASE WHEN t.status = 'pending' THEN 1 END) as pending_todos,
        COUNT(DISTINCT c.id) as categories_count,
        MAX(GREATEST(t.updated_at, c.updated_at)) as last_activity
       FROM users u
       LEFT JOIN todos t ON u.id = t.user_id AND t.is_deleted = FALSE
       LEFT JOIN categories c ON u.id = c.user_id
       WHERE u.id = $1
       GROUP BY u.id`, [userId]);
        const row = result.rows[0];
        return {
            totalTodos: row?.total_todos || 0,
            completedTodos: row?.completed_todos || 0,
            pendingTodos: row?.pending_todos || 0,
            categoriesCount: row?.categories_count || 0,
            lastActivity: row?.last_activity || null,
        };
    }
}
exports.UserDAO = UserDAO;
exports.userDAO = new UserDAO();
