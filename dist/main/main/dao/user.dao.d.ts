import { BaseDAO } from './base.dao';
import { User, UserProfile } from '@shared/types';
export declare class UserDAO extends BaseDAO<User> {
    constructor();
    findByUsername(username: string): Promise<User | null>;
    findByEmail(email: string): Promise<User | null>;
    createWithProfile(userData: Omit<User, 'id' | 'created_at' | 'updated_at'>, profileData: Omit<UserProfile, 'user_id' | 'created_at' | 'updated_at'>): Promise<{
        user: User;
        profile: UserProfile;
    }>;
    updateLastLogin(userId: string): Promise<void>;
    activateUser(userId: string): Promise<User>;
    deactivateUser(userId: string): Promise<User>;
    verifyEmail(userId: string): Promise<User>;
    updatePassword(userId: string, passwordHash: string): Promise<User>;
    getActiveUsersCount(): Promise<number>;
    getRecentUsers(limit?: number): Promise<User[]>;
    searchUsers(searchTerm: string, limit?: number): Promise<User[]>;
    getUserStats(userId: string): Promise<{
        totalTodos: number;
        completedTodos: number;
        pendingTodos: number;
        categoriesCount: number;
        lastActivity: Date | null;
    }>;
}
export declare const userDAO: UserDAO;
