import { BaseDAO } from './base.dao';
import { Category } from '@shared/types';
export declare class CategoryDAO extends BaseDAO<Category> {
    constructor();
    findByUserId(userId: string): Promise<Category[]>;
    findByName(userId: string, name: string): Promise<Category | null>;
    getDefaultCategory(userId: string): Promise<Category | null>;
    createCategory(userId: string, name: string, color?: string, icon?: string, isDefault?: boolean): Promise<Category>;
    updateCategory(categoryId: string, userId: string, updates: Partial<Category>): Promise<Category>;
    deleteCategory(categoryId: string, userId: string): Promise<boolean>;
    reorderCategories(userId: string, categoryOrders: Array<{
        id: string;
        sort_order: number;
    }>): Promise<Category[]>;
    getCategoryWithTodoCount(userId: string): Promise<Array<Category & {
        todo_count: number;
    }>>;
    setDefaultCategory(categoryId: string, userId: string): Promise<Category>;
    private getNextSortOrder;
    private removeDefaultStatus;
    getCategoryStats(categoryId: string, userId: string): Promise<{
        totalTodos: number;
        completedTodos: number;
        pendingTodos: number;
        overdueTodos: number;
    }>;
    searchCategories(userId: string, searchTerm: string): Promise<Category[]>;
    duplicateCategory(categoryId: string, userId: string, newName: string): Promise<Category>;
}
export declare const categoryDAO: CategoryDAO;
