export declare class CryptographyService {
    private algorithm;
    private keyDerivationAlgorithm;
    private iterations;
    private keyLength;
    private ivLength;
    private saltLength;
    private saltRounds;
    hashPassword(password: string): Promise<string>;
    verifyPassword(password: string, hash: string): Promise<boolean>;
    private validatePasswordStrength;
    private isCommonPassword;
    deriveKey(password: string, salt: Buffer): Promise<Buffer>;
    encryptData(plaintext: string, password: string): Promise<{
        encrypted: string;
        salt: string;
        iv: string;
    }>;
    decryptData(encryptedData: {
        encrypted: string;
        salt: string;
        iv: string;
    }, password: string): Promise<string>;
    generateSecureId(length?: number): string;
    generateUUID(): string;
    hashString(input: string): string;
    generateSecurePassword(length?: number): string;
    private getRandomChar;
    generateSessionToken(): string;
    generateApiKey(): string;
    generateTOTPSecret(): string;
    verifyTOTP(token: string, secret: string): boolean;
    private generateTOTPToken;
    generateDeviceFingerprint(deviceInfo: any): string;
    clearSensitiveData(buffer: Buffer): void;
    rotateEncryptionKey(oldKey: string, newKey: string, encryptedData: any): Promise<any>;
    sanitizeInput(input: string): string;
    createRateLimitToken(identifier: string, windowMs: number): string;
    validateRateLimitToken(token: string, maxAge?: number): {
        identifier: string;
        timestamp: number;
    } | null;
}
export declare const cryptoService: CryptographyService;
