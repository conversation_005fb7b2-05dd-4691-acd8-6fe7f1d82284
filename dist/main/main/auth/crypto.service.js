"use strict";
var __createBinding = (this && this.__createBinding) || (Object.create ? (function(o, m, k, k2) {
    if (k2 === undefined) k2 = k;
    var desc = Object.getOwnPropertyDescriptor(m, k);
    if (!desc || ("get" in desc ? !m.__esModule : desc.writable || desc.configurable)) {
      desc = { enumerable: true, get: function() { return m[k]; } };
    }
    Object.defineProperty(o, k2, desc);
}) : (function(o, m, k, k2) {
    if (k2 === undefined) k2 = k;
    o[k2] = m[k];
}));
var __setModuleDefault = (this && this.__setModuleDefault) || (Object.create ? (function(o, v) {
    Object.defineProperty(o, "default", { enumerable: true, value: v });
}) : function(o, v) {
    o["default"] = v;
});
var __importStar = (this && this.__importStar) || (function () {
    var ownKeys = function(o) {
        ownKeys = Object.getOwnPropertyNames || function (o) {
            var ar = [];
            for (var k in o) if (Object.prototype.hasOwnProperty.call(o, k)) ar[ar.length] = k;
            return ar;
        };
        return ownKeys(o);
    };
    return function (mod) {
        if (mod && mod.__esModule) return mod;
        var result = {};
        if (mod != null) for (var k = ownKeys(mod), i = 0; i < k.length; i++) if (k[i] !== "default") __createBinding(result, mod, k[i]);
        __setModuleDefault(result, mod);
        return result;
    };
})();
Object.defineProperty(exports, "__esModule", { value: true });
exports.cryptoService = exports.CryptographyService = void 0;
const bcrypt = __importStar(require("bcrypt"));
const crypto = __importStar(require("crypto"));
const uuid_1 = require("uuid");
class CryptographyService {
    algorithm = 'aes-256-cbc';
    keyDerivationAlgorithm = 'pbkdf2';
    iterations = 100000;
    keyLength = 32;
    ivLength = 16;
    saltLength = 32;
    saltRounds = 12;
    // Password operations
    async hashPassword(password) {
        this.validatePasswordStrength(password);
        return bcrypt.hash(password, this.saltRounds);
    }
    async verifyPassword(password, hash) {
        return bcrypt.compare(password, hash);
    }
    validatePasswordStrength(password) {
        const errors = [];
        const minLength = 12;
        if (password.length < minLength) {
            errors.push(`Password must be at least ${minLength} characters long`);
        }
        if (!/[a-z]/.test(password)) {
            errors.push('Password must contain at least one lowercase letter');
        }
        if (!/[A-Z]/.test(password)) {
            errors.push('Password must contain at least one uppercase letter');
        }
        if (!/\d/.test(password)) {
            errors.push('Password must contain at least one number');
        }
        if (!/[!@#$%^&*()_+\-=\[\]{};':"\\|,.<>\/?]/.test(password)) {
            errors.push('Password must contain at least one special character');
        }
        if (this.isCommonPassword(password)) {
            errors.push('Password is too common. Please choose a more unique password');
        }
        if (errors.length > 0) {
            throw new Error(errors.join('. '));
        }
    }
    isCommonPassword(password) {
        const commonPasswords = [
            'password', 'password123', '123456789', 'qwerty', 'abc123', 'letmein',
            'monkey', '1234567890', 'dragon', 'princess', 'welcome', 'login',
            'admin', 'root', 'user', 'test', 'guest', 'demo'
        ];
        return commonPasswords.includes(password.toLowerCase());
    }
    // Data encryption operations
    async deriveKey(password, salt) {
        return new Promise((resolve, reject) => {
            crypto.pbkdf2(password, salt, this.iterations, this.keyLength, 'sha256', (err, derivedKey) => {
                if (err)
                    reject(err);
                else
                    resolve(derivedKey);
            });
        });
    }
    async encryptData(plaintext, password) {
        try {
            const salt = crypto.randomBytes(this.saltLength);
            const iv = crypto.randomBytes(this.ivLength);
            const key = await this.deriveKey(password, salt);
            const cipher = crypto.createCipheriv(this.algorithm, key, iv);
            let encrypted = cipher.update(plaintext, 'utf8', 'hex');
            encrypted += cipher.final('hex');
            return {
                encrypted,
                salt: salt.toString('hex'),
                iv: iv.toString('hex'),
            };
        }
        catch (error) {
            throw new Error(`Encryption failed: ${error instanceof Error ? error.message : 'Unknown error'}`);
        }
    }
    async decryptData(encryptedData, password) {
        try {
            const salt = Buffer.from(encryptedData.salt, 'hex');
            const iv = Buffer.from(encryptedData.iv, 'hex');
            const key = await this.deriveKey(password, salt);
            const decipher = crypto.createDecipheriv(this.algorithm, key, iv);
            let decrypted = decipher.update(encryptedData.encrypted, 'hex', 'utf8');
            decrypted += decipher.final('utf8');
            return decrypted;
        }
        catch (error) {
            throw new Error(`Decryption failed: ${error instanceof Error ? error.message : 'Unknown error'}`);
        }
    }
    // Utility functions
    generateSecureId(length = 32) {
        return crypto.randomBytes(length).toString('hex');
    }
    generateUUID() {
        return (0, uuid_1.v4)();
    }
    hashString(input) {
        return crypto.createHash('sha256').update(input).digest('hex');
    }
    generateSecurePassword(length = 16) {
        const lowercase = 'abcdefghijklmnopqrstuvwxyz';
        const uppercase = 'ABCDEFGHIJKLMNOPQRSTUVWXYZ';
        const numbers = '0123456789';
        const symbols = '!@#$%^&*()_+-=[]{}|;:,.<>?';
        const allChars = lowercase + uppercase + numbers + symbols;
        let password = '';
        // Ensure at least one character from each category
        password += this.getRandomChar(lowercase);
        password += this.getRandomChar(uppercase);
        password += this.getRandomChar(numbers);
        password += this.getRandomChar(symbols);
        // Fill the rest randomly
        for (let i = 4; i < length; i++) {
            password += this.getRandomChar(allChars);
        }
        // Shuffle the password
        return password.split('').sort(() => Math.random() - 0.5).join('');
    }
    getRandomChar(charset) {
        return charset.charAt(Math.floor(Math.random() * charset.length));
    }
    // Session token operations
    generateSessionToken() {
        return this.generateSecureId(64);
    }
    generateApiKey() {
        return this.generateSecureId(48);
    }
    // TOTP operations (simplified implementation)
    generateTOTPSecret() {
        // Generate a base32-compatible secret (using hex for simplicity)
        return crypto.randomBytes(20).toString('hex');
    }
    verifyTOTP(token, secret) {
        // Simplified TOTP implementation
        // In a real implementation, use a proper TOTP library like 'otplib'
        const timeStep = Math.floor(Date.now() / 30000);
        const expectedToken = this.generateTOTPToken(secret, timeStep);
        // Allow for time drift (check previous and next time steps)
        return token === expectedToken ||
            token === this.generateTOTPToken(secret, timeStep - 1) ||
            token === this.generateTOTPToken(secret, timeStep + 1);
    }
    generateTOTPToken(secret, timeStep) {
        // Simplified TOTP implementation
        const key = Buffer.from(secret, 'hex');
        const time = Buffer.alloc(8);
        time.writeUInt32BE(timeStep, 4);
        const hmac = crypto.createHmac('sha1', key);
        hmac.update(time);
        const hash = hmac.digest();
        const offset = hash[hash.length - 1] & 0x0f;
        const code = ((hash[offset] & 0x7f) << 24) |
            ((hash[offset + 1] & 0xff) << 16) |
            ((hash[offset + 2] & 0xff) << 8) |
            (hash[offset + 3] & 0xff);
        return (code % 1000000).toString().padStart(6, '0');
    }
    // Device fingerprinting
    generateDeviceFingerprint(deviceInfo) {
        const components = [
            deviceInfo.platform || 'unknown',
            deviceInfo.arch || 'unknown',
            deviceInfo.hostname || 'unknown',
            deviceInfo.userAgent || 'unknown',
            deviceInfo.screenResolution || 'unknown',
        ];
        return this.hashString(components.join('|'));
    }
    // Secure memory operations
    clearSensitiveData(buffer) {
        // Overwrite buffer with random data multiple times
        for (let i = 0; i < 3; i++) {
            crypto.randomFillSync(buffer);
        }
        buffer.fill(0);
    }
    // Key rotation utilities
    async rotateEncryptionKey(oldKey, newKey, encryptedData) {
        const plaintext = await this.decryptData(encryptedData, oldKey);
        const newEncryptedData = await this.encryptData(plaintext, newKey);
        return newEncryptedData;
    }
    // Input sanitization
    sanitizeInput(input) {
        return input
            .replace(/[<>\"'&]/g, '')
            .replace(/javascript:/gi, '')
            .replace(/on\w+=/gi, '')
            .trim();
    }
    // Rate limiting token bucket
    createRateLimitToken(identifier, windowMs) {
        const data = {
            identifier,
            timestamp: Date.now(),
            window: windowMs,
            nonce: this.generateSecureId(16)
        };
        return Buffer.from(JSON.stringify(data)).toString('base64');
    }
    validateRateLimitToken(token, maxAge = 3600000) {
        try {
            const data = JSON.parse(Buffer.from(token, 'base64').toString('utf8'));
            if (Date.now() - data.timestamp > maxAge) {
                return null;
            }
            return {
                identifier: data.identifier,
                timestamp: data.timestamp
            };
        }
        catch {
            return null;
        }
    }
}
exports.CryptographyService = CryptographyService;
exports.cryptoService = new CryptographyService();
