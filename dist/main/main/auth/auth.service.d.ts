import { User, UserProfile } from '@shared/types';
export interface LoginCredentials {
    username: string;
    password: string;
    totpCode?: string;
    deviceInfo?: any;
}
export interface RegisterData {
    username: string;
    password: string;
    email?: string;
    fullName?: string;
}
export interface AuthSession {
    sessionId: string;
    userId: string;
    username: string;
    createdAt: Date;
    expiresAt: Date;
    deviceFingerprint: string;
    ipAddress: string;
    userAgent: string;
    isActive: boolean;
    permissions: string[];
}
export declare class AuthenticationService {
    private sessions;
    private loginAttempts;
    private accountLockouts;
    private sessionCleanupTimer;
    private securityConfig;
    constructor();
    register(registerData: RegisterData): Promise<{
        user: User;
        profile: UserProfile;
    }>;
    login(credentials: LoginCredentials): Promise<AuthSession>;
    logout(sessionId: string): Promise<void>;
    validateSession(sessionId: string): Promise<AuthSession | null>;
    refreshSession(sessionId: string): Promise<AuthSession | null>;
    changePassword(userId: string, currentPassword: string, newPassword: string): Promise<void>;
    resetPassword(username: string, newPassword: string): Promise<void>;
    private validateCredentials;
    private validateTwoFactor;
    private createSession;
    private checkAccountLockout;
    private trackFailedLogin;
    private revokeAllUserSessions;
    private startSessionCleanup;
    getSessionCount(): number;
    getActiveUsers(): string[];
    getUserSessions(userId: string): Promise<AuthSession[]>;
    shutdown(): Promise<void>;
}
export declare const authService: AuthenticationService;
