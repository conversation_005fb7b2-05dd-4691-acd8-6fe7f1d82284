import { QueryResult, ConnectionStatus } from '@shared/types';
export declare class DatabaseConnection {
    private static instance;
    private db;
    private connection;
    private isConnected;
    private connectionStatus;
    private constructor();
    static getInstance(): DatabaseConnection;
    initialize(): Promise<void>;
    private connect;
    private configureDatabase;
    private testConnection;
    executeQuery<T = any>(query: string, params?: any[]): Promise<QueryResult<T>>;
    executeTransaction<T>(callback: (execute: (query: string, params?: any[]) => Promise<QueryResult>) => Promise<T>): Promise<T>;
    batch(queries: Array<{
        query: string;
        params?: any[];
    }>): Promise<QueryResult[]>;
    getConnectionStatus(): ConnectionStatus;
    isReady(): boolean;
    reconnect(): Promise<void>;
    close(): Promise<void>;
    healthCheck(): Promise<boolean>;
    getStats(): Promise<any>;
}
export declare const dbConnection: DatabaseConnection;
