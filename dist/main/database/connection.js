"use strict";
var __createBinding = (this && this.__createBinding) || (Object.create ? (function(o, m, k, k2) {
    if (k2 === undefined) k2 = k;
    var desc = Object.getOwnPropertyDescriptor(m, k);
    if (!desc || ("get" in desc ? !m.__esModule : desc.writable || desc.configurable)) {
      desc = { enumerable: true, get: function() { return m[k]; } };
    }
    Object.defineProperty(o, k2, desc);
}) : (function(o, m, k, k2) {
    if (k2 === undefined) k2 = k;
    o[k2] = m[k];
}));
var __setModuleDefault = (this && this.__setModuleDefault) || (Object.create ? (function(o, v) {
    Object.defineProperty(o, "default", { enumerable: true, value: v });
}) : function(o, v) {
    o["default"] = v;
});
var __importStar = (this && this.__importStar) || (function () {
    var ownKeys = function(o) {
        ownKeys = Object.getOwnPropertyNames || function (o) {
            var ar = [];
            for (var k in o) if (Object.prototype.hasOwnProperty.call(o, k)) ar[ar.length] = k;
            return ar;
        };
        return ownKeys(o);
    };
    return function (mod) {
        if (mod && mod.__esModule) return mod;
        var result = {};
        if (mod != null) for (var k = ownKeys(mod), i = 0; i < k.length; i++) if (k[i] !== "default") __createBinding(result, mod, k[i]);
        __setModuleDefault(result, mod);
        return result;
    };
})();
Object.defineProperty(exports, "__esModule", { value: true });
exports.dbConnection = exports.DatabaseConnection = void 0;
const duckdb = __importStar(require("duckdb"));
const path = __importStar(require("path"));
const fs = __importStar(require("fs/promises"));
const config_1 = require("@main/utils/config");
const types_1 = require("@shared/types");
class DatabaseConnection {
    static instance;
    db = null;
    connection = null;
    isConnected = false;
    connectionStatus = {
        isConnected: false,
        connectionAttempts: 0,
    };
    constructor() { }
    static getInstance() {
        if (!DatabaseConnection.instance) {
            DatabaseConnection.instance = new DatabaseConnection();
        }
        return DatabaseConnection.instance;
    }
    async initialize() {
        try {
            await this.connect();
            console.log('Database connection initialized successfully');
        }
        catch (error) {
            console.error('Failed to initialize database connection:', error);
            throw new types_1.DatabaseError('Database initialization failed', 'INIT_ERROR', error);
        }
    }
    async connect() {
        try {
            this.connectionStatus.connectionAttempts++;
            const dbConfig = config_1.config.getDatabaseConfig();
            // Ensure data directory exists
            const dataDir = path.dirname(dbConfig.path);
            await fs.mkdir(dataDir, { recursive: true });
            // Create DuckDB database
            this.db = new duckdb.Database(dbConfig.path);
            // Get connection
            this.connection = this.db.connect();
            // Configure DuckDB settings
            await this.configureDatabase();
            // Test connection
            await this.testConnection();
            this.isConnected = true;
            this.connectionStatus.isConnected = true;
            this.connectionStatus.lastConnectionAt = new Date();
            this.connectionStatus.lastError = undefined;
        }
        catch (error) {
            this.isConnected = false;
            this.connectionStatus.isConnected = false;
            this.connectionStatus.lastError = error instanceof Error ? error.message : 'Unknown error';
            throw new types_1.DatabaseError('Failed to connect to database', 'CONNECTION_ERROR', error);
        }
    }
    async configureDatabase() {
        if (!this.connection) {
            throw new types_1.DatabaseError('No database connection available', 'NO_CONNECTION');
        }
        const dbConfig = config_1.config.getDatabaseConfig();
        try {
            // Configure DuckDB settings
            const settings = [
                'SET memory_limit = \'1GB\'',
                'SET threads = 4',
                'SET enable_progress_bar = false',
                'SET enable_print_progress_bar = false',
            ];
            // Enable WAL mode if configured
            if (dbConfig.enableWAL) {
                settings.push('PRAGMA journal_mode = WAL');
            }
            for (const setting of settings) {
                await this.executeQuery(setting);
            }
            // Install and load required extensions
            const extensions = [
                'httpfs', // For reading from HTTP(S) sources
                'json', // For JSON operations
                'fts', // For full-text search (if available)
            ];
            for (const ext of extensions) {
                try {
                    await this.executeQuery(`INSTALL ${ext}`);
                    await this.executeQuery(`LOAD ${ext}`);
                }
                catch (error) {
                    console.warn(`Failed to load extension ${ext}:`, error);
                    // Continue with other extensions
                }
            }
        }
        catch (error) {
            console.warn('Failed to configure some database settings:', error);
            // Don't throw here as basic functionality should still work
        }
    }
    async testConnection() {
        if (!this.connection) {
            throw new types_1.DatabaseError('No database connection available', 'NO_CONNECTION');
        }
        try {
            await this.executeQuery('SELECT 1 as test');
        }
        catch (error) {
            throw new types_1.DatabaseError('Database connection test failed', 'CONNECTION_TEST_FAILED', error);
        }
    }
    async executeQuery(query, params = []) {
        if (!this.connection || !this.isConnected) {
            throw new types_1.DatabaseError('Database not connected', 'NOT_CONNECTED');
        }
        return new Promise((resolve, reject) => {
            try {
                this.connection.all(query, params, (err, rows) => {
                    if (err) {
                        reject(new types_1.DatabaseError(`Query execution failed: ${err.message}`, 'QUERY_ERROR', err));
                        return;
                    }
                    resolve({
                        rows: rows || [],
                        rowCount: rows ? rows.length : 0,
                        command: query.trim().split(' ')[0].toUpperCase(),
                    });
                });
            }
            catch (error) {
                reject(new types_1.DatabaseError('Query execution failed', 'QUERY_ERROR', error));
            }
        });
    }
    async executeTransaction(callback) {
        if (!this.connection || !this.isConnected) {
            throw new types_1.DatabaseError('Database not connected', 'NOT_CONNECTED');
        }
        try {
            await this.executeQuery('BEGIN TRANSACTION');
            const executeInTransaction = async (query, params = []) => {
                return this.executeQuery(query, params);
            };
            const result = await callback(executeInTransaction);
            await this.executeQuery('COMMIT');
            return result;
        }
        catch (error) {
            try {
                await this.executeQuery('ROLLBACK');
            }
            catch (rollbackError) {
                console.error('Failed to rollback transaction:', rollbackError);
            }
            throw error;
        }
    }
    async batch(queries) {
        const results = [];
        await this.executeTransaction(async (execute) => {
            for (const { query, params = [] } of queries) {
                const result = await execute(query, params);
                results.push(result);
            }
            return results;
        });
        return results;
    }
    getConnectionStatus() {
        return { ...this.connectionStatus };
    }
    isReady() {
        return this.isConnected && this.connection !== null;
    }
    async reconnect() {
        if (this.connection) {
            try {
                this.connection.close();
            }
            catch (error) {
                console.warn('Error closing existing connection:', error);
            }
        }
        if (this.db) {
            try {
                this.db.close();
            }
            catch (error) {
                console.warn('Error closing existing database:', error);
            }
        }
        this.connection = null;
        this.db = null;
        this.isConnected = false;
        await this.connect();
    }
    async close() {
        try {
            if (this.connection) {
                this.connection.close();
                this.connection = null;
            }
            if (this.db) {
                this.db.close();
                this.db = null;
            }
            this.isConnected = false;
            this.connectionStatus.isConnected = false;
            console.log('Database connection closed successfully');
        }
        catch (error) {
            console.error('Error closing database connection:', error);
            throw new types_1.DatabaseError('Failed to close database connection', 'CLOSE_ERROR', error);
        }
    }
    // Health check method
    async healthCheck() {
        try {
            if (!this.isConnected) {
                return false;
            }
            await this.executeQuery('SELECT 1');
            return true;
        }
        catch (error) {
            console.warn('Database health check failed:', error);
            return false;
        }
    }
    // Get database statistics
    async getStats() {
        try {
            const [memoryUsage, tableCount, indexCount,] = await Promise.all([
                this.executeQuery("SELECT * FROM pragma_database_size()"),
                this.executeQuery("SELECT COUNT(*) as count FROM information_schema.tables WHERE table_schema = 'main'"),
                this.executeQuery("SELECT COUNT(*) as count FROM information_schema.statistics"),
            ]);
            return {
                memoryUsage: memoryUsage.rows[0],
                tableCount: tableCount.rows[0]?.count || 0,
                indexCount: indexCount.rows[0]?.count || 0,
                isConnected: this.isConnected,
                connectionAttempts: this.connectionStatus.connectionAttempts,
                lastConnectionAt: this.connectionStatus.lastConnectionAt,
            };
        }
        catch (error) {
            console.warn('Failed to get database stats:', error);
            return {
                isConnected: this.isConnected,
                connectionAttempts: this.connectionStatus.connectionAttempts,
                lastConnectionAt: this.connectionStatus.lastConnectionAt,
                error: error instanceof Error ? error.message : 'Unknown error',
            };
        }
    }
}
exports.DatabaseConnection = DatabaseConnection;
// Export singleton instance
exports.dbConnection = DatabaseConnection.getInstance();
