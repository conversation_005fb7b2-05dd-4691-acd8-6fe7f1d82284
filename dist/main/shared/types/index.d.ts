export interface User {
    id: string;
    username: string;
    password_hash: string;
    created_at: Date;
    updated_at: Date;
    last_login_at?: Date;
    is_active: boolean;
    email_verified: boolean;
}
export interface UserProfile {
    user_id: string;
    full_name?: string;
    email?: string;
    date_of_birth?: Date;
    profile_picture?: string;
    theme_preference: 'light' | 'dark' | 'frutiger-aero';
    notification_enabled: boolean;
    timezone: string;
    language_preference: string;
    created_at: Date;
    updated_at: Date;
}
export interface Category {
    id: string;
    user_id: string;
    name: string;
    color: string;
    icon?: string;
    sort_order: number;
    is_default: boolean;
    created_at: Date;
    updated_at: Date;
}
export type TodoStatus = 'pending' | 'in_progress' | 'completed' | 'archived' | 'cancelled';
export type TodoPriority = 'very_low' | 'low' | 'medium' | 'high' | 'very_high';
export interface Todo {
    id: string;
    user_id: string;
    category_id?: string;
    title: string;
    description?: string;
    status: TodoStatus;
    priority: TodoPriority;
    due_date?: Date;
    reminder_at?: Date;
    tags: string[];
    position: number;
    estimated_duration?: string;
    actual_duration?: string;
    metadata: Record<string, any>;
    created_at: Date;
    updated_at: Date;
    completed_at?: Date;
    is_deleted: boolean;
}
export type SyncStatus = 'pending' | 'synced' | 'conflict' | 'error';
export interface SyncMetadata {
    id: string;
    table_name: string;
    record_id: string;
    local_version: number;
    remote_version: number;
    last_synced_at?: Date;
    sync_status: SyncStatus;
    conflict_data?: Record<string, any>;
    error_message?: string;
    created_at: Date;
    updated_at: Date;
}
export type AuditOperation = 'INSERT' | 'UPDATE' | 'DELETE';
export interface AuditLog {
    id: string;
    table_name: string;
    record_id: string;
    operation: AuditOperation;
    old_values?: Record<string, any>;
    new_values?: Record<string, any>;
    user_id?: string;
    session_id?: string;
    created_at: Date;
    ip_address?: string;
    user_agent?: string;
}
export interface UserSession {
    session_id: string;
    user_id: string;
    device_info?: Record<string, any>;
    ip_address?: string;
    user_agent?: string;
    created_at: Date;
    expires_at: Date;
    last_activity_at: Date;
    is_active: boolean;
}
export interface SearchVector {
    id: string;
    todo_id: string;
    search_content: string;
    content_hash: string;
    created_at: Date;
    updated_at: Date;
}
export interface MCPConfig {
    motherduckToken: string;
    databaseName: string;
    connectionTimeout: number;
    retryAttempts: number;
    retryDelay: number;
    enableMetrics: boolean;
    logLevel: 'debug' | 'info' | 'warn' | 'error';
}
export interface MCPToolOptions {
    name: string;
    arguments: Record<string, any>;
}
export interface ConnectionStatus {
    isConnected: boolean;
    lastConnectionAt?: Date;
    connectionAttempts: number;
    lastError?: string;
}
export interface DatabaseConfig {
    path: string;
    motherduckToken?: string;
    connectionTimeout: number;
    maxConnections: number;
    minConnections: number;
    enableWAL: boolean;
    enableEncryption: boolean;
}
export declare class DatabaseError extends Error {
    code?: string | undefined;
    details?: any | undefined;
    constructor(message: string, code?: string | undefined, details?: any | undefined);
}
export declare class MCPError extends Error {
    code?: string | undefined;
    details?: any | undefined;
    constructor(message: string, code?: string | undefined, details?: any | undefined);
}
export declare class AuthenticationError extends Error {
    code?: string | undefined;
    constructor(message: string, code?: string | undefined);
}
export interface QueryResult<T = any> {
    rows: T[];
    rowCount: number;
    command: string;
    fields?: Array<{
        name: string;
        dataTypeID: number;
    }>;
}
export interface PaginationOptions {
    page: number;
    limit: number;
    sortBy?: string;
    sortOrder?: 'ASC' | 'DESC';
}
export interface FilterOptions {
    user_id?: string;
    category_id?: string;
    status?: TodoStatus;
    priority?: TodoPriority;
    due_date_from?: Date;
    due_date_to?: Date;
    tags?: string[];
    search?: string;
}
