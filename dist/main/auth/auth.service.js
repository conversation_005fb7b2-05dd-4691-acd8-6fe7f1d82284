"use strict";
Object.defineProperty(exports, "__esModule", { value: true });
exports.authService = exports.AuthenticationService = void 0;
const crypto_service_1 = require("./crypto.service");
const user_dao_1 = require("@main/dao/user.dao");
const config_1 = require("@main/utils/config");
const types_1 = require("@shared/types");
class AuthenticationService {
    sessions = new Map();
    loginAttempts = new Map();
    accountLockouts = new Map();
    sessionCleanupTimer = null;
    securityConfig = config_1.config.getSecurityConfig();
    constructor() {
        this.startSessionCleanup();
    }
    async register(registerData) {
        try {
            // Check if username already exists
            const existingUser = await user_dao_1.userDAO.findByUsername(registerData.username);
            if (existingUser) {
                throw new types_1.AuthenticationError('Username already exists', 'USERNAME_EXISTS');
            }
            // Check if email already exists (if provided)
            if (registerData.email) {
                const existingEmailUser = await user_dao_1.userDAO.findByEmail(registerData.email);
                if (existingEmailUser) {
                    throw new types_1.AuthenticationError('Email already exists', 'EMAIL_EXISTS');
                }
            }
            // Hash password
            const passwordHash = await crypto_service_1.cryptoService.hashPassword(registerData.password);
            // Create user and profile
            const result = await user_dao_1.userDAO.createWithProfile({
                username: registerData.username,
                password_hash: passwordHash,
                is_active: true,
                email_verified: false,
            }, {
                full_name: registerData.fullName,
                email: registerData.email,
                theme_preference: 'light',
                notification_enabled: true,
                timezone: 'UTC',
                language_preference: 'en',
            });
            console.log(`User registered successfully: ${registerData.username}`);
            return result;
        }
        catch (error) {
            if (error instanceof types_1.AuthenticationError) {
                throw error;
            }
            console.error('Registration error:', error);
            throw new types_1.AuthenticationError('Registration failed', 'REGISTRATION_ERROR');
        }
    }
    async login(credentials) {
        try {
            // Check account lockout
            await this.checkAccountLockout(credentials.username);
            // Validate credentials
            const user = await this.validateCredentials(credentials);
            // Two-factor authentication (if enabled)
            if (this.securityConfig.require2FA && credentials.totpCode) {
                await this.validateTwoFactor(user.id, credentials.totpCode);
            }
            // Create session
            const session = await this.createSession(user, credentials.deviceInfo || {});
            // Reset login attempts
            this.loginAttempts.delete(credentials.username);
            // Update last login
            await user_dao_1.userDAO.updateLastLogin(user.id);
            // Log successful authentication
            console.log(`User logged in successfully: ${credentials.username}`);
            return session;
        }
        catch (error) {
            // Track failed attempts
            await this.trackFailedLogin(credentials.username);
            if (error instanceof types_1.AuthenticationError) {
                throw error;
            }
            console.error('Login error:', error);
            throw new types_1.AuthenticationError('Login failed', 'LOGIN_ERROR');
        }
    }
    async logout(sessionId) {
        const session = this.sessions.get(sessionId);
        if (session) {
            session.isActive = false;
            this.sessions.delete(sessionId);
            console.log(`User logged out: ${session.username}`);
        }
    }
    async validateSession(sessionId) {
        const session = this.sessions.get(sessionId);
        if (!session) {
            return null;
        }
        // Check if session is expired
        if (session.expiresAt < new Date()) {
            await this.logout(sessionId);
            return null;
        }
        // Update session expiry (sliding expiration)
        session.expiresAt = new Date(Date.now() + this.securityConfig.sessionTimeout);
        return session;
    }
    async refreshSession(sessionId) {
        const session = await this.validateSession(sessionId);
        if (!session) {
            return null;
        }
        // Extend session
        session.expiresAt = new Date(Date.now() + this.securityConfig.sessionTimeout);
        return session;
    }
    async changePassword(userId, currentPassword, newPassword) {
        try {
            // Get user
            const user = await user_dao_1.userDAO.findById(userId);
            if (!user) {
                throw new types_1.AuthenticationError('User not found', 'USER_NOT_FOUND');
            }
            // Verify current password
            const isCurrentPasswordValid = await crypto_service_1.cryptoService.verifyPassword(currentPassword, user.password_hash);
            if (!isCurrentPasswordValid) {
                throw new types_1.AuthenticationError('Current password is incorrect', 'INVALID_PASSWORD');
            }
            // Hash new password
            const newPasswordHash = await crypto_service_1.cryptoService.hashPassword(newPassword);
            // Update password
            await user_dao_1.userDAO.updatePassword(userId, newPasswordHash);
            // Invalidate all user sessions (force re-login)
            await this.revokeAllUserSessions(userId);
            console.log(`Password changed for user: ${user.username}`);
        }
        catch (error) {
            if (error instanceof types_1.AuthenticationError) {
                throw error;
            }
            console.error('Password change error:', error);
            throw new types_1.AuthenticationError('Password change failed', 'PASSWORD_CHANGE_ERROR');
        }
    }
    async resetPassword(username, newPassword) {
        try {
            const user = await user_dao_1.userDAO.findByUsername(username);
            if (!user) {
                throw new types_1.AuthenticationError('User not found', 'USER_NOT_FOUND');
            }
            const newPasswordHash = await crypto_service_1.cryptoService.hashPassword(newPassword);
            await user_dao_1.userDAO.updatePassword(user.id, newPasswordHash);
            // Invalidate all user sessions
            await this.revokeAllUserSessions(user.id);
            console.log(`Password reset for user: ${username}`);
        }
        catch (error) {
            if (error instanceof types_1.AuthenticationError) {
                throw error;
            }
            console.error('Password reset error:', error);
            throw new types_1.AuthenticationError('Password reset failed', 'PASSWORD_RESET_ERROR');
        }
    }
    async validateCredentials(credentials) {
        const user = await user_dao_1.userDAO.findByUsername(credentials.username);
        if (!user) {
            throw new types_1.AuthenticationError('Invalid credentials', 'INVALID_CREDENTIALS');
        }
        const isValid = await crypto_service_1.cryptoService.verifyPassword(credentials.password, user.password_hash);
        if (!isValid) {
            throw new types_1.AuthenticationError('Invalid credentials', 'INVALID_CREDENTIALS');
        }
        if (!user.is_active) {
            throw new types_1.AuthenticationError('Account is disabled', 'ACCOUNT_DISABLED');
        }
        return user;
    }
    async validateTwoFactor(userId, totpCode) {
        // In a real implementation, you would retrieve the user's TOTP secret
        // and validate the code. For now, this is a placeholder.
        // const userTotpSecret = await getUserTotpSecret(userId);
        // const isValid = cryptoService.verifyTOTP(totpCode, userTotpSecret);
        // For demo purposes, accept '123456' as valid TOTP
        if (totpCode !== '123456') {
            throw new types_1.AuthenticationError('Invalid two-factor authentication code', 'INVALID_2FA');
        }
    }
    async createSession(user, deviceInfo) {
        const sessionId = crypto_service_1.cryptoService.generateSessionToken();
        const deviceFingerprint = crypto_service_1.cryptoService.generateDeviceFingerprint(deviceInfo);
        const session = {
            sessionId,
            userId: user.id,
            username: user.username,
            createdAt: new Date(),
            expiresAt: new Date(Date.now() + this.securityConfig.sessionTimeout),
            deviceFingerprint,
            ipAddress: deviceInfo.ipAddress || 'localhost',
            userAgent: deviceInfo.userAgent || 'unknown',
            isActive: true,
            permissions: ['read', 'write'], // Default permissions
        };
        // Store session
        this.sessions.set(sessionId, session);
        return session;
    }
    async checkAccountLockout(username) {
        const lockoutUntil = this.accountLockouts.get(username);
        if (lockoutUntil && lockoutUntil > new Date()) {
            const remainingTime = Math.ceil((lockoutUntil.getTime() - Date.now()) / 1000 / 60);
            throw new types_1.AuthenticationError(`Account locked. Try again in ${remainingTime} minutes.`, 'ACCOUNT_LOCKED');
        }
    }
    async trackFailedLogin(username) {
        const attempts = (this.loginAttempts.get(username) || 0) + 1;
        this.loginAttempts.set(username, attempts);
        if (attempts >= this.securityConfig.maxLoginAttempts) {
            const lockoutUntil = new Date(Date.now() + this.securityConfig.lockoutDuration);
            this.accountLockouts.set(username, lockoutUntil);
            console.warn(`Account locked for ${username} after ${attempts} failed attempts`);
        }
    }
    async revokeAllUserSessions(userId) {
        for (const [sessionId, session] of this.sessions.entries()) {
            if (session.userId === userId) {
                await this.logout(sessionId);
            }
        }
    }
    startSessionCleanup() {
        this.sessionCleanupTimer = setInterval(() => {
            const now = new Date();
            for (const [sessionId, session] of this.sessions.entries()) {
                if (session.expiresAt < now) {
                    this.sessions.delete(sessionId);
                }
            }
            // Clean up old lockouts
            for (const [username, lockoutUntil] of this.accountLockouts.entries()) {
                if (lockoutUntil < now) {
                    this.accountLockouts.delete(username);
                }
            }
            // Clean up old login attempts (after 1 hour)
            const oneHourAgo = Date.now() - 3600000;
            for (const [username] of this.loginAttempts.entries()) {
                // In a real implementation, you'd track timestamps for login attempts
                // For now, we'll clear all attempts periodically
                this.loginAttempts.delete(username);
            }
        }, 60000); // Clean up every minute
    }
    getSessionCount() {
        return this.sessions.size;
    }
    getActiveUsers() {
        const users = new Set();
        for (const session of this.sessions.values()) {
            if (session.isActive) {
                users.add(session.username);
            }
        }
        return Array.from(users);
    }
    async getUserSessions(userId) {
        const userSessions = [];
        for (const session of this.sessions.values()) {
            if (session.userId === userId && session.isActive) {
                userSessions.push(session);
            }
        }
        return userSessions;
    }
    async shutdown() {
        if (this.sessionCleanupTimer) {
            clearInterval(this.sessionCleanupTimer);
            this.sessionCleanupTimer = null;
        }
        // Clear all sessions
        this.sessions.clear();
        this.loginAttempts.clear();
        this.accountLockouts.clear();
        console.log('Authentication service shut down');
    }
}
exports.AuthenticationService = AuthenticationService;
exports.authService = new AuthenticationService();
