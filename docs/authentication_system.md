# Authentication System Documentation

## Overview

This document provides an overview of the authentication system implemented in the Modern Todo application. The system provides secure user authentication with session management, password security, and integration with the DuckDB backend through the MotherDuck MCP Server.

## Architecture

The authentication system follows a client-server architecture with the following components:

### Frontend Components

1. **Authentication Service** (`src/renderer/services/auth.service.ts`)
   - <PERSON>les communication with the backend authentication service
   - Manages session persistence using localStorage
   - Provides methods for login, registration, logout, and session management

2. **Authentication Context** (`src/renderer/contexts/AuthContext.tsx`)
   - React context for managing authentication state across the application
   - Provides authentication status, user information, and profile data
   - Exposes methods for authentication operations

3. **UI Components**
   - **Login Form** (`src/renderer/components/auth/LoginForm.tsx`)
   - **Registration Form** (`src/renderer/components/auth/RegisterForm.tsx`)
   - **Password Reset** (`src/renderer/components/auth/PasswordReset.tsx`)
   - **User Profile** (`src/renderer/components/auth/UserProfile.tsx`)
   - **Protected Route** (`src/renderer/components/auth/ProtectedRoute.tsx`)

4. **Session Manager** (`src/renderer/hooks/useSessionManager.ts`)
   - Custom React hook for automatic session management
   - Handles session refresh and automatic logout on expiry

### Backend Components

1. **Authentication Service** (`src/main/auth/auth.service.ts`)
   - Core authentication logic
   - Session management with in-memory storage
   - Password validation and hashing
   - Account lockout mechanisms

2. **Cryptography Service** (`src/main/auth/crypto.service.ts`)
   - Password hashing using bcrypt
   - Session token generation
   - Data encryption utilities

3. **User DAO** (`src/main/dao/user.dao.ts`)
   - Database operations for user management
   - User creation, retrieval, and updates

## Authentication Flow

### User Registration

1. User fills out registration form with username, email, and password
2. Frontend validates password strength requirements
3. Registration data sent to backend authentication service
4. Backend validates uniqueness of username/email
5. Password is hashed using bcrypt
6. User and profile records are created in the database
7. Default categories are created for the user
8. Success response sent to frontend

### User Login

1. User provides username and password
2. Credentials sent to backend authentication service
3. Backend validates credentials against stored hash
4. Account lockout checks performed
5. New session is created with expiration
6. Session stored in memory and returned to frontend
7. Frontend stores session in localStorage
8. User is redirected to main application

### Session Management

1. Session tokens are stored in localStorage for persistence
2. Session validation occurs on application startup
3. Automatic session refresh when expiration approaches
4. Automatic logout on session expiry
5. Manual logout clears session from memory and localStorage

### Password Security

1. Minimum 12-character password requirement
2. Must contain uppercase, lowercase, numbers, and special characters
3. Common password dictionary check
4. bcrypt hashing with salt rounds = 12
5. Password strength validation on registration

## Integration with DuckDB and MCP

The authentication system integrates with the DuckDB backend through:

1. **Database Schema** (`src/main/database/schema.ts`)
   - Users table with username, password_hash, and account status
   - User profiles table with personal information
   - User sessions table for session tracking

2. **MCP Service** (`src/main/mcp/service.ts`)
   - Connection to MotherDuck cloud database
   - Secure token-based authentication
   - Query execution for user data operations

## Security Features

### Password Security
- bcrypt hashing with configurable salt rounds
- Password strength requirements
- Common password dictionary check
- No password length maximum to support passphrases

### Session Security
- Secure session token generation
- Session expiration with sliding window
- Device fingerprinting
- Account lockout after failed attempts
- Automatic session refresh

### Data Security
- Passwords never stored in plain text
- Session tokens stored securely
- User data encrypted at rest (when enabled)
- Secure communication channels

### Rate Limiting
- Login attempt limiting
- Registration rate limiting
- Brute force protection

## API Endpoints

The authentication system uses Electron IPC for communication between renderer and main processes:

### Authentication IPC Channels

- `auth:login` - User login
- `auth:register` - User registration
- `auth:logout` - User logout
- `auth:validate-session` - Session validation
- `auth:refresh-session` - Session refresh
- `auth:change-password` - Password change
- `auth:reset-password` - Password reset

## UI Components

### Login Form
- Username and password fields
- Password visibility toggle
- "Remember me" option
- Password reset link
- Loading states and error handling

### Registration Form
- Username, email, and password fields
- Password strength indicator
- Password confirmation
- Real-time validation
- Loading states and error handling

### User Profile
- User information display
- Password change functionality
- Account settings

### Protected Route
- Route protection for authenticated areas
- Loading states during authentication check
- Redirect to login when unauthenticated

## Session Management

### Automatic Session Handling
- Session validation on application startup
- Automatic refresh before expiration
- Graceful logout on session expiry
- Persistent sessions across application restarts

### Manual Session Management
- User-initiated logout
- Session clearing on logout
- Session cleanup on application shutdown

## Error Handling

### Authentication Errors
- Invalid credentials
- Account disabled
- Account locked
- Registration conflicts
- Session validation failures

### Recovery Options
- Password reset functionality
- Account unlock procedures
- Session refresh on network errors

## Testing

### Unit Tests
- Authentication service tests
- Context provider tests
- Component tests for UI elements

### Integration Tests
- End-to-end authentication flows
- Session management scenarios
- Error handling validation

## Configuration

### Environment Variables
- `SESSION_TIMEOUT` - Session duration in milliseconds
- `MAX_LOGIN_ATTEMPTS` - Maximum failed login attempts
- `LOCKOUT_DURATION` - Account lockout duration
- `PASSWORD_MIN_LENGTH` - Minimum password length
- `ENCRYPTION_ENABLED` - Enable data encryption

### Security Configuration
- Password strength requirements
- Session timeout settings
- Account lockout policies
- Encryption settings

## Future Enhancements

### Multi-Factor Authentication
- TOTP support
- Biometric authentication
- SMS/email verification

### Advanced Security Features
- OAuth integration
- SAML support
- Advanced audit logging
- Security event monitoring

### User Experience Improvements
- Social login options
- Passwordless authentication
- Improved account recovery
- Enhanced session management

## Troubleshooting

### Common Issues
- Session expiration
- Login failures
- Registration conflicts
- Password reset issues

### Debugging Steps
1. Check browser console for errors
2. Verify network connectivity
3. Check authentication service logs
4. Validate database connectivity
5. Review configuration settings

## Best Practices

### Security Best Practices
- Always hash passwords
- Use secure session tokens
- Implement rate limiting
- Validate all inputs
- Sanitize user data
- Use HTTPS in production

### Development Best Practices
- Follow password strength guidelines
- Implement proper error handling
- Use secure coding practices
- Regular security audits
- Keep dependencies updated