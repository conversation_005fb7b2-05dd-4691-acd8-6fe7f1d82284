{"compilerOptions": {"target": "ES2022", "module": "commonjs", "lib": ["ES2022", "DOM", "DOM.Iterable"], "allowJs": true, "skipLibCheck": true, "esModuleInterop": true, "allowSyntheticDefaultImports": true, "strict": true, "forceConsistentCasingInFileNames": true, "noFallthroughCasesInSwitch": true, "moduleResolution": "node", "resolveJsonModule": true, "isolatedModules": true, "noEmit": false, "declaration": true, "outDir": "dist", "rootDir": "src", "jsx": "react-jsx", "jsxImportSource": "react", "baseUrl": ".", "paths": {"@/*": ["src/*"], "@main/*": ["src/main/*"], "@renderer/*": ["src/renderer/*"], "@shared/*": ["src/shared/*"]}, "typeRoots": ["node_modules/@types", "src/types"]}, "include": ["src/**/*"], "exclude": ["node_modules", "dist", "release", "**/*.test.ts", "**/*.test.tsx"]}